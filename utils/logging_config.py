#!/usr/bin/env python3
"""
统一日志配置模块

提供全局的日志配置和管理功能，避免在每个类中重复设置日志
"""

import logging
import os
import sys
from pathlib import Path
from logging.handlers import RotatingFileHandler
from typing import Optional


class LoggingManager:
    """日志管理器 - 单例模式"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_logging()
            LoggingManager._initialized = True
    
    def _setup_logging(self):
        """设置全局日志配置"""
        # 创建日志目录
        log_dir = Path("data/logs")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取日志级别
        log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        
        # 创建根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level, logging.INFO))
        
        # 清除现有的处理器
        root_logger.handlers.clear()
        
        # 文件处理器 - 使用轮转日志
        file_handler = RotatingFileHandler(
            log_dir / "whmcs_auto.log",
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding="utf-8"
        )
        file_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器 - 带颜色
        console_handler = logging.StreamHandler(sys.stdout)
        console_formatter = ColoredFormatter(
            "\033[36m%(asctime)s\033[0m - %(name)s - \033[1;33m%(levelname)s\033[0m - %(message)s"
        )
        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(getattr(logging, log_level, logging.INFO))
        
        # 添加处理器到根日志器
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
        
        # 设置第三方库的日志级别
        self._configure_third_party_loggers()
    
    def _configure_third_party_loggers(self):
        """配置第三方库的日志级别"""
        # 降低第三方库的日志级别，避免过多输出
        third_party_loggers = [
            "aiohttp",
            "urllib3",
            "requests",
            "asyncio",
            "selenium",
            "playwright",
        ]
        
        for logger_name in third_party_loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.WARNING)
    
    @staticmethod
    def get_logger(name: str) -> logging.Logger:
        """
        获取日志器
        
        Args:
            name: 日志器名称，通常使用 __name__
            
        Returns:
            配置好的日志器实例
        """
        # 确保日志管理器已初始化
        LoggingManager()
        return logging.getLogger(name)


class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def format(self, record):
        # 为日志级别添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


# 便捷函数
def get_logger(name: str = None) -> logging.Logger:
    """
    获取日志器的便捷函数
    
    Args:
        name: 日志器名称，如果为 None 则使用调用者的模块名
        
    Returns:
        配置好的日志器实例
    """
    if name is None:
        # 自动获取调用者的模块名
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'unknown')
    
    return LoggingManager.get_logger(name)


def setup_logging(log_level: Optional[str] = None):
    """
    设置日志配置的便捷函数
    
    Args:
        log_level: 日志级别，如果为 None 则使用环境变量或默认值
    """
    if log_level:
        os.environ["LOG_LEVEL"] = log_level.upper()
    
    # 强制重新初始化
    LoggingManager._initialized = False
    LoggingManager()


# 模块级别的日志器，供直接导入使用
logger = get_logger(__name__)


# 兼容性函数 - 用于替换现有的 setup_logging 方法
def setup_module_logging():
    """
    模块级别的日志设置函数
    用于替换各个类中的 setup_logging 方法
    """
    # 这个函数实际上什么都不做，因为日志已经在模块导入时设置好了
    pass


if __name__ == "__main__":
    # 测试日志功能
    test_logger = get_logger("test")
    
    test_logger.debug("这是一条调试信息")
    test_logger.info("这是一条信息")
    test_logger.warning("这是一条警告")
    test_logger.error("这是一条错误")
    test_logger.critical("这是一条严重错误")
