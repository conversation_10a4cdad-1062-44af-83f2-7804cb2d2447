#!/usr/bin/env python3
"""
集成监控系统

统一管理：
1. WHMCS库存监控 (from stock_monitor.py)
2. LET RSS监控 (from let/)
3. 统一的推送通知系统
"""

import asyncio
from datetime import datetime
import json
import logging
from pathlib import Path

import aiohttp
from pydantic import BaseModel, Field

from advanced_notification_service import AdvancedNotificationService
from let.scheduler import LETScheduler, LETSchedulerConfig
from notification_models import NotificationMessage, NotificationPriority, NotificationType
from whmcs.models import NotificationConfig, StockMonitorConfig
from whmcs.stock_monitor import AsyncStockMonitor


class IntegratedMonitorConfig(BaseModel):
    """集成监控配置"""

    stock_config: StockMonitorConfig | None = None
    let_config: LETSchedulerConfig | None = None
    notification_configs: dict[str, NotificationConfig] = Field(default_factory=dict)

    def load_config(self, config_path: Path):
        """加载配置文件"""
        config_path.parent.mkdir(parents=True, exist_ok=True)

        if not config_path.exists():
            self.create_default_config(config_path)

        try:
            with open(config_path, encoding="utf-8") as f:
                config_data = json.load(f)

            # 加载whmcs库存监控配置
            if "stock_monitor" in config_data:
                self.stock_config = StockMonitorConfig(**config_data["stock_monitor"])

            # 如果integrated_config中的whmcs_sites为空，尝试从独立的stock_config.json同步
            if (not self.stock_config or not self.stock_config.whmcs_sites) and Path("data/stock_config.json").exists():
                try:
                    with open("data/stock_config.json", encoding="utf-8") as f:
                        stock_data = json.load(f)
                    if stock_data.get("whmcs_sites"):
                        logging.info("🔄 从stock_config.json同步WHMCS配置到integrated_config.json")
                        # 更新或创建stock_config
                        if not self.stock_config:
                            self.stock_config = StockMonitorConfig(**stock_data)
                        else:
                            # 只同步whmcs_sites部分
                            self.stock_config.whmcs_sites = stock_data.get("whmcs_sites", {})
                        # 保存更新后的配置
                        self.save_config(config_path)
                except Exception as e:
                    logging.warning(f"同步stock_config.json配置失败: {e}")

            # 加载LET监控配置
            if "let_monitor" in config_data:
                let_data = config_data["let_monitor"]
                self.let_config = LETSchedulerConfig(
                    proxy_url=let_data.get("proxy_url"),
                    check_interval=let_data.get("check_interval", 300),
                    data_dir=let_data.get("data_dir", "data/let_posts"),
                )

            # 加载通知配置
            if "notifications" in config_data:
                for name, notif_data in config_data["notifications"].items():
                    self.notification_configs[name] = NotificationConfig(**notif_data)

        except Exception as e:
            logging.error(f"❌ 加载配置文件失败: {e}")
            raise

    def create_default_config(self, config_path: Path):
        """创建默认配置文件"""
        default_config = {
            "stock_monitor": {
                "monitor": {
                    "frequency": 30,
                    "min_in_stock_count": 2,
                    "retry_delay": 5,
                    "max_concurrent_checks": 5,
                },
                "notification": {
                    "notice_type": "telegram",
                    "telegram_token": "",
                    "chat_id": "",
                },
                "proxy": {"proxy_host": None, "use_proxy": False},
                "whmcs_sites": {},
            },
            "let_monitor": {"proxy_url": None, "check_interval": 300, "data_dir": "data/let_posts"},
            "notifications": {
                "main": {
                    "notice_type": "telegram",
                    "telegram_token": "",
                    "chat_id": "",
                    "discord_webhook_url": "",
                },
                "backup": {
                    "notice_type": "wechat",
                    "wechat_key": "",
                },
            },
        }

        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(default_config, f, indent=4, ensure_ascii=False)

        logging.info(f"📄 默认集成配置文件已创建: {config_path}")

    def save_config(self, config_path: Path):
        """保存配置文件"""
        try:
            config_dict = {
                "stock_monitor": self.stock_config.model_dump() if self.stock_config else {},
                "let_monitor": {
                    "proxy_url": self.let_config.proxy_url if self.let_config else None,
                    "check_interval": self.let_config.check_interval if self.let_config else 300,
                    "data_dir": self.let_config.data_dir if self.let_config else "data/let_posts",
                },
                "notifications": {name: config.model_dump() for name, config in self.notification_configs.items()},
            }

            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False, default=str)

        except Exception as e:
            logging.error(f"❌ 保存配置文件失败: {e}")


class IntegratedMonitor:
    """集成监控器"""

    def __init__(self, config_path: str = "data/integrated_config.json"):
        self.config = IntegratedMonitorConfig()
        self.config_path = Path(config_path)
        self.config.load_config(self.config_path)
        self.logger = logging.getLogger(__name__)

        # 监控器实例
        self.stock_monitor: AsyncStockMonitor | None = None
        self.let_scheduler: LETScheduler | None = None
        self.notification_service: AdvancedNotificationService | None = None

        # 运行状态
        self.is_running = False
        self.monitor_tasks: set[asyncio.Task] = set()

        # 会话管理
        self.session: aiohttp.ClientSession | None = None

        self.setup_logging()

    def setup_logging(self):
        """设置日志配置"""
        log_dir = Path("data/logs")
        log_dir.mkdir(parents=True, exist_ok=True)

        # 为文件和控制台定义不同的格式
        file_log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        console_log_format = "\033[36m%(asctime)s\033[0m - %(name)s - \033[1;33m%(levelname)s\033[0m - %(message)s"

        logger = logging.getLogger()

        if not logger.handlers:
            logger.setLevel(logging.INFO)

            # 创建文件handler
            file_handler = logging.FileHandler(log_dir / "integrated_monitor.log", encoding="utf-8")
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(logging.Formatter(file_log_format))

            # 创建控制台handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(logging.Formatter(console_log_format))

            logger.addHandler(file_handler)
            logger.addHandler(console_handler)

    async def __aenter__(self):
        """异步上下文管理器入口"""
        # 创建HTTP会话
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"
        }
        connector = aiohttp.TCPConnector(limit=20)
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(headers=headers, connector=connector, timeout=timeout)

        if not self.config.stock_config:
            self.config.stock_config = StockMonitorConfig.parse_file(Path("data/stock_config.json"))

        self.stock_monitor = AsyncStockMonitor(
            config_path="data/stock_config.json",
            notification_service=self.notification_service,
            config=self.config.stock_config,
        )
        # 设置反向同步回调
        self.stock_monitor.set_config_update_callback(self._on_stock_config_updated)
        await self.stock_monitor.__aenter__()

        # 初始化LET调度器
        if self.config.let_config:
            self.let_scheduler = LETScheduler(self.config.let_config)
            # 设置集成监控器实例，以便LET调度器可以调用WHMCS功能
            self.let_scheduler.set_integrated_monitor(self)

        # 初始化通知服务（如果还没有设置的话）
        if not self.notification_service:
            self.notification_service = AdvancedNotificationService()
            await self.notification_service.__aenter__()

        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        # 停止所有监控任务
        self.stop_monitoring()

        # 等待任务完成
        if self.monitor_tasks:
            await asyncio.gather(*self.monitor_tasks, return_exceptions=True)

        # 清理资源
        if self.stock_monitor:
            await self.stock_monitor.__aexit__(exc_type, exc_val, exc_tb)

        if self.notification_service:
            await self.notification_service.__aexit__(exc_type, exc_val, exc_tb)

        if self.session:
            await self.session.close()

    async def start_monitoring(self):
        """启动集成监控"""
        self.logger.info("🚀 启动集成监控系统")
        self.is_running = True

        # 发送启动通知
        notification_count = len(self.notification_service.get_channels()) if self.notification_service else 0
        await self.send_system_notification(
            "🚀 集成监控系统已启动",
            f"whmcs库存监控: {'✅' if self.stock_monitor else '❌'}\n"
            f"LET监控: {'✅' if self.let_scheduler else '❌'}\n"
            f"通知渠道: {notification_count} 个",
            "success",
        )

        # 启动whmcs库存监控
        if self.stock_monitor:
            task = asyncio.create_task(self._run_stock_monitor())
            self.monitor_tasks.add(task)
            task.add_done_callback(self.monitor_tasks.discard)

        # 启动LET监控
        if self.let_scheduler:
            task = asyncio.create_task(self._run_let_monitor())
            self.monitor_tasks.add(task)
            task.add_done_callback(self.monitor_tasks.discard)

        # 等待所有监控任务
        try:
            await asyncio.gather(*self.monitor_tasks)
        except Exception as e:
            self.logger.error(f"❌ 监控过程中发生错误: {e}")
            await self.send_error_notification("❌ 监控系统异常", f"错误信息: {e}", "integrated_monitor")

    async def _run_stock_monitor(self):
        """运行whmcs库存监控"""
        try:
            self.logger.info("📈 启动whmcs库存监控器")
            await self.stock_monitor.start_monitoring()
        except Exception as e:
            self.logger.error(f"❌ whmcs库存监控器异常: {e}")
            await self.send_error_notification("❌ whmcs库存监控异常", f"错误: {e}", "stock_monitor")

    async def _run_let_monitor(self):
        """运行LET监控"""
        try:
            self.logger.info("📰 启动LET RSS监控器")

            # 设置LET消息处理回调
            original_send = (
                self.let_scheduler._send_notification if hasattr(self.let_scheduler, "_send_notification") else None
            )

            async def let_notification_handler(message: str, title: str = "LET优惠通知"):
                await self.send_let_notification(title, message, NotificationPriority.NORMAL)

            # 如果LET调度器支持回调，设置它
            if hasattr(self.let_scheduler, "set_notification_callback"):
                self.let_scheduler.set_notification_callback(let_notification_handler)

            await self.let_scheduler.start()

        except Exception as e:
            self.logger.error(f"❌ LET监控器异常: {e}")
            await self.send_error_notification("❌ LET监控异常", f"错误: {e}", "let_monitor")

    async def send_notification(
        self,
        title: str,
        content: str,
        notification_type: NotificationType,
        priority: NotificationPriority = NotificationPriority.NORMAL,
        source: str = "integrated_monitor",
        tags: list[str] | None = None,
        extra_data: dict | None = None,
    ):
        """发送通知 - 通用方法"""
        if self.notification_service:
            message = NotificationMessage(
                title=title,
                content=content,
                notification_type=notification_type,
                priority=priority,
                source=source,
                tags=tags or [],
                extra_data=extra_data or {},
                format_markdown=True,
            )
            await self.notification_service.send_notification(message)

    async def send_system_notification(self, title: str, content: str, message_type: str = "info"):
        """发送系统状态通知 - 保持向后兼容"""
        # 映射旧的message_type到新的优先级
        priority_mapping = {
            "info": NotificationPriority.LOW,
            "success": NotificationPriority.NORMAL,
            "warning": NotificationPriority.HIGH,
            "error": NotificationPriority.CRITICAL,
        }

        # 根据message_type决定通知类型
        if message_type == "error":
            notification_type = NotificationType.ERROR_ALERT
        else:
            notification_type = NotificationType.SYSTEM_STATUS

        priority = priority_mapping.get(message_type, NotificationPriority.NORMAL)

        await self.send_notification(
            title=title,
            content=content,
            notification_type=notification_type,
            priority=priority,
            source="integrated_monitor",
        )

    async def send_let_notification(
        self, title: str, content: str, priority: NotificationPriority = NotificationPriority.NORMAL
    ):
        """发送LET监控通知"""
        await self.send_notification(
            title=title,
            content=content,
            notification_type=NotificationType.LET_MONITOR,
            priority=priority,
            source="let_monitor",
            tags=["LET", "RSS监控"],
        )

    async def send_error_notification(self, title: str, content: str, source: str = "integrated_monitor"):
        """发送错误警报通知"""
        await self.send_notification(
            title=title,
            content=content,
            notification_type=NotificationType.ERROR_ALERT,
            priority=NotificationPriority.CRITICAL,
            source=source,
            tags=["错误", "异常"],
        )

    def _on_stock_config_updated(self, updated_config: "StockMonitorConfig"):
        """当stock_monitor配置更新时的回调"""
        try:
            self.config.stock_config = updated_config
            self.logger.debug("🔄 从stock_monitor同步配置更新")
            self.config.save_config(self.config_path)
        except Exception as e:
            self.logger.warning(f"反向配置同步失败: {e}")

    def stop_monitoring(self):
        """停止监控"""
        self.logger.info("🛑 停止集成监控系统")
        self.is_running = False

        if self.stock_monitor:
            self.stock_monitor.stop_monitoring()

        if self.let_scheduler:
            self.let_scheduler.stop()

        # 取消所有任务
        for task in self.monitor_tasks:
            task.cancel()

    def get_status_summary(self) -> dict:
        """获取监控状态摘要"""
        summary = {
            "is_running": self.is_running,
            "stock_monitor_enabled": self.stock_monitor is not None,
            "let_monitor_enabled": self.let_scheduler is not None,
            "notification_channels": len(self.notification_service.get_channels()) if self.notification_service else 0,
            "last_update": datetime.now().isoformat(),
            "active_tasks": len(self.monitor_tasks),
        }

        # 合并WHMCS库存监控的详细状态
        if self.stock_monitor:
            stock_status = self.stock_monitor.get_status_summary()
            summary.update(stock_status)
        else:
            # 提供默认值以防止前端显示错误
            summary.update(
                {
                    "total_sites": 0,
                    "enabled_sites": 0,
                    "total_products": 0,
                    "enabled_products": 0,
                    "in_stock_products": 0,
                }
            )

        # 合并LET监控的状态
        if self.let_scheduler:
            let_status = self.let_scheduler.get_statistics()
            summary.update(
                {
                    "let_monitor_running": let_status.get("is_running", False),
                    "let_processed_posts": let_status.get("processed_posts", 0),
                    "let_stored_posts": let_status.get("stored_posts", 0),
                }
            )
        else:
            summary.update(
                {
                    "let_monitor_running": False,
                    "let_processed_posts": 0,
                    "let_stored_posts": 0,
                }
            )

        return summary


# 便利函数
async def main():
    """主函数"""
    import signal

    async with IntegratedMonitor() as monitor:
        # 信号处理
        def signal_handler(signum, frame):
            print("\n🛑 收到中断信号，正在停止监控...")
            monitor.stop_monitoring()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        try:
            await monitor.start_monitoring()
        except KeyboardInterrupt:
            print("🛑 监控已停止")
        except Exception as e:
            logging.error(f"❌ 监控系统异常: {e}")
        finally:
            print("👋 集成监控系统已退出")


if __name__ == "__main__":
    asyncio.run(main())
