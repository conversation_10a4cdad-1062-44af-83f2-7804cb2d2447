#!/usr/bin/env python3
"""
统一推送通知模块

支持多种推送方式：Telegram、微信、Discord、邮件等
从stock_monitor.py中独立出来，供多个模块使用
"""

import asyncio
import logging
from typing import Any
from urllib.parse import quote

import aiohttp
from pydantic import BaseModel, Field

from whmcs.models import NotificationConfig


class NotificationMessage(BaseModel):
    """通知消息模型"""

    title: str = Field(..., description="消息标题")
    content: str = Field(..., description="消息内容")
    extra_data: dict[str, Any] | None = Field(default=None, description="额外数据")
    message_type: str = Field(default="info", description="消息类型：info, success, warning, error")
    source: str = Field(default="system", description="消息来源")


class AsyncNotificationSender:
    """异步推送通知发送器"""

    def __init__(self, config: NotificationConfig, session: aiohttp.ClientSession | None = None):
        self.config = config
        self.session = session
        self._external_session = session is not None
        self.logger = logging.getLogger(__name__)

    async def __aenter__(self):
        """异步上下文管理器入口"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session and not self._external_session:
            await self.session.close()

    async def send_notification(self, message: NotificationMessage) -> bool:
        """
        发送通知消息

        Args:
            message: 通知消息对象

        Returns:
            bool: 发送是否成功
        """
        try:
            self.logger.info(f"📤 准备发送通知: {message.title}")

            # 以非阻塞方式发送
            asyncio.create_task(self._send_notification_background(message))
            return True

        except Exception as e:
            self.logger.error(f"❌ 发送通知失败: {e}")
            return False

    async def _send_notification_background(self, message: NotificationMessage):
        """在后台实际执行发送通知的操作"""
        notice_type = self.config.notice_type
        self.logger.info(f"📮 正在后台发送通知 ({notice_type})...")

        try:
            if notice_type == "telegram":
                await self._send_telegram(message)
            elif notice_type == "wechat":
                await self._send_wechat(message)
            elif notice_type == "discord":
                await self._send_discord(message)
            elif notice_type == "email":
                await self._send_email(message)
            elif notice_type == "custom":
                await self._send_custom(message)
            else:
                self.logger.error(f"❌ 未知的通知类型: {notice_type}")

        except Exception as e:
            self.logger.error(f"❌ 后台发送通知失败: {e}")

    async def _send_telegram(self, message: NotificationMessage):
        """发送Telegram通知"""
        token = self.config.telegram_token
        chat_id = self.config.chat_id

        if not token or not chat_id:
            self.logger.error("❌ Telegram配置不完整")
            return

        # 构建完整消息
        full_message = f"**{message.title}**\n\n{message.content}"

        # 添加消息类型emoji
        emoji_map = {"info": "ℹ️", "success": "✅", "warning": "⚠️", "error": "❌"}
        emoji = emoji_map.get(message.message_type, "📢")
        full_message = f"{emoji} {full_message}"

        url = f"https://api.telegram.org/bot{token}/sendMessage"
        data = {"chat_id": chat_id, "text": full_message, "parse_mode": "Markdown", "disable_web_page_preview": True}

        async with self.session.post(url, json=data) as response:
            if response.status == 200:
                self.logger.info("✅ Telegram消息发送成功")
            else:
                error_text = await response.text()
                self.logger.error(f"❌ Telegram消息发送失败: {response.status} - {error_text}")

    async def _send_wechat(self, message: NotificationMessage):
        """发送微信通知"""
        key = self.config.wechat_key
        if not key:
            self.logger.error("❌ 微信配置不完整")
            return

        url = f"https://xizhi.qqoq.net/{key}.send"
        data = {"title": message.title, "content": message.content, "type": message.message_type}

        async with self.session.get(url, params=data) as response:
            if response.status == 200:
                self.logger.info("✅ 微信消息发送成功")
            else:
                self.logger.error(f"❌ 微信消息发送失败: {response.status}")

    async def _send_discord(self, message: NotificationMessage):
        """发送Discord通知"""
        webhook_url = getattr(self.config, "discord_webhook_url", None)
        if not webhook_url:
            self.logger.error("❌ Discord配置不完整")
            return

        # Discord颜色映射
        color_map = {
            "info": 3447003,  # 蓝色
            "success": 3066993,  # 绿色
            "warning": 15105570,  # 橙色
            "error": 15158332,  # 红色
        }

        data = {
            "embeds": [
                {
                    "title": message.title,
                    "description": message.content,
                    "color": color_map.get(message.message_type, 3447003),
                    "footer": {"text": f"来源: {message.source}"},
                }
            ]
        }

        async with self.session.post(webhook_url, json=data) as response:
            if response.status == 204:
                self.logger.info("✅ Discord消息发送成功")
            else:
                self.logger.error(f"❌ Discord消息发送失败: {response.status}")

    async def _send_email(self, message: NotificationMessage):
        """发送邮件通知"""
        # 这里可以集成SMTP邮件发送功能
        self.logger.warning("📧 邮件发送功能待实现")

    async def _send_custom(self, message: NotificationMessage):
        """发送自定义通知"""
        custom_url = self.config.custom_url
        if not custom_url:
            self.logger.error("❌ 自定义通知URL未配置")
            return

        # 支持URL模板变量替换
        url = custom_url.format(
            title=quote(message.title, safe=""),
            content=quote(message.content, safe=""),
            message_type=message.message_type,
            source=message.source,
        )

        async with self.session.get(url) as response:
            if response.status == 200:
                self.logger.info("✅ 自定义通知发送成功")
            else:
                self.logger.error(f"❌ 自定义通知发送失败: {response.status}")


class NotificationBroadcaster:
    """多渠道通知广播器"""

    def __init__(self, configs: dict[str, NotificationConfig], session: aiohttp.ClientSession | None = None):
        self.configs = configs
        self.session = session
        self.senders: dict[str, AsyncNotificationSender] = {}
        self.logger = logging.getLogger(__name__)

    async def __aenter__(self):
        """异步上下文管理器入口"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)

        # 初始化所有发送器
        for name, config in self.configs.items():
            self.senders[name] = AsyncNotificationSender(config, self.session)
            await self.senders[name].__aenter__()

        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        # 清理所有发送器
        for sender in self.senders.values():
            await sender.__aexit__(exc_type, exc_val, exc_tb)

        if self.session:
            await self.session.close()

    async def broadcast(self, message: NotificationMessage, channels: list[str] | None = None):
        """
        广播消息到多个渠道

        Args:
            message: 通知消息
            channels: 指定发送渠道，None表示发送到所有渠道
        """
        if channels is None:
            channels = list(self.senders.keys())

        tasks = []
        for channel in channels:
            if channel in self.senders:
                tasks.append(self.senders[channel].send_notification(message))
            else:
                self.logger.warning(f"⚠️ 渠道不存在: {channel}")

        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for r in results if r is True)
            self.logger.info(f"📊 广播完成: {success_count}/{len(tasks)} 成功")


# 便利函数
async def send_stock_notification(config: NotificationConfig, site_id: str, message: str):
    """发送库存相关通知 - 兼容原有接口"""
    async with AsyncNotificationSender(config) as sender:
        notification = NotificationMessage(
            title="库存变更通知", content=message, message_type="info", source=f"stock_monitor_{site_id}"
        )
        await sender.send_notification(notification)


async def send_let_notification(config: NotificationConfig, message: str, title: str = "LET优惠通知"):
    """发送LET相关通知"""
    async with AsyncNotificationSender(config) as sender:
        notification = NotificationMessage(title=title, content=message, message_type="info", source="let_monitor")
        await sender.send_notification(notification)
