import asyncio
import json
import logging
import os
from pathlib import Path
import random
import re
import string
import time
from urllib.parse import urlparse

import aiohttp
from bs4 import BeautifulSoup

from utils.logging_config import get_logger


def _get_site_specific_cookie_file(base_url: str, username: str) -> Path:
    """
    根据网站URL和用户名生成特定且安全的文件路径用于存储Cookie。
    """
    # 确保 data/cookies 目录存在
    cookies_dir = Path("data") / "cookies"
    cookies_dir.mkdir(parents=True, exist_ok=True)

    # 从 base_url 提取主机名
    hostname = urlparse(base_url).hostname
    if not hostname:
        # 如果无法解析主机名，使用一个备用方案
        hostname = re.sub(r"https?://", "", base_url).split("/")[0]

    # 清理主机名和用户名，使其适用于文件名
    safe_hostname = re.sub(r"[^\w\-.]", "_", hostname)
    safe_username = re.sub(r"[^\w\-.]", "_", username)

    return cookies_dir / f"session_{safe_hostname}_{safe_username}.json"


class WHMCSAuto:
    """通用WHMCS自动化客户端"""

    def __init__(
        self,
        base_url: str,
        hostname: str,
        rootpw: str,
        cookies_file: str = Path("data") / "whmcs_cookies.json",
        cookies_string: str = None,
        use_cfbypass: bool = False,
        cfbypass_retries: int = 3,
    ):
        """
        初始化WHMCS客户端

        Args:
            base_url: WHMCS网站的基础URL，例如: https://example.com/billing
            hostname: 主机名（基础名称，会自动添加随机后缀确保唯一性）
            rootpw: root密码
            cookies_file: cookie保存文件路径
            cookies_string: 可选的cookies字符串，格式如浏览器复制的cookies
            use_cfbypass: 是否使用cfbypass绕过Cloudflare
            cfbypass_retries: cfbypass重试次数
        """
        self.base_url = base_url.rstrip("/")
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info(f"base_url: {self.base_url}")
        self.session = None
        self.headers = {
            "user-agent": (
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/113.0.1774.35"
            )
        }
        self.base_hostname = hostname
        self.rootpw = rootpw
        self.cookies_file = cookies_file
        self.cookies_string = cookies_string
        self.use_cfbypass = use_cfbypass
        self.cfbypass_retries = cfbypass_retries
        self.cfbypass_client = None

        # 如果启用cfbypass，初始化cfbypass客户端
        if self.use_cfbypass:
            from .cfbypass_client import CFBypassClient

            self.cfbypass_client = CFBypassClient(retries=cfbypass_retries)
            self.logger.info(f"已启用CFBypass支持，重试次数: {cfbypass_retries}")

    def generate_unique_hostname(self) -> str:
        """生成唯一的主机名"""
        timestamp = str(int(time.time()))[-6:]  # 取时间戳后6位
        random_suffix = "".join(random.choices(string.ascii_lowercase + string.digits, k=4))
        return f"{self.base_hostname}-{timestamp}-{random_suffix}"

    def parse_cookies_string(self, cookies_string: str):
        """
        解析cookies字符串并添加到session中

        Args:
            cookies_string: 浏览器格式的cookies字符串，如: "name1=value1; name2=value2"
        """
        if not cookies_string or not self.session:
            return

        try:
            # 解析cookies字符串
            cookies_dict = {}
            for cookie_pair in cookies_string.split(";"):
                cookie_pair = cookie_pair.strip()
                if "=" in cookie_pair:
                    name, value = cookie_pair.split("=", 1)
                    cookies_dict[name.strip()] = value.strip()

            # 添加cookies到session
            for name, value in cookies_dict.items():
                self.session.cookie_jar.update_cookies({name: value})

            self.logger.info(f"已从cookies字符串加载 {len(cookies_dict)} 个cookies")

        except Exception as e:
            self.logger.error(f"解析cookies字符串失败: {e!s}")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        # 如果启用cfbypass，先尝试获取cookies
        if self.use_cfbypass and self.cfbypass_client and not self.cookies_string:
            self.logger.info("尝试通过CFBypass获取cookies")
            try:
                cookies, user_agent = await self.cfbypass_client.get_cookies_and_user_agent(self.base_url)
                if cookies and user_agent:
                    self.logger.info("成功通过CFBypass获取cookies和user-agent")
                    # 更新headers中的user-agent
                    self.headers["user-agent"] = user_agent
                    # 创建session并设置cookies
                    self.session = aiohttp.ClientSession(headers=self.headers)
                    # 将cfbypass获取的cookies添加到session
                    formatted_cookies = self.cfbypass_client.format_cookies_for_aiohttp(cookies)
                    for name, value in formatted_cookies.items():
                        self.session.cookie_jar.update_cookies({name: value})
                    self.logger.info(f"已设置 {len(formatted_cookies)} 个CFBypass cookies")
                else:
                    self.logger.warning("CFBypass获取cookies失败，使用常规方式")
                    self.session = aiohttp.ClientSession(headers=self.headers)
            except Exception as e:
                self.logger.error(f"CFBypass初始化失败: {e}")
                self.session = aiohttp.ClientSession(headers=self.headers)
        else:
            self.session = aiohttp.ClientSession(headers=self.headers)

        # 如果提供了cookies字符串，优先使用它
        if self.cookies_string:
            self.logger.info("使用提供的cookies字符串")
            self.parse_cookies_string(self.cookies_string)
        elif not self.use_cfbypass:
            # 如果没有使用cfbypass，尝试加载已保存的cookies
            await self.load_cookies()

        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            # 保存cookies
            await self.save_cookies()
            await self.session.close()

    async def save_cookies(self):
        """保存cookies到文件"""
        try:
            if not self.session:
                return

            cookies_data = []
            for cookie in self.session.cookie_jar:
                cookie_dict = {
                    "name": cookie.key,
                    "value": cookie.value,
                    "domain": cookie["domain"],
                    "path": cookie["path"],
                }
                # 添加其他cookie属性（如果存在）
                if cookie.get("expires"):
                    cookie_dict["expires"] = cookie["expires"]
                if cookie.get("secure"):
                    cookie_dict["secure"] = cookie["secure"]
                if cookie.get("httponly"):
                    cookie_dict["httponly"] = cookie["httponly"]

                cookies_data.append(cookie_dict)

            with open(self.cookies_file, "w", encoding="utf-8") as f:
                json.dump(cookies_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"已保存 {len(cookies_data)} 个cookies到 {self.cookies_file}")

        except Exception as e:
            self.logger.error(f"保存cookies失败: {e!s}")

    async def load_cookies(self):
        """从文件加载cookies"""
        try:
            if not os.path.exists(self.cookies_file):
                self.logger.info(f"Cookie文件 {self.cookies_file} 不存在，将使用新的session")
                return

            with open(self.cookies_file, encoding="utf-8") as f:
                cookies_data = json.load(f)

            for cookie_dict in cookies_data:
                # 重建cookie对象
                self.session.cookie_jar.update_cookies({cookie_dict["name"]: cookie_dict["value"]})

            self.logger.info(f"已加载 {len(cookies_data)} 个cookies从 {self.cookies_file}")

        except Exception as e:
            self.logger.error(f"加载cookies失败: {e!s}")

    async def check_login_status(self) -> bool:
        """
        检查当前登录状态

        Returns:
            bool: 是否已登录
        """
        try:
            # 访问一个需要登录的页面来检查状态
            test_url = f"{self.base_url}/clientarea.php"
            async with self.session.get(test_url) as response:
                html_content = await response.text()

            # 检查是否包含登录页面的特征
            login_indicators = [
                "login" in html_content.lower(),
                "username" in html_content.lower() and "password" in html_content.lower(),
                "sign in" in html_content.lower(),
            ]

            # 检查是否包含已登录页面的特征
            logged_in_indicators = [
                "dashboard" in html_content.lower(),
                "client area" in html_content.lower(),
                "logout" in html_content.lower(),
                "account" in html_content.lower(),
            ]

            is_logged_in = any(logged_in_indicators) and not any(login_indicators)

            if is_logged_in:
                self.logger.info("检测到已登录状态")
            else:
                self.logger.info("检测到未登录状态")

            return is_logged_in

        except Exception as e:
            self.logger.error(f"检查登录状态时发生错误: {e!s}")
            return False

    async def login(
        self,
        username: str,
        password: str,
        force_login: bool = False,
        skip_check_login: bool = False,
    ) -> bool:
        """
        登录WHMCS系统

        Args:
            username: 用户名
            password: 密码
            force_login: 是否强制重新登录（即使已经登录）

        Returns:
            bool: 登录是否成功
        """
        # 如果不是强制登录，先检查当前登录状态
        if not force_login and not skip_check_login:
            if await self.check_login_status():
                self.logger.info("检测到有效的登录状态，跳过登录")
                return True

        login_url = f"{self.base_url}/login"
        self.logger.info(f"开始登录: {username}")

        try:
            # 获取登录页面和CSRF token
            async with self.session.get(login_url) as response:
                html_content = await response.text()

            # 尝试多种CSRF token提取方式
            csrf_token = None
            csrf_patterns = [
                r"csrfToken\s*=\s*['\"](.+?)['\"]",
                r"name=['\"]token['\"]\s+value=['\"](.+?)['\"]",
                r"_token['\"]?\s*:\s*['\"](.+?)['\"]",
                r"csrf_token['\"]?\s*:\s*['\"](.+?)['\"]",
            ]

            for pattern in csrf_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    csrf_token = matches[0]
                    break

            if not csrf_token:
                # 尝试从隐藏input字段获取token
                soup = BeautifulSoup(html_content, "html.parser")
                token_input = (
                    soup.find("input", {"name": "token"})
                    or soup.find("input", {"name": "_token"})
                    or soup.find("input", {"name": "csrf_token"})
                )
                if token_input:
                    csrf_token = token_input.get("value")

            if not csrf_token:
                self.logger.error("无法获取CSRF token")
                await self._save_debug_html(html_content, "login_fail.html")
                return False

            self.logger.info("成功获取CSRF token")

            # 准备登录数据
            login_data = {
                "token": csrf_token,
                "username": username,
                "password": password,
            }

            # 发送登录请求
            headers = {
                "content-type": "application/x-www-form-urlencoded",
                "referer": login_url,
            }

            async with self.session.post(login_url, data=login_data, headers=headers) as response:
                response_text = await response.text()

            # 检查登录结果
            title_match = re.search(r"<title>(.*?)</title>", response_text, re.S)
            title = title_match.group(1) if title_match else ""

            # 多种方式判断登录成功
            login_success_indicators = [
                "login" not in title.lower(),
                "dashboard" in title.lower(),
                "control panel" in title.lower(),
                "client area" in title.lower(),
            ]

            # 检查是否有错误信息
            error_indicators = [
                "invalid login" in response_text.lower(),
                "incorrect username" in response_text.lower(),
                "error" in title.lower(),
                "login failed" in response_text.lower(),
            ]

            if any(error_indicators):
                self.logger.error("登录失败: 用户名或密码错误")
                return False
            elif any(login_success_indicators):
                self.logger.info("登录成功")
                # 登录成功后立即保存cookies
                await self.save_cookies()
                return True
            else:
                self.logger.warning("登录状态不明确，保存页面内容用于调试")
                await self._save_debug_html(response_text, "login_response.html")
                return False

        except Exception as e:
            self.logger.error(f"登录过程中发生错误: {e!s}")
            return False

    def clear_cookies(self):
        """清除保存的cookies文件"""
        try:
            if os.path.exists(self.cookies_file):
                os.remove(self.cookies_file)
                self.logger.info(f"已清除cookies文件: {self.cookies_file}")
            else:
                self.logger.info(f"Cookies文件不存在: {self.cookies_file}")
        except Exception as e:
            self.logger.error(f"清除cookies文件失败: {e!s}")

    async def enter_product_page(self, product_url: str, billing_cycle: str = "monthly") -> tuple[dict, str] | None:
        """
        进入产品页面并获取配置数据

        Args:
            product_url: 产品页面URL
            billing_cycle: 计费周期，默认为monthly

        Returns:
            Optional[Tuple[Dict, str]]: 配置数据和页面URL的元组，失败时返回None
        """
        self.logger.info(f"进入产品页面: {product_url}")
        self.logger.info(f"使用计费周期: {billing_cycle}")

        try:
            async with self.session.get(product_url) as response:
                html_content = await response.text()
                current_url = str(response.url)

            soup = BeautifulSoup(html_content, "html.parser")

            # 获取产品ID
            product_id = None
            logging.info(f"current_url: {current_url}")
            if "i=" in current_url:
                logging.info(f"find i in current_url: {current_url}")
                product_id = current_url.split("i=")[1].split("&")[0]
                logging.info(f"product_id: {product_id}")

            if not product_id:
                self.logger.error("无法获取产品ID")
                return None

            # 获取计费周期 - 使用传入的参数而不是自动选择第一个
            billingcycle = billing_cycle
            billingcycle_select = soup.find("select", {"id": "inputBillingcycle"})
            if billingcycle_select:
                options = billingcycle_select.find_all("option")
                # 检查指定的计费周期是否可用
                available_cycles = [opt.get("value") for opt in options if opt.get("value")]
                if billing_cycle in available_cycles:
                    billingcycle = billing_cycle
                    self.logger.info(f"使用指定的计费周期: {billingcycle}")
                elif available_cycles:
                    billingcycle = available_cycles[0]
                    self.logger.warning(f"指定的计费周期 {billing_cycle} 不可用，使用第一个可用选项: {billingcycle}")
                else:
                    billingcycle = "monthly"
                    self.logger.warning(f"未找到可用的计费周期选项，使用默认值: {billingcycle}")

            # 生成唯一主机名
            hostname = self.generate_unique_hostname()
            self.logger.info(f"生成的主机名: {hostname}")

            rootpw = self.rootpw

            # 获取操作系统配置
            os_config = {}
            os_selects = soup.find_all("select", {"id": re.compile(r"inputConfigOption\d+")})
            for select in os_selects:
                name = select.get("name")
                options = select.find_all("option")
                if name and options:
                    os_config[name] = options[0].get("value", "")

            # 构建配置数据
            data = {
                "ajax": "1",
                "a": "confproduct",
                "configure": "true",
                "i": product_id,
                "billingcycle": billingcycle,
                "hostname": hostname,
                "rootpw": rootpw,
                "ns1prefix": "NA",
                "ns2prefix": "NA",
                **os_config,
            }
            logging.info(f"data: {data}")
            # 移除空值
            data = {k: v for k, v in data.items() if v}

            self.logger.info(f"成功获取产品配置数据: {len(data)}个字段")
            return data, current_url

        except Exception as e:
            self.logger.error(f"进入产品页面时发生错误: {e!s}")
            return None

    async def add_to_cart(self, config_data: dict, product_url: str) -> bool:
        """
        将产品添加到购物车

        Args:
            config_data: 产品配置数据
            product_url: 产品页面URL

        Returns:
            bool: 是否成功添加到购物车
        """
        self.logger.info("开始添加产品到购物车")

        try:
            cart_url = f"{self.base_url}/cart.php"
            logging.info(f"cart_url: {cart_url}")

            headers = {
                "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                "referer": product_url,
            }

            # 发送添加到购物车的请求
            async with self.session.post(cart_url, data=config_data, headers=headers) as response:
                await response.text()  # 确保请求完成

            # 验证是否成功添加
            verify_url = f"{self.base_url}/cart.php?a=confdomains"
            async with self.session.get(verify_url) as response:
                cart_content = await response.text()

            # 检查购物车中是否包含产品信息
            hostname = config_data.get("hostname", "")
            if hostname and hostname in cart_content:
                self.logger.info("成功添加产品到购物车")
                return True
            else:
                # 检查其他指标
                success_indicators = [
                    "item" in cart_content.lower(),
                    "product" in cart_content.lower(),
                    "checkout" in cart_content.lower(),
                ]

                if any(success_indicators):
                    self.logger.info("产品已添加到购物车")
                    return True
                else:
                    self.logger.error("添加到购物车失败")
                    await self._save_debug_html(cart_content, "cart_fail.html")
                    return False

        except Exception as e:
            self.logger.error(f"添加到购物车时发生错误: {e!s}")
            return False

    async def _read_debug_html(self, filename: str) -> str:
        """读取调试HTML内容"""
        DIR = Path("data") / "debug"
        filename = DIR / filename
        try:
            with open(filename, encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            self.logger.error(f"读取调试文件失败: {e!s}")
            return None

    async def _save_debug_html(self, content: str, filename: str):
        """保存HTML内容用于调试"""
        DIR = Path("data") / "debug"
        DIR.mkdir(parents=True, exist_ok=True)
        filename = DIR / filename
        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write(content)
            self.logger.info(f"调试HTML已保存到: {filename}")
        except Exception as e:
            self.logger.error(f"保存调试文件失败: {e!s}")

    async def add_to_cart_with_retry(self, config_data: dict, product_url: str, max_retries: int = 3) -> bool:
        """
        带重试机制的添加到购物车功能

        Args:
            config_data: 产品配置数据
            product_url: 产品页面URL
            max_retries: 最大重试次数

        Returns:
            bool: 是否成功添加到购物车
        """
        for attempt in range(max_retries):
            self.logger.info(f"尝试添加到购物车 (第{attempt + 1}次)")

            # 目前统一使用新的主机名
            if attempt >= 0:
                new_hostname = self.generate_unique_hostname()
                config_data["hostname"] = new_hostname
                self.logger.info(f"使用新的主机名: {new_hostname}")

            success = await self.add_to_cart(config_data, product_url)
            if success:
                self.logger.info("成功添加到购物车")
                return True
            else:
                try:
                    response_content = await self._read_debug_html("cart_fail.html")

                    if "hostname" in response_content.lower() and "already in use" in response_content.lower():
                        self.logger.warning("主机名冲突，将在下次尝试时使用新主机名")
                        continue
                    else:
                        self.logger.error("添加到购物车失败，非主机名冲突问题")
                        break

                except Exception as e:
                    self.logger.error(f"检查响应内容时出错: {e!s}")

        return False

    async def get_checkout_form_data(
        self, checkout_url: str, preferred_payment_method: str = "cryptomusgateway"
    ) -> dict | None:
        """
        获取结算表单数据

        Args:
            checkout_url: 结算页面URL
            preferred_payment_method: 首选支付方式，默认为cryptomusgateway

        Returns:
            Optional[Dict]: 表单数据字典，失败时返回None
        """
        try:
            async with self.session.get(checkout_url) as response:
                html_content = await response.text()

            soup = BeautifulSoup(html_content, "html.parser")

            # 获取CSRF token
            csrf_token = None
            token_input = soup.find("input", {"name": "token"})
            if token_input:
                csrf_token = token_input.get("value")

            if not csrf_token:
                self.logger.error("无法获取CSRF token")
                return None

            # 查找支付方式
            payment_methods = soup.find_all("input", {"name": "paymentmethod"})
            selected_payment_method = None
            available_methods = []

            for method in payment_methods:
                method_value = method.get("value")
                if method_value:
                    available_methods.append(method_value)

                    # 优先选择用户指定的支付方式
                    if method_value == preferred_payment_method:
                        selected_payment_method = method_value
                        self.logger.info(f"✅ 找到首选支付方式: {preferred_payment_method}")
                        break

            # 如果没找到首选支付方式，选择第一个可用的
            if not selected_payment_method and available_methods:
                selected_payment_method = available_methods[0]
                self.logger.warning(f"⚠️ 未找到首选支付方式 {preferred_payment_method}，使用: {selected_payment_method}")

            self.logger.info(f"可用支付方式: {available_methods}")

            if not selected_payment_method:
                self.logger.error("❌ 未找到任何可用的支付方式")
                return None

            # 构建基础表单数据
            form_data = {
                "token": csrf_token,
                "checkout": "true",
                "custtype": "existing",  # 现有客户
                "paymentmethod": selected_payment_method,
            }

            # 检查是否需要同意条款
            terms_checkbox = soup.find("input", {"name": "accepttos"})
            if terms_checkbox:
                form_data["accepttos"] = "on"
                self.logger.info("✅ 已同意服务条款")

            # 检查是否有自定义字段需要填写
            custom_fields = soup.find_all("select", {"name": re.compile(r"customfield\[\d+\]")})
            for field in custom_fields:
                field_name = field.get("name")
                options = field.find_all("option")
                if field_name and options:
                    # 选择第一个选项（通常是默认值）
                    first_option = options[0]
                    form_data[field_name] = first_option.get("value", "")
                    self.logger.info(f"设置自定义字段 {field_name}: {first_option.get('value', '')}")

            self.logger.info(f"✅ 获取到结算表单数据: {form_data}")
            return form_data

        except Exception as e:
            self.logger.error(f"❌ 获取结算表单数据时发生错误: {e!s}")
            return None

    async def submit_order(self, form_data: dict) -> bool:
        """
        提交订单

        Args:
            form_data: 表单数据

        Returns:
            bool: 是否成功提交订单
        """
        try:
            checkout_url = f"{self.base_url}/cart.php?a=checkout"

            headers = {
                "content-type": "application/x-www-form-urlencoded",
                "referer": checkout_url,
            }

            self.logger.info(f"🚀 开始提交订单，支付方式: {form_data.get('paymentmethod', '未知')}")

            # 提交订单
            async with self.session.post(checkout_url, data=form_data, headers=headers) as response:
                response_text = await response.text()

                current_url = str(response.url)
                self.logger.info(f"📍 订单提交后的URL: {current_url}")

            # 检查订单是否成功提交
            # 对于Cryptomus等支付方式，可能会生成账单而不是直接完成订单
            success_indicators = [
                "order complete" in response_text.lower(),
                "thank you" in response_text.lower(),
                "order confirmation" in response_text.lower(),
                "invoice" in response_text.lower(),
                "payment" in current_url.lower(),
                "pending" in response_text.lower(),
                "bill" in response_text.lower(),
                "checkout" in current_url.lower() and "success" in current_url.lower(),
            ]

            # 检查是否重定向到了支付页面（这也算成功）
            payment_redirect_indicators = [
                "cryptomus" in current_url.lower(),
                "paypal" in current_url.lower(),
                "payment" in current_url.lower(),
                "invoice" in current_url.lower(),
            ]

            error_indicators = [
                "error" in response_text.lower() and "payment" not in response_text.lower(),
                "invalid" in response_text.lower(),
                "failed" in response_text.lower(),
                "please try again" in response_text.lower(),
            ]

            # 检查页面内容以确定状态
            if any(error_indicators):
                self.logger.error("❌ 订单提交失败，检测到错误信息")
                await self._save_debug_html(response_text, "order_submission_error.html")
                return False
            elif any(success_indicators) or any(payment_redirect_indicators):
                if form_data.get("paymentmethod") == "cryptomusgateway":
                    self.logger.info("🎉 订单提交成功！已生成账单，可以选择支付方式")
                    self.logger.info("💡 选择了Cryptomus支付方式，将生成支付账单")
                else:
                    self.logger.info("🎉 订单提交成功！")
                return True
            else:
                # 检查购物车数量变化来判断是否成功
                cart_badge = re.search(r'<span id="cartItemCount"[^>]*>(\d+)</span>', response_text)
                if cart_badge:
                    cart_count = int(cart_badge.group(1))
                    if cart_count == 0:
                        self.logger.info("🎉 订单提交成功！购物车已清空")
                        return True

                self.logger.warning("⚠️ 订单提交状态不明确，请手动检查")
                self.logger.info("💡 请查看 order_submission_response.html 了解详细信息")
                # 保存响应内容用于调试
                await self._save_debug_html(response_text, "order_submission_unclear.html")

                # 询问用户是否要继续（在实际使用中，这里可能需要人工确认）
                return True  # 保守地认为提交成功，让用户自行确认

        except Exception as e:
            self.logger.error(f"❌ 提交订单时发生错误: {e!s}")
            return False

    async def apply_promo_code(self, promo_code: str) -> bool:
        """
        应用优惠码

        Args:
            promo_code: 优惠码

        Returns:
            bool: 是否成功应用优惠码
        """

        async def has_promo_code_applied(response_text: str) -> bool:
            success_indicators = [
                "remove promotion code" in response_text.lower(),
                "successfully applied" in response_text.lower(),
                "promotion code applied" in response_text.lower(),
                "优惠码已应用" in response_text.lower(),
                "discount applied" in response_text.lower(),
                '"success":true' in response_text.lower(),
                '"status":"success"' in response_text.lower(),
            ]
            return any(success_indicators)

        async def has_promo_code_error(response_text: str) -> bool:
            error_indicators = [
                "the promotion code entered does not exist" in response_text.lower(),
                "invalid" in response_text.lower(),
                "expired" in response_text.lower(),
            ]
            return any(error_indicators)

        async def check_success_indicators(response_text: str) -> bool:
            if await has_promo_code_applied(response_text):
                self.logger.info(f"✅ 优惠码 {promo_code} 应用成功")
                return True
            elif await has_promo_code_error(response_text):
                self.logger.warning(f"⚠️ 优惠码 {promo_code} 应用失败: 可能已过期或无效")
                await self._save_debug_html(html_content, "promo_code_error.html")
                return False
            else:
                # 无法确定状态，检查购物车价格变化
                self.logger.info("🔍 检查购物车价格变化来确认优惠码状态")

                # 重新获取购物车页面检查价格
                async with self.session.get(cart_url) as response:
                    updated_cart_content = await response.text()

                # 查找折扣信息
                discount_indicators = [
                    "discount" in updated_cart_content.lower(),
                    "promotion" in updated_cart_content.lower(),
                    promo_code.lower() in updated_cart_content.lower(),
                    "优惠" in updated_cart_content.lower(),
                    "折扣" in updated_cart_content.lower(),
                ]

                if any(discount_indicators):
                    self.logger.info(f"✅ 在购物车中检测到优惠信息，优惠码 {promo_code} 应用成功")
                    return True
                else:
                    self.logger.warning(f"⚠️ 优惠码 {promo_code} 应用状态不明确")
                    return False

        try:
            self.logger.info(f"🎫 开始应用优惠码: {promo_code}")

            # 首先访问购物车页面获取当前状态
            cart_url = f"{self.base_url}/cart.php?a=view"
            async with self.session.get(cart_url) as response:
                html_content = await response.text()

            soup = BeautifulSoup(html_content, "html.parser")

            # 查找优惠码表单
            promo_form = soup.find("form", {"id": "frmPromoCode"}) or soup.find("form", {"name": "promocode"})
            # 如果优惠码表单不存在，则检查是否已应用优惠码
            if not promo_form and not await has_promo_code_applied(html_content):
                # 尝试查找优惠码输入框
                promo_input = soup.find("input", {"name": "promocode"}) or soup.find("input", {"id": "promocode"})
                if not promo_input:
                    self.logger.warning("⚠️ 未找到优惠码输入表单")
                    await self._save_debug_html(html_content, "promo_code_not_found.html")
                    return False

            # 获取CSRF token
            csrf_token = None
            token_input = soup.find("input", {"name": "token"})
            if token_input:
                csrf_token = token_input.get("value")

            if not csrf_token:
                # 尝试从JavaScript中获取token
                csrf_matches = re.findall(r"csrfToken\s*=\s*['\"](.+?)['\"]", html_content, re.IGNORECASE)
                if csrf_matches:
                    csrf_token = csrf_matches[0]

            if not csrf_token:
                self.logger.warning("⚠️ 无法获取CSRF token，尝试不使用token")
            if not await has_promo_code_applied(html_content):
                # 准备优惠码应用数据
                promo_data = {
                    "promocode": promo_code,
                    "ajax": "1",
                    "validatepromo": "Validate Code",
                }

                if csrf_token:
                    promo_data["token"] = csrf_token

                # 发送优惠码应用请求
                headers = {
                    "content-type": "application/x-www-form-urlencoded",
                    "referer": cart_url,
                    "x-requested-with": "XMLHttpRequest",
                }

                async with self.session.post(cart_url, data=promo_data, headers=headers) as response:
                    response_text = await response.text()

                self.logger.debug(f"优惠码应用响应: {response_text}")

                return await check_success_indicators(response_text)
            else:
                self.logger.info(f"✅ 优惠码 {promo_code} 已应用")
                return True
        except Exception as e:
            self.logger.error(f"❌ 应用优惠码时发生错误: {e!s}")
            return False

    async def complete_order_process(
        self,
        product_url: str,
        payment_method: str = "cryptomusgateway",
        promo_code: str | None = None,
        billing_cycle: str = "monthly",
    ) -> bool:
        """
        完成完整的下单流程

        Args:
            product_url: 产品页面URL
            payment_method: 支付方式，默认为cryptomusgateway
            promo_code: 优惠码，可选
            billing_cycle: 计费周期，默认为monthly

        Returns:
            bool: 是否成功完成整个下单流程
        """
        try:
            self.logger.info("🚀 开始完整的下单流程")
            self.logger.info(f"💳 将使用支付方式: {payment_method}")
            self.logger.info(f"📅 将使用计费周期: {billing_cycle}")
            if promo_code:
                self.logger.info(f"🎫 将使用优惠码: {promo_code}")

            # 1. 进入产品页面并获取配置
            self.logger.info("📄 步骤1: 获取产品配置")
            config_result = await self.enter_product_page(product_url, billing_cycle)
            if not config_result:
                self.logger.error("❌ 获取产品配置失败")
                return False

            config_data, page_url = config_result
            self.logger.info("✅ 产品配置获取成功")

            # 2. 添加到购物车（带重试机制）
            self.logger.info("🛒 步骤2: 添加产品到购物车")
            if not await self.add_to_cart_with_retry(config_data, page_url):
                self.logger.error("❌ 添加到购物车失败")
                return False

            self.logger.info("✅ 产品已成功添加到购物车")

            # 2.5. 应用优惠码（如果有）
            if promo_code:
                self.logger.info("🎫 步骤2.5: 应用优惠码")
                if not await self.apply_promo_code(promo_code):
                    self.logger.warning("⚠️ 优惠码应用失败，继续下单流程")
                else:
                    self.logger.info("✅ 优惠码已成功应用")

            # 3. 获取结算表单数据
            self.logger.info("📋 步骤3: 准备结算信息")
            checkout_url = f"{self.base_url}/cart.php?a=checkout"
            form_data = await self.get_checkout_form_data(checkout_url, payment_method)
            if not form_data:
                self.logger.error("❌ 获取结算表单数据失败")
                return False

            self.logger.info("✅ 结算表单数据准备完成")

            # 4. 提交订单
            self.logger.info("💳 步骤4: 提交订单")
            if not await self.submit_order(form_data):
                self.logger.error("❌ 订单提交失败")
                return False

            if payment_method == "cryptomusgateway":
                self.logger.info("🎉 订单流程完成！")
                self.logger.info("📋 已选择Cryptomus支付方式，系统将生成支付账单")
                self.logger.info("💡 您可以登录WHMCS查看账单并选择具体的支付方式")
            else:
                self.logger.info("🎉 订单提交成功！完整的下单流程已完成")

            return True

        except Exception as e:
            self.logger.error(f"❌ 完整下单流程中发生错误: {e!s}")
            return False


# 仅登录，以保存cookie(检测到有货时方便快速下单，而不需要等待登录)
async def login_only(
    base_url: str,
    username: str,
    password: str,
    cookies_file: str | None = None,
    cookies_string: str = None,
    use_cfbypass: bool = False,
    cfbypass_retries: int = 3,
) -> bool:
    # 设置默认cookies文件路径
    if cookies_file is None:
        cookies_file = _get_site_specific_cookie_file(base_url, username)

    async with WHMCSAuto(base_url, "", "", cookies_file, cookies_string, use_cfbypass, cfbypass_retries) as client:
        # 如果提供了cookies字符串，直接检查登录状态
        if cookies_string:
            return await client.check_login_status()
        else:
            return await client.login(username, password)


# 全自动下单
async def auto_order(
    base_url: str,
    username: str,
    password: str,
    hostname_base: str,
    rootpw: str,
    product_url: str,
    payment_method: str = "cryptomusgateway",
    promo_code: str | None = None,
    billing_cycle: str = "monthly",
    cookies_file: str | None = None,
    cookies_string: str | None = None,
    force_login: bool = False,
    use_cfbypass: bool = False,
    cfbypass_retries: int = 3,
) -> dict:
    """
    全自动下单函数

    Args:
        base_url: WHMCS网站的基础URL
        username: 用户名
        password: 密码
        hostname_base: 基础主机名
        rootpw: root密码
        product_url: 产品页面URL
        payment_method: 支付方式，默认为cryptomusgateway
        promo_code: 优惠码，可选
        billing_cycle: 计费周期，默认为monthly
        cookies_file: cookies文件路径，默认为None（使用默认路径）
        cookies_string: 可选的cookies字符串，格式如浏览器复制的cookies
        force_login: 是否强制重新登录
        use_cfbypass: 是否使用cfbypass绕过Cloudflare
        cfbypass_retries: cfbypass重试次数

    Returns:
        dict: 包含成功状态和详细信息的字典
        {
            "success": bool,
            "message": str,
            "details": dict,
            "files": list
        }
    """
    # 设置默认cookies文件路径
    if cookies_file is None:
        cookies_file = _get_site_specific_cookie_file(base_url, username)

    # 初始化结果
    result = {"success": False, "message": "", "details": {}, "files": []}

    # 获取logger
    logger = logging.getLogger("auto_order")
    logger.info("🚀 开始全自动下单流程")
    logger.info("📋 配置信息:")
    logger.info(f"   - 网站: {base_url}")
    logger.info(f"   - 用户: {username}")
    logger.info(f"   - 主机名前缀: {hostname_base}")
    logger.info(f"   - 支付方式: {payment_method}")
    logger.info(f"   - 计费周期: {billing_cycle}")
    logger.info(f"   - 产品链接: {product_url}")
    if promo_code:
        logger.info(f"   - 优惠码: {promo_code}")
    if cookies_string:
        logger.info("   - 使用cookies字符串进行认证")

    try:
        async with WHMCSAuto(
            base_url, hostname_base, rootpw, cookies_file, cookies_string, use_cfbypass, cfbypass_retries
        ) as client:
            # 1. 登录或验证cookies
            if cookies_string:
                logger.info("🔐 步骤1: 使用cookies字符串验证登录状态")
                login_success = await client.check_login_status()
                if not login_success:
                    result["message"] = "cookies验证失败，可能已过期"
                    result["details"]["step"] = "cookie_validation"
                    result["details"]["error"] = "提供的cookies无效或已过期"
                    logger.error("❌ cookies验证失败")
                    return result
                logger.info("✅ cookies验证成功")
            else:
                logger.info("🔐 步骤1: 尝试登录")
                login_success = await client.login(username, password, force_login)
                if not login_success:
                    result["message"] = "登录失败"
                    result["details"]["step"] = "login"
                    result["details"]["error"] = "用户名或密码错误，或网络连接问题"
                    logger.error("❌ 登录失败")
                    return result
                logger.info("✅ 登录成功")

            result["details"]["login"] = "success"

            # 2. 完成下单流程
            logger.info("🛒 步骤2: 开始下单流程")
            order_success = await client.complete_order_process(product_url, payment_method, promo_code, billing_cycle)

            if not order_success:
                result["message"] = "下单流程失败"
                result["details"]["step"] = "order"
                result["details"]["error"] = "下单过程中发生错误，请查看日志和调试文件"
                logger.error("❌ 下单流程失败")
                return result

            # 3. 成功完成
            logger.info("🎉 全自动下单流程成功完成")
            result["success"] = True
            result["details"]["step"] = "completed"
            result["details"]["payment_method"] = payment_method
            result["details"]["billing_cycle"] = billing_cycle
            if promo_code:
                result["details"]["promo_code"] = promo_code

            # 根据支付方式设置不同的消息
            if payment_method == "cryptomusgateway":
                result["message"] = "订单已成功提交！已生成账单，请登录WHMCS选择具体支付方式"
                result["details"]["next_steps"] = [
                    "登录您的WHMCS账户",
                    "查看「我的账单」或「My Invoices」",
                    "找到新生成的账单",
                    "选择您喜欢的具体支付方式完成付款",
                    "支付完成后服务将自动开通",
                ]
            else:
                result["message"] = "订单已成功提交！请根据支付方式完成后续付款"
                result["details"]["next_steps"] = [
                    "根据选择的支付方式完成付款",
                    "登录WHMCS查看订单状态",
                    "等待服务开通",
                ]

            # 4. 记录生成的文件
            result["files"] = [
                "product_page.html",
                "cart_response.html",
                "checkout_form.html",
                "order_submission_response.html",
                "whmcs_auto.log",
                cookies_file,
            ]

            logger.info(f"✅ 全自动下单完成，支付方式: {payment_method}，计费周期: {billing_cycle}")
            if promo_code:
                logger.info(f"🎫 已使用优惠码: {promo_code}")
            return result

    except Exception as e:
        logger.error(f"❌ 全自动下单过程中发生异常: {e!s}")
        result["message"] = f"执行过程中发生异常: {e!s}"
        result["details"]["step"] = "exception"
        result["details"]["error"] = str(e)
        return result


# 批量全自动下单
async def batch_auto_order(
    base_url: str,
    username: str,
    password: str,
    hostname_base: str,
    rootpw: str,
    product_urls: list,
    payment_method: str = "cryptomusgateway",
    cookies_file: str | None = None,
    cookies_string: str = None,
    delay_between_orders: int = 5,
    use_cfbypass: bool = False,
    cfbypass_retries: int = 3,
) -> list:
    """
    批量全自动下单函数

    Args:
        base_url: WHMCS网站的基础URL
        username: 用户名
        password: 密码
        hostname_base: 基础主机名
        rootpw: root密码
        product_urls: 产品页面URL列表
        payment_method: 支付方式，默认为cryptomusgateway
        cookies_file: cookies文件路径，默认为None（使用默认路径）
        cookies_string: 可选的cookies字符串，格式如浏览器复制的cookies
        delay_between_orders: 订单间隔时间（秒），默认5秒
        use_cfbypass: 是否使用cfbypass绕过Cloudflare
        cfbypass_retries: cfbypass重试次数

    Returns:
        list: 每个订单的结果列表
    """
    logger = logging.getLogger("batch_auto_order")
    logger.info(f"🚀 开始批量全自动下单，共 {len(product_urls)} 个产品")

    results = []

    for i, product_url in enumerate(product_urls, 1):
        logger.info(f"📦 开始处理第 {i}/{len(product_urls)} 个产品")

        # 为每个产品使用不同的主机名后缀
        current_hostname = f"{hostname_base}{i}"

        # 执行单个订单
        result = await auto_order(
            base_url=base_url,
            username=username,
            password=password,
            hostname_base=current_hostname,
            rootpw=rootpw,
            product_url=product_url,
            payment_method=payment_method,
            cookies_file=cookies_file,
            cookies_string=cookies_string,
            force_login=(i == 1 and not cookies_string),  # 使用cookies时不需要强制登录
            use_cfbypass=use_cfbypass,
            cfbypass_retries=cfbypass_retries,
        )

        result["product_index"] = i
        result["product_url"] = product_url
        results.append(result)

        if result["success"]:
            logger.info(f"✅ 第 {i} 个产品下单成功")
        else:
            logger.error(f"❌ 第 {i} 个产品下单失败: {result['message']}")

        # 如果不是最后一个订单，添加延迟
        if i < len(product_urls):
            logger.info(f"⏳ 等待 {delay_between_orders} 秒后处理下一个订单...")
            await asyncio.sleep(delay_between_orders)

    # 统计结果
    success_count = sum(1 for r in results if r["success"])
    logger.info(f"🎯 批量下单完成: {success_count}/{len(product_urls)} 个成功")

    return results


# 同步版本的全自动下单函数（便于在非异步环境中使用）
def sync_auto_order(
    base_url: str,
    username: str,
    password: str,
    hostname_base: str,
    rootpw: str,
    product_url: str,
    payment_method: str = "cryptomusgateway",
    promo_code: str | None = None,
    billing_cycle: str = "monthly",
    cookies_file: str | None = None,
    cookies_string: str = None,
    force_login: bool = False,
    use_cfbypass: bool = False,
    cfbypass_retries: int = 3,
) -> dict:
    """
    同步版本的全自动下单函数

    参数和返回值与 auto_order 相同
    """
    return asyncio.run(
        auto_order(
            base_url=base_url,
            username=username,
            password=password,
            hostname_base=hostname_base,
            rootpw=rootpw,
            product_url=product_url,
            payment_method=payment_method,
            promo_code=promo_code,
            billing_cycle=billing_cycle,
            cookies_file=cookies_file,
            cookies_string=cookies_string,
            force_login=force_login,
            use_cfbypass=use_cfbypass,
            cfbypass_retries=cfbypass_retries,
        )
    )


def sync_batch_auto_order(
    base_url: str,
    username: str,
    password: str,
    hostname_base: str,
    rootpw: str,
    product_urls: list,
    payment_method: str = "cryptomusgateway",
    cookies_file: str | None = None,
    cookies_string: str = None,
    delay_between_orders: int = 5,
    use_cfbypass: bool = False,
    cfbypass_retries: int = 3,
) -> list:
    """
    同步版本的批量全自动下单函数

    参数和返回值与 batch_auto_order 相同
    """
    return asyncio.run(
        batch_auto_order(
            base_url=base_url,
            username=username,
            password=password,
            hostname_base=hostname_base,
            rootpw=rootpw,
            product_urls=product_urls,
            payment_method=payment_method,
            cookies_file=cookies_file,
            cookies_string=cookies_string,
            delay_between_orders=delay_between_orders,
            use_cfbypass=use_cfbypass,
            cfbypass_retries=cfbypass_retries,
        )
    )


async def main():
    """主函数示例"""
    # 配置参数
    base_url = "https://xxx.com/"  # 替换为实际的WHMCS网站URL
    username = "<EMAIL>"  # 替换为实际用户名
    password = "xxx"  # 替换为实际密码
    hostname_base = "catcat"  # 基础主机名，系统会自动添加随机后缀
    rootpw = password

    # 支付方式配置
    payment_method = "cryptomusgateway"  # 默认使用Cryptomus生成账单

    # cookies文件会根据网站和用户名自动生成，无需手动指定
    # cookies_file = "whmcs_session.json"

    if not username or not password:
        print("请先设置用户名和密码")
        return

    async with WHMCSAuto(base_url, hostname_base, rootpw) as client:
        print("🔐 正在检查登录状态...")

        # 尝试登录（如果已经有有效的session，会自动跳过）
        if await client.login(username, password):
            print("✅ 登录成功或使用了已保存的session")

            # 获取产品链接
            product_url = input("请输入需要下单的产品链接: ")
            if not product_url:
                print("❌ 产品链接不能为空")
                return

            # 询问支付方式
            print("\n💳 选择支付方式:")
            print("1. Cryptomus (生成账单，推荐)")
            print("2. Stripe (信用卡)")
            print("3. PayPal")
            print("4. PayPal Basic")

            payment_choice = input("请选择支付方式 (1-4，默认为1): ").strip()
            payment_methods = {
                "1": "cryptomusgateway",
                "2": "stripe",
                "3": "paypalcheckout",
                "4": "paypal",
            }

            if payment_choice in payment_methods:
                payment_method = payment_methods[payment_choice]
            else:
                payment_method = "cryptomusgateway"  # 默认值

            payment_names = {
                "cryptomusgateway": "Cryptomus (生成账单)",
                "stripe": "Stripe (信用卡)",
                "paypalcheckout": "PayPal",
                "paypal": "PayPal Basic",
            }

            print(f"✅ 已选择支付方式: {payment_names.get(payment_method, payment_method)}")

            # 询问是否使用优惠码
            promo_code = input("请输入优惠码 (留空表示不使用): ").strip()
            if promo_code:
                print(f"✅ 将使用优惠码: {promo_code}")
            else:
                promo_code = None

            # 询问计费周期
            print("\n📅 选择计费周期:")
            print("1. 月付 (Monthly)")
            print("2. 季付 (Quarterly)")
            print("3. 半年付 (Semi-Annually)")
            print("4. 年付 (Annually)")
            print("5. 两年付 (Biennially)")
            print("6. 三年付 (Triennially)")

            billing_choice = input("请选择计费周期 (1-6，默认为1): ").strip()
            billing_cycles = {
                "1": "monthly",
                "2": "quarterly",
                "3": "semiannually",
                "4": "annually",
                "5": "biennially",
                "6": "triennially",
            }

            if billing_choice in billing_cycles:
                billing_cycle = billing_cycles[billing_choice]
            else:
                billing_cycle = "monthly"  # 默认值

            billing_names = {
                "monthly": "月付 (Monthly)",
                "quarterly": "季付 (Quarterly)",
                "semiannually": "半年付 (Semi-Annually)",
                "annually": "年付 (Annually)",
                "biennially": "两年付 (Biennially)",
                "triennially": "三年付 (Triennially)",
            }

            print(f"✅ 已选择计费周期: {billing_names.get(billing_cycle, billing_cycle)}")

            print("\n" + "=" * 60)
            print("🚀 开始自动下单流程")
            print("=" * 60)

            # 执行完整的下单流程
            success = await client.complete_order_process(product_url, payment_method, promo_code, billing_cycle)

            print("\n" + "=" * 60)
            if success:
                print("🎉 恭喜！自动下单流程已成功完成！")
                print("\n📋 请检查以下文件获取详细信息：")
                print("   - product_page.html: 产品页面内容")
                print("   - cart_response.html: 购物车响应")
                print("   - checkout_form.html: 结算表单")
                print("   - order_submission_response.html: 订单提交响应")

                if payment_method == "cryptomusgateway":
                    print("\n💡 使用Cryptomus支付方式的后续步骤：")
                    print("   1. 登录您的WHMCS账户")
                    print("   2. 查看「我的账单」或「My Invoices」")
                    print("   3. 找到新生成的账单")
                    print("   4. 选择您喜欢的具体支付方式完成付款")
                    print("   5. 支付完成后服务将自动开通")
                else:
                    print("💡 建议登录WHMCS查看订单状态和后续步骤")

            else:
                print("❌ 自动下单流程失败")
                print("🔍 请检查以下调试文件：")
                print("   - whmcs_auto.log: 详细日志")
                print("   - *.html: 各步骤的页面响应")
                print("💪 可以尝试重新运行或手动完成剩余步骤")
            print("=" * 60)

        else:
            print("❌ 登录失败")
            # 可以选择清除可能损坏的cookies
            response = input("是否清除保存的cookies并重试？(y/n): ")
            if response.lower() == "y":
                client.clear_cookies()
                print("已清除cookies，请重新运行程序")


# 使用cookies字符串的示例
async def cookies_example():
    """
    演示如何使用cookies字符串进行认证的示例

    当网站有复杂的登录验证（如验证码、2FA等）时，
    可以手动登录后从浏览器复制cookies来使用
    """
    base_url = "https://example.com/billing"
    hostname_base = "test"
    rootpw = "your_password"
    product_url = "https://example.com/billing/store/vps-hosting/vps-basic"

    # 从浏览器开发者工具中复制的cookies字符串
    cookies_string = """
    _ga=GA1.3.**********.**********; 
    __stripe_mid=7a9294e7-000e-4801-b3df-57067ac03075c128da; 
    __stripe_sid=9debfb30-407a-4a4f-81bc-6a24f1f3581b71f636; 
    WHMCSApdzZPRlXKKr=qtbpaoefhmkn5s3dg8kr94j0d9; 
    WHMCSlogin_auth_tk=L1N2V0ZHRWFpeW43VlNJZzB5clVhOGE5bUdXM0JBR20wN01meHlFdFl5MnM4MWVMQmNQcU1talRWeTQ5SFpLTnR4bU9DWDdjcnpiaVRPT09JcEVRMDBpL3A3QzZodW9QMDVrK0dXYWh0Q09vOVdJcHI1aU1Eb0FIVHRVK0tQNkRiSmtCSjVhUVdCYnJkdlM5c1VPOFArT2pWdlRIUUIrOTZMNTRraGl3QndENkx5MkJESlZPdHNEWGdyWEZDL3gzZFNVN1U5N0hBd04xQnFJRVJlbTg1RkhXNXppaHk4amUwbVk0Y1lSR29hVUQzWWdVYWxldEdPWEptdnNXTzhZSEVLUVNsMFFKMHpINk9zOHluakVOMkdmM3FVMXpDaDFPMjRCdzFXaEcvaFAvVjkyWHZnajJ0NEdkZ25WSDhUWEtEWmdwOUdWMXRKdi9pM0VvL3VBNG5QYitLZTZYbXUyNUFIa3RZNGx4Y1VmZjhIZGR5VVhrR08yY2dxcFhzZmRwVEtWRlJCUmlTSHZrVjdjcldZSHFocHgzWVpEN3JwWT0%3D; 
    _ga_HF1L4W3YQK=GS2.3.s1750399036$o1$g1$t1750400718$j60$l0$h0
    """.strip().replace("\n    ", "")  # 清理格式

    print("🍪 使用cookies字符串进行自动下单演示")

    # 使用cookies字符串进行自动下单
    result = await auto_order(
        base_url=base_url,
        username="",  # 使用cookies时用户名密码可以为空
        password="",
        hostname_base=hostname_base,
        rootpw=rootpw,
        product_url=product_url,
        cookies_string=cookies_string,  # 使用cookies字符串
        payment_method="cryptomusgateway",
    )

    if result["success"]:
        print("✅ 使用cookies字符串下单成功！")
        print(f"📋 消息: {result['message']}")
    else:
        print("❌ 使用cookies字符串下单失败")
        print(f"❌ 错误: {result['message']}")


if __name__ == "__main__":
    # 运行常规示例
    asyncio.run(main())

    # 取消注释下面的行来运行cookies示例
    # asyncio.run(cookies_example())
