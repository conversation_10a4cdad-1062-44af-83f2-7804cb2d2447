#!/usr/bin/env python3
"""
数据模型定义

包含所有用于库存监控系统的Pydantic数据模型
"""

from datetime import datetime

from pydantic import BaseModel, Field, HttpUrl, validator


# API请求/响应模型
class WHMCSConfigRequest(BaseModel):
    """WHMCS配置请求模型"""

    base_url: HttpUrl
    username: str
    password: str | None = None
    hostname_base: str
    rootpw: str | None = None
    payment_method: str = "cryptomusgateway"
    cookies_string: str | None = Field(default=None, description="可选的cookies字符串，用于直接认证")


class LETMatchCriteriaRequest(BaseModel):
    """LET匹配条件请求模型"""

    natural_language: str = Field("", description="自然语言描述需求")
    keywords: list[str] = Field(default_factory=list, description="关键词列表")
    min_price: float | None = Field(None, description="最低价格")
    max_price: float | None = Field(None, description="最高价格")
    min_cpu_cores: int | None = Field(None, description="最少CPU核心数")
    min_ram_gb: int | None = Field(None, description="最少内存GB")
    min_storage_gb: int | None = Field(None, description="最少存储GB")
    preferred_locations: list[str] = Field(default_factory=list, description="偏好地区")
    excluded_locations: list[str] = Field(default_factory=list, description="排除地区")


class ProductConfigRequest(BaseModel):
    """产品配置请求模型"""

    name: str
    product_type: str = Field(default="traditional", description="产品类型: traditional 或 let_smart")

    # 传统产品字段
    url: HttpUrl | None = Field(None, description="产品页面URL（传统产品必需）")
    alert_class: str = "alert alert-danger error-heading"
    promo_code: str | None = None
    billing_cycle: str = "monthly"

    # LET智能产品字段
    let_match_criteria: LETMatchCriteriaRequest | None = Field(None, description="LET匹配条件（LET智能产品必需）")

    # 通用字段
    enabled: bool = True
    auto_order_enabled: bool = True
    max_order_retries: int = Field(default=3, ge=0, le=10, description="最大下单重试次数")

    @validator("url")
    def validate_traditional_product_url(cls, v, values):
        """验证传统产品必需URL"""
        if values.get("product_type") == "traditional" and not v:
            raise ValueError("传统产品类型必须提供URL")
        return v

    @validator("let_match_criteria")
    def validate_let_smart_criteria(cls, v, values):
        """验证LET智能产品必需匹配条件"""
        if values.get("product_type") == "let_smart" and not v:
            raise ValueError("LET智能产品类型必须提供匹配条件")
        return v


class MonitorStatusResponse(BaseModel):
    """监控状态响应模型"""

    is_running: bool
    monitor_running: bool | None = None  # 兼容前端字段
    total_sites: int
    enabled_sites: int
    total_products: int
    enabled_products: int
    in_stock_products: int
    last_update: str
    uptime: int | None = Field(default=0, description="运行时长(秒)")


class LogEntry(BaseModel):
    """日志条目模型"""

    timestamp: str
    level: str
    message: str


# 核心配置模型
class NotificationConfig(BaseModel):
    """通知配置模型"""

    notice_type: str = Field(default="telegram", description="通知类型")
    telegram_token: str | None = Field(default=None, description="Telegram Bot Token")
    chat_id: str | None = Field(default=None, description="Telegram Chat ID")
    wechat_key: str | None = Field(default=None, description="微信推送密钥")
    discord_webhook_url: str | None = Field(default=None, description="Discord Webhook URL")
    custom_url: str | None = Field(default=None, description="自定义通知URL")


class ProxyConfig(BaseModel):
    """代理配置模型"""

    proxy_host: str | None = Field(default=None, description="代理主机地址")
    use_proxy: bool = Field(default=False, description="是否使用代理")


class LETMatchCriteriaConfig(BaseModel):
    """LET匹配条件配置模型"""

    natural_language: str = Field("", description="自然语言描述需求")
    keywords: list[str] = Field(default_factory=list, description="关键词列表")
    min_price: float | None = Field(None, description="最低价格")
    max_price: float | None = Field(None, description="最高价格")
    min_cpu_cores: int | None = Field(None, description="最少CPU核心数")
    min_ram_gb: int | None = Field(None, description="最少内存GB")
    min_storage_gb: int | None = Field(None, description="最少存储GB")
    preferred_locations: list[str] = Field(default_factory=list, description="偏好地区")
    excluded_locations: list[str] = Field(default_factory=list, description="排除地区")


class ProductConfig(BaseModel):
    """产品配置模型"""

    name: str = Field(..., description="产品名称")
    product_type: str = Field(default="traditional", description="产品类型: traditional 或 let_smart")

    # 传统产品字段
    url: HttpUrl | None = Field(None, description="产品页面URL（传统产品必需）")
    alert_class: str = Field(default="alert alert-danger error-heading", description="缺货提示的CSS类名")
    promo_code: str | None = Field(default=None, description="优惠码")
    billing_cycle: str = Field(default="monthly", description="计费周期")
    status: bool = Field(default=False, description="当前库存状态")
    last_check: datetime | None = Field(default=None, description="最后检查时间")
    in_stock_count: int = Field(default=0, description="连续有货次数")
    order_attempted: bool = Field(default=False, description="是否已尝试下单")

    # LET智能产品字段
    let_match_criteria: LETMatchCriteriaConfig | None = Field(None, description="LET匹配条件")
    last_let_match_count: int = Field(default=0, description="最后LET匹配数量")
    last_let_match_time: datetime | None = Field(default=None, description="最后LET匹配时间")
    total_let_matches: int = Field(default=0, description="总LET匹配次数")
    successful_let_orders: int = Field(default=0, description="成功的LET订单次数")

    # 通用字段
    enabled: bool = Field(default=True, description="是否启用监控")
    auto_order_enabled: bool = Field(default=True, description="是否启用自动下单")
    max_order_retries: int = Field(default=3, ge=0, le=10, description="最大下单重试次数")
    order_failure_count: int = Field(default=0, description="连续下单失败次数")
    last_order_attempt: datetime | None = Field(default=None, description="最后一次下单尝试时间")


class WHMCSConfig(BaseModel):
    """WHMCS配置模型"""

    base_url: HttpUrl = Field(..., description="WHMCS网站基础URL")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    hostname_base: str = Field(..., description="基础主机名")
    rootpw: str = Field(..., description="root密码")
    payment_method: str = Field(default="cryptomusgateway", description="支付方式")
    cookies_string: str | None = Field(default=None, description="可选的cookies字符串，用于直接认证")
    products: dict[str, ProductConfig] = Field(default_factory=dict, description="产品配置")


class MonitorConfig(BaseModel):
    """监控配置模型"""

    frequency: int = Field(default=30, ge=0, le=3600, description="检查频率（秒）")
    min_in_stock_count: int = Field(default=1, ge=0, description="连续有货次数阈值, 参数暂时弃用")
    retry_delay: int = Field(default=5, ge=1, description="重试延迟（秒）")
    max_concurrent_checks: int = Field(default=5, ge=1, le=20, description="最大并发检查数")
    auto_login: bool = Field(default=True, description="是否自动登录")


class StockMonitorConfig(BaseModel):
    """完整的监控配置模型"""

    monitor: MonitorConfig = Field(default_factory=MonitorConfig)
    notification: NotificationConfig = Field(default_factory=NotificationConfig)
    proxy: ProxyConfig = Field(default_factory=ProxyConfig)
    whmcs_sites: dict[str, WHMCSConfig] = Field(default_factory=dict, description="WHMCS网站配置")

    @validator("whmcs_sites")
    def validate_whmcs_sites(cls, v):
        if not v:
            import logging

            logging.warning("⚠️  没有配置任何WHMCS网站，请通过API或配置文件添加网站配置")
        return v


# 响应模型
class ApiResponse(BaseModel):
    """通用API响应模型"""

    success: bool
    message: str
    data: dict | None = None


class SiteResponse(BaseModel):
    """网站响应模型"""

    site_id: str
    base_url: str
    username: str
    hostname_base: str
    payment_method: str
    auto_order_enabled: bool
    product_count: int


class ProductResponse(BaseModel):
    """产品响应模型"""

    product_id: str
    name: str
    url: str
    status: bool
    in_stock_count: int
    order_attempted: bool
    enabled: bool
    auto_order_enabled: bool
    last_check: str | None
    max_order_retries: int
    order_failure_count: int
    last_order_attempt: str | None


class ConfigUpdateRequest(BaseModel):
    """配置更新请求模型"""

    config: dict


class TestLoginResponse(BaseModel):
    """测试登录响应模型"""

    success: bool
    message: str
