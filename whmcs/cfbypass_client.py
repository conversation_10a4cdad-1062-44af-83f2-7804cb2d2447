#!/usr/bin/env python3
"""
CFBypass HTTP客户端

用于通过cfbypass服务绕过Cloudflare保护的HTTP客户端
"""

import logging
import os

import aiohttp


class CFBypassClient:
    """CFBypass HTTP客户端"""

    def __init__(self, base_url: str = None, retries: int = 3):
        """
        初始化CFBypass客户端

        Args:
            base_url: cfbypass服务的基础URL，默认从环境变量获取
            retries: 重试次数
        """
        self.base_url = base_url or os.getenv("CFPASS_BASE_URL", "http://cfbypass:8000")
        self.retries = retries
        self.logger = logging.getLogger(__name__)

    async def get_cookies(self, url: str, proxy: str = None) -> dict | None:
        """
        获取网站的cookies（包括Cloudflare cookies）

        Args:
            url: 目标网站URL
            proxy: 可选的代理设置

        Returns:
            包含cookies和user_agent的字典，失败时返回None
        """
        endpoint = f"{self.base_url}/cookies"
        params = {"url": url}

        if proxy:
            params["proxy"] = proxy

        for attempt in range(self.retries):
            try:
                self.logger.info(f"获取cookies: {url} (尝试 {attempt + 1}/{self.retries})")

                async with aiohttp.ClientSession() as session:
                    async with session.get(endpoint, params=params) as response:
                        if response.status == 200:
                            result = await response.json()
                            self.logger.info(f"成功获取cookies: {url}")
                            return result
                        else:
                            error_text = await response.text()
                            self.logger.warning(f"获取cookies失败 (状态码: {response.status}): {error_text}")

            except Exception as e:
                self.logger.error(f"获取cookies时发生错误 (尝试 {attempt + 1}/{self.retries}): {e}")

                if attempt < self.retries - 1:
                    self.logger.info("等待重试...")
                    await asyncio.sleep(2**attempt)  # 指数退避

        self.logger.error(f"获取cookies最终失败: {url}")
        return None

    async def get_html(self, url: str, proxy: str = None) -> str | None:
        """
        获取网站的HTML内容

        Args:
            url: 目标网站URL
            proxy: 可选的代理设置

        Returns:
            HTML内容字符串，失败时返回None
        """
        endpoint = f"{self.base_url}/html"
        params = {"url": url}

        if proxy:
            params["proxy"] = proxy

        for attempt in range(self.retries):
            try:
                self.logger.info(f"获取HTML内容: {url} (尝试 {attempt + 1}/{self.retries})")

                async with aiohttp.ClientSession() as session:
                    async with session.get(endpoint, params=params) as response:
                        if response.status == 200:
                            html_content = await response.text()
                            self.logger.info(f"成功获取HTML内容: {url} (长度: {len(html_content)})")
                            return html_content
                        else:
                            error_text = await response.text()
                            self.logger.warning(f"获取HTML内容失败 (状态码: {response.status}): {error_text}")

            except Exception as e:
                self.logger.error(f"获取HTML内容时发生错误 (尝试 {attempt + 1}/{self.retries}): {e}")

                if attempt < self.retries - 1:
                    self.logger.info("等待重试...")
                    await asyncio.sleep(2**attempt)  # 指数退避

        self.logger.error(f"获取HTML内容最终失败: {url}")
        return None

    async def get_cookies_and_user_agent(self, url: str, proxy: str = None) -> tuple[dict | None, str | None]:
        """
        获取cookies和user agent

        Args:
            url: 目标网站URL
            proxy: 可选的代理设置

        Returns:
            (cookies字典, user_agent字符串) 的元组，失败时返回 (None, None)
        """
        result = await self.get_cookies(url, proxy)
        if result:
            cookies = result.get("cookies", {})
            user_agent = result.get("user_agent", "")
            return cookies, user_agent
        return None, None

    def format_cookies_for_aiohttp(self, cookies: dict) -> dict:
        """
        将cookies格式化为aiohttp可用的格式

        Args:
            cookies: 原始cookies字典

        Returns:
            格式化后的cookies字典
        """
        if not cookies:
            return {}

        # 如果cookies已经是正确格式，直接返回
        if isinstance(cookies, dict):
            return cookies

        # 处理其他可能的格式
        return cookies

    async def test_connection(self) -> bool:
        """
        测试与cfbypass服务的连接

        Returns:
            连接是否成功
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/health", timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        self.logger.info("CFBypass服务连接正常")
                        return True
                    else:
                        self.logger.warning(f"CFBypass服务响应异常: {response.status}")
                        return False
        except Exception as e:
            self.logger.error(f"CFBypass服务连接失败: {e}")
            return False


# 导入asyncio（在文件末尾以避免循环导入）
import asyncio
