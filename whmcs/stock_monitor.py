#!/usr/bin/env python3
"""
异步WHMCS库存监控系统

功能：
- 异步监控多个产品库存状态
- 检测到有货时自动下单
- 支持多种通知方式
- 使用pydantic进行数据验证
"""

import asyncio
from datetime import datetime
import json
import logging
from pathlib import Path
import random
import time

import aiohttp
from bs4 import BeautifulSoup

from .core import auto_order, login_only
from .models import (
    ProductConfig,
    StockMonitorConfig,
)


class AsyncStockMonitor:
    """异步库存监控器"""

    def __init__(
        self, config_path: str = "data/stock_config.json", notification_service=None, config: StockMonitorConfig = None
    ):
        self.config_path = Path(config_path)
        self.blocked_urls: set[str] = set()
        self.session: aiohttp.ClientSession | None = None
        self.config: StockMonitorConfig | None = None

        # 通知服务引用
        self._notification_service = notification_service

        # 设置日志
        self.setup_logging()
        self.logger = logging.getLogger(__name__)

        # 加载配置：优先使用传入的配置对象，否则从文件加载
        if config is not None:
            self.config = config
            self.logger.info("✅ 使用传入的配置对象")
        else:
            self.load_config()

        # 运行状态
        self.is_running = False
        self.check_tasks: set[asyncio.Task] = set()

    def setup_logging(self):
        """设置日志配置"""
        log_dir = Path("data/logs")
        log_dir.mkdir(parents=True, exist_ok=True)

        # 为文件和控制台定义不同的格式
        file_log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        console_log_format = "\033[36m%(asctime)s\033[0m - %(name)s - \033[1;33m%(levelname)s\033[0m - %(message)s"

        logger = logging.getLogger(__name__)

        # 防止重复添加handler
        if not logger.handlers:
            logger.setLevel(logging.INFO)
            logger.propagate = False  # 防止日志向上传播到root logger

            # 创建文件handler (无颜色)
            file_handler = logging.FileHandler(log_dir / "stock_monitor.log", encoding="utf-8")
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(logging.Formatter(file_log_format))

            # 创建控制台handler (有颜色)
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(logging.Formatter(console_log_format))

            logger.addHandler(file_handler)
            logger.addHandler(console_handler)

    def load_config(self):
        """加载配置文件"""
        self.config_path.parent.mkdir(parents=True, exist_ok=True)

        if not self.config_path.exists():
            self.logger.warning(f"⚠️ 配置文件不存在，创建默认配置: {self.config_path}")
            self.create_default_config()

        try:
            with open(self.config_path, encoding="utf-8") as f:
                config_data = json.load(f)

            self.config = StockMonitorConfig(**config_data)

            # 计算总产品数量
            total_products = sum(len(site.products) for site in self.config.whmcs_sites.values())
            self.logger.info(
                f"✅ 配置已加载\n   📊 监控网站数量: {len(self.config.whmcs_sites)}\n   📦 总产品数量: {total_products}"
            )

        except Exception as e:
            self.logger.error(f"❌ 加载配置文件失败: {e}")
            raise

    def create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            "monitor": {
                "frequency": 30,
                "min_in_stock_count": 2,
                "retry_delay": 5,
                "max_concurrent_checks": 5,
            },
            "notification": {
                "notice_type": "telegram",
                "telegram_token": "",
                "chat_id": "",
            },
            "proxy": {"proxy_host": None, "use_proxy": False},
            "whmcs_sites": {
                "example_site": {
                    "base_url": "https://clients.gorillaservers.com/",
                    "username": "your_username",
                    "password": "your_password",
                    "hostname_base": "auto-server",
                    "rootpw": "your_root_password",
                    "payment_method": "cryptomusgateway",
                    "cookies_string": None,
                    "use_cfbypass": False,
                    "cfbypass_retries": 3,
                    "auto_order_enabled": True,
                    "products": {
                        "example_product": {
                            "name": "示例产品",
                            "url": "https://example.com/cart.php?a=confproduct&i=123",
                            "alert_class": "alert alert-danger error-heading",
                            "promo_code": "",
                            "billing_cycle": "monthly",
                            "status": False,
                            "in_stock_count": 0,
                            "order_attempted": False,
                            "enabled": True,
                            "auto_order_enabled": True,
                            "max_order_retries": 3,
                            "order_failure_count": 0,
                            "last_order_attempt": None,
                        }
                    },
                }
            },
        }

        with open(self.config_path, "w", encoding="utf-8") as f:
            json.dump(default_config, f, indent=4, ensure_ascii=False)

        self.logger.info(f"📄 默认配置文件已创建: {self.config_path}")

    def save_config(self):
        """保存配置文件"""
        try:
            # 更新产品的时间字段
            config_dict = self.config.model_dump()
            for site_id, site in config_dict["whmcs_sites"].items():
                for product_id, product in site["products"].items():
                    if product.get("last_check"):
                        if isinstance(product["last_check"], datetime):
                            product["last_check"] = product["last_check"].isoformat()
                    if product.get("last_order_attempt"):
                        if isinstance(product["last_order_attempt"], datetime):
                            product["last_order_attempt"] = product["last_order_attempt"].isoformat()

            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False, default=str)

            self.logger.debug("💾 配置文件已保存")

            # 通知集成监控器配置已更新（如果有回调）
            if hasattr(self, "_config_update_callback") and self._config_update_callback:
                try:
                    self.logger.info(f"🔄 配置已更新: {self.config}")
                    self._config_update_callback(self.config)
                except Exception as e:
                    self.logger.warning(f"配置同步回调失败: {e}")

        except Exception as e:
            self.logger.error(f"❌ 保存配置文件失败: {e}")

    def set_config_update_callback(self, callback):
        """设置配置更新回调函数"""
        logging.info("🔄 设置配置更新回调函数")
        self._config_update_callback = callback

    def update_config(self, new_config: StockMonitorConfig):
        """更新配置对象（用于集成监控器同步配置）"""
        old_sites_count = len(self.config.whmcs_sites) if self.config else 0
        self.config = new_config
        new_sites_count = len(self.config.whmcs_sites)

        self.logger.info(f"🔄 配置已更新: {old_sites_count} -> {new_sites_count} 个WHMCS网站")

    def get_config(self) -> StockMonitorConfig:
        """获取当前配置对象"""
        return self.config

    async def __aenter__(self):
        """异步上下文管理器入口"""
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"
        }

        connector = aiohttp.TCPConnector(limit=self.config.monitor.max_concurrent_checks)
        timeout = aiohttp.ClientTimeout(total=30)

        self.session = aiohttp.ClientSession(headers=headers, connector=connector, timeout=timeout)

        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    async def check_stock(self, site_id: str, product_id: str, product: ProductConfig) -> bool | None:
        """检查单个产品的库存状态"""
        url = str(product.url)
        self.logger.debug(f"🔍 检查库存: {product.name} - {url}")

        try:
            # 检查是否需要使用代理
            use_proxy = (
                self.config.proxy.use_proxy
                and self.config.proxy.proxy_host
                and (url in self.blocked_urls or random.random() < 0.1)
            )

            if use_proxy:
                content = await self._fetch_with_proxy(site_id, url)
            else:
                content = await self._fetch_direct(site_id, url)

            if content is None:
                return None

            # 解析HTML内容
            soup = BeautifulSoup(content, "html.parser")

            # 检查宝塔防火墙拦截
            if "宝塔防火墙正在检查您的访问" in content:
                self.logger.warning(f"🛡️ 被宝塔防火墙拦截: {product.name}")
                return None

            # 检查缺货标识
            if soup.find("div", class_=product.alert_class):
                self.logger.debug(f"🔴 发现缺货标识: {product.name}")
                return False

            # 检查缺货关键词
            out_of_stock_keywords = [
                "out of stock",
                "缺货",
                "sold out",
                "no stock",
                "缺貨中",
                "暂时缺货",
            ]

            page_text = soup.get_text().lower()
            for keyword in out_of_stock_keywords:
                if keyword in page_text:
                    self.logger.debug(f"🔴 发现缺货关键词 '{keyword}': {product.name}")
                    return False

            self.logger.debug(f"🟢 商品有货: {product.name}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 检查库存时发生错误 {product.name}: {e}")
            return None

    async def _fetch_direct(self, site_id: str, url: str) -> str | None:
        """直接请求获取页面内容"""
        try:
            async with self.session.get(url) as response:
                if response.status == 403:
                    self.logger.warning(f"🚫 直接请求被拒绝: {url}")
                    self.blocked_urls.add(url)
                    return None
                elif response.status == 200:
                    content = await response.text()
                    return content
                else:
                    self.logger.error(f"❌ 请求失败: {url}, 状态码: {response.status}")
                    return None

        except Exception as e:
            self.logger.error(f"❌ 直接请求异常: {url}, 错误: {e}")
            return None

    async def _fetch_with_proxy(self, site_id: str, url: str) -> str | None:
        """使用代理请求获取页面内容"""
        if not self.config.proxy.proxy_host:
            return await self._fetch_direct(site_id, url)

        try:
            proxy_url = f"{self.config.proxy.proxy_host}/v1"
            data = {"cmd": "request.get", "url": url, "maxTimeout": 60000}

            async with self.session.post(proxy_url, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == "ok":
                        return result["solution"]["response"]
                    else:
                        self.logger.error(f"❌ 代理请求失败: {result.get('message', 'Unknown error')}")
                        return None
                else:
                    self.logger.error(f"❌ 代理服务器错误: {response.status}")
                    return None

        except Exception as e:
            self.logger.error(f"❌ 代理请求异常: {url}, 错误: {e}")
            return None

    async def send_notification(
        self, site_id: str, message: str, title: str = "WHMCS库存通知", notification_type: str = "whmcs_stock"
    ):
        """
        发送通知消息 - 使用新的高级通知服务
        """
        try:
            if hasattr(self, "_notification_service") and self._notification_service:
                from notification_models import NotificationMessage, NotificationPriority, NotificationType

                # 创建通知消息
                notification_message = NotificationMessage(
                    title=title,
                    content=message,
                    notification_type=NotificationType(notification_type),
                    priority=NotificationPriority.NORMAL,
                    source=f"stock_monitor_{site_id}",
                    format_markdown=True,
                )

                # 发送到新的通知服务
                await self._notification_service.send_notification(notification_message)
            else:
                # 降级到旧系统
                self.logger.warning("新通知服务不可用，使用旧通知系统")
                from notification import send_stock_notification

                await send_stock_notification(self.config.notification, site_id, message)
        except Exception as e:
            self.logger.error(f"❌ 发送通知失败: {e}")

    async def process_product_stock_change(
        self,
        site_id: str,
        product_id: str,
        product: ProductConfig,
        is_in_stock: bool,
        should_send_notification: bool = True,
    ):
        """处理产品库存变化"""
        if is_in_stock:
            product.in_stock_count += 1

            # 构建有货通知消息
            stock_message = (
                f"📦 **产品名称**: {product.name}\n"
                f"🟢 **库存状态**: 有货 (连续检测 {product.in_stock_count} 次)\n"
                f"💰 **计费周期**: {self._get_billing_cycle_emoji(product.billing_cycle)} {self._get_billing_cycle_text(product.billing_cycle)}\n"
                f"🎫 **优惠码**: {product.promo_code or '无'}\n"
                f"🛒 **购买链接**: {product.url}\n"
                f"⏰ **检测时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )

            if should_send_notification:
                self.logger.info(f"🟢 {product.name} 检测到有货 (第 {product.in_stock_count} 次)")
                await self.send_notification(site_id, stock_message, "🎉 库存预警", "whmcs_stock")
            # 输出各个参数，
            self.logger.info(
                f"🔄 {product.name} 参数: {product.in_stock_count} < {self.config.monitor.min_in_stock_count} and {product.auto_order_enabled}"
            )
            # 输出产品dict
            self.logger.info(f"🔄 {product.name} 产品dict: {product.model_dump()}")
            # 检查是否达到连续有货阈值且启用自动下单(实际上第二次有货不会进这个函数，因为状态未变化)
            if product.in_stock_count >= 0 and product.auto_order_enabled:
                # 检查是否还有重试机会
                self.logger.info(
                    f"🔄 {product.name} 自动下单: {product.order_failure_count} < {product.max_order_retries}"
                )
                if product.order_failure_count < product.max_order_retries:
                    self.logger.info(
                        f"🔄 {product.name} 自动下单: {product.order_attempted} or {product.order_failure_count}"
                    )
                    if not product.order_attempted or product.order_failure_count > 0:
                        await self.attempt_auto_order(site_id, product_id, product)
                    else:
                        self.logger.debug(f"✅ {product.name} 已成功下单，无需重试")
                else:
                    # 重试次数已用完，将状态设为失败
                    if product.order_failure_count >= product.max_order_retries:
                        self.logger.warning(
                            f"⚠️ {product.name} 已达到最大重试次数 ({product.max_order_retries})，"
                            f"自动下单已停止。失败次数: {product.order_failure_count}"
                        )
                        # 发送重试耗尽通知
                        failure_message = (
                            f"📦 **产品名称**: {product.name}\n"
                            f"❌ **状态**: 重试次数已耗尽\n"
                            f"🔄 **最大重试次数**: {product.max_order_retries}\n"
                            f"💥 **连续失败次数**: {product.order_failure_count}\n"
                            f"⏹️ **自动下单**: 已自动停止\n\n"
                            f"🛠️ **请手动操作**:\n"
                            f"1. 检查账户状态和余额\n"
                            f"2. 手动尝试下单\n"
                            f"3. 重新启用自动下单\n\n"
                            f"🛒 **产品链接**: {product.url}"
                        )
                        await self.send_notification(site_id, failure_message, "🚨 自动下单失败", "whmcs_order")

                        # 暂时禁用自动下单，避免持续失败
                        product.auto_order_enabled = False
                        self.logger.info(f"⏹️ {product.name} 自动下单已自动禁用，需手动重新启用")

        else:
            self.logger.info(f"🔴 {product.name} 库存状态变为缺货")
            if product.in_stock_count > 0:
                # 构建缺货通知消息
                out_of_stock_message = (
                    f"📦 **产品名称**: {product.name}\n"
                    f"🔴 **库存状态**: 缺货\n"
                    f"📊 **之前状态**: 有货 (连续 {product.in_stock_count} 次)\n"
                    f"🔍 **检查链接**: {product.url}\n"
                    f"⏰ **检测时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )

                await self.send_notification(site_id, out_of_stock_message, "📉 库存变化通知", "whmcs_stock")

            # 缺货时重置计数，但保留失败计数和下单尝试状态
            product.in_stock_count = 0
            # 不重置 order_attempted 和 order_failure_count，让重试逻辑继续生效

        # 更新状态和检查时间
        product.status = is_in_stock
        product.last_check = datetime.now()

    def _get_billing_cycle_emoji(self, cycle: str) -> str:
        """获取计费周期对应的emoji"""
        cycle_emojis = {
            "monthly": "📅",
            "quarterly": "📆",
            "semiannually": "🗓️",
            "annually": "📊",
            "biennially": "🔄",
            "triennially": "⏳",
        }
        return cycle_emojis.get(cycle, "💳")

    def _get_billing_cycle_text(self, cycle: str) -> str:
        """获取计费周期的中文描述"""
        cycle_texts = {
            "monthly": "月付",
            "quarterly": "季付",
            "semiannually": "半年付",
            "annually": "年付",
            "biennially": "双年付",
            "triennially": "三年付",
        }
        return cycle_texts.get(cycle, cycle)

    async def attempt_auto_order(self, site_id: str, product_id: str, product: ProductConfig):
        """尝试自动下单"""
        # 更新最后尝试时间
        product.last_order_attempt = datetime.now()

        retry_info = ""
        if product.order_failure_count > 0:
            retry_info = f" (重试 {product.order_failure_count + 1}/{product.max_order_retries})"

        self.logger.info(f"🚀 开始自动下单: {product.name}{retry_info}")

        try:
            # 构建下单通知消息
            if product.order_failure_count == 0:
                order_message = (
                    f"🚀 **自动下单启动** 🚀\n\n"
                    f"📦 **产品名称**: {product.name}\n"
                    f"🟢 **库存状态**: 已确认有货\n"
                    f"💰 **计费周期**: {self._get_billing_cycle_emoji(product.billing_cycle)} {self._get_billing_cycle_text(product.billing_cycle)}\n"
                    f"🎫 **优惠码**: {product.promo_code or '无'}\n"
                    f"🔄 **尝试次数**: 第 1 次\n"
                    f"⚡ **状态**: 正在处理订单...\n\n"
                    f"🛒 **产品链接**: {product.url}"
                )
            else:
                order_message = (
                    f"🔄 **自动下单重试** 🔄\n\n"
                    f"📦 **产品名称**: {product.name}\n"
                    f"🟢 **库存状态**: 已确认有货\n"
                    f"💰 **计费周期**: {self._get_billing_cycle_emoji(product.billing_cycle)} {self._get_billing_cycle_text(product.billing_cycle)}\n"
                    f"🎫 **优惠码**: {product.promo_code or '无'}\n"
                    f"🔄 **重试次数**: 第 {product.order_failure_count + 1}/{product.max_order_retries} 次\n"
                    f"⚡ **状态**: 正在重新处理订单...\n\n"
                    f"🛒 **产品链接**: {product.url}"
                )

            await self.send_notification(site_id, order_message, "🚀 自动下单启动", "whmcs_order")

            # 执行自动下单
            site = self.config.whmcs_sites[site_id]

            # 为重试使用稍微不同的主机名，避免冲突
            hostname_suffix = f"-{product_id}"
            if product.order_failure_count > 0:
                hostname_suffix += f"-r{product.order_failure_count + 1}"

            result = await auto_order(
                base_url=str(site.base_url),
                username=site.username,
                password=site.password,
                hostname_base=f"{site.hostname_base}{hostname_suffix}",
                rootpw=site.rootpw,
                product_url=str(product.url),
                payment_method=site.payment_method,
                promo_code=product.promo_code,
                billing_cycle=product.billing_cycle,
                cookies_string=site.cookies_string,
                force_login=product.order_failure_count > 0,  # 重试时强制重新登录
                use_cfbypass=getattr(site, 'use_cfbypass', False),
                cfbypass_retries=getattr(site, 'cfbypass_retries', 3),
            )

            # 处理下单结果
            if result["success"]:
                success_message = (
                    f"🎉 **自动下单成功** 🎉\n\n"
                    f"📦 **产品名称**: {product.name}\n"
                    f"✅ **订单状态**: 下单成功\n"
                    f"💰 **计费周期**: {self._get_billing_cycle_emoji(product.billing_cycle)} {self._get_billing_cycle_text(product.billing_cycle)}\n"
                    f"🎫 **优惠码**: {product.promo_code or '无'}\n"
                )

                if product.order_failure_count > 0:
                    success_message += f"🎯 **重试成功**: 第 {product.order_failure_count + 1} 次尝试成功\n"

                success_message += (
                    f"📋 **订单详情**: {result['message']}\n"
                    f"⏰ **完成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                    f"🎊 恭喜！订单已成功提交"
                )

                self.logger.info(f"✅ 自动下单成功: {product.name}")

                # 下单成功，标记完成并重置失败计数
                product.order_attempted = True
                product.order_failure_count = 0

                final_message = success_message
            else:
                # 下单失败，增加失败计数
                product.order_failure_count += 1
                remaining_retries = product.max_order_retries - product.order_failure_count

                failure_message = (
                    f"📦 **产品名称**: {product.name}\n"
                    f"💥 **失败原因**: {result['message']}\n"
                    f"🔄 **失败次数**: 第 {product.order_failure_count} 次\n"
                )

                if remaining_retries > 0:
                    failure_message += (
                        f"⏳ **剩余重试**: {remaining_retries} 次\n"
                        f"🔄 **下次重试**: 检测到下次有货时自动重试\n\n"
                        f"💡 **提示**: 系统将继续监控库存并自动重试"
                    )
                    self.logger.warning(
                        f"❌ 自动下单失败: {product.name} - {result['message']} "
                        f"(失败 {product.order_failure_count}/{product.max_order_retries})"
                    )
                else:
                    failure_message += (
                        "🚨 **重试耗尽**: 已达到最大重试次数\n"
                        "⏹️ **自动下单**: 已自动停止\n\n"
                        "🛠️ **建议操作**:\n"
                        "1. 检查账户状态和余额\n"
                        "2. 手动尝试下单测试\n"
                        "3. 联系客服或检查产品状态\n"
                        "4. 确认无误后重新启用自动下单"
                    )
                    self.logger.error(
                        f"❌ 自动下单彻底失败: {product.name} - {result['message']} "
                        f"(已重试 {product.max_order_retries} 次)"
                    )
                    # 重置失败次数
                    product.order_failure_count = 0

                final_message = failure_message

            if result["success"]:
                await self.send_notification(site_id, final_message, "🎉 自动下单成功", "whmcs_order")
            else:
                await self.send_notification(site_id, final_message, "❌ 自动下单失败", "whmcs_order")

        except Exception as e:
            # 异常情况也计入失败
            product.order_failure_count += 1
            remaining_retries = product.max_order_retries - product.order_failure_count

            error_message = (
                f"🔥 **自动下单异常** 🔥\n\n"
                f"📦 **产品名称**: {product.name}\n"
                f"💥 **异常信息**: {e!s}\n"
                f"🔄 **异常次数**: 第 {product.order_failure_count} 次\n"
            )

            if remaining_retries > 0:
                error_message += (
                    f"⏳ **剩余重试**: {remaining_retries} 次\n"
                    f"🔄 **下次重试**: 检测到下次有货时自动重试\n\n"
                    f"💡 **提示**: 系统将继续监控库存并自动重试"
                )
                self.logger.error(
                    f"🔥 自动下单异常: {product.name} - {e!s} "
                    f"(异常 {product.order_failure_count}/{product.max_order_retries})"
                )
            else:
                error_message += (
                    "🚨 **重试耗尽**: 已达到最大重试次数\n"
                    "⏹️ **自动下单**: 已自动停止\n\n"
                    "🛠️ **建议操作**:\n"
                    "1. 检查网络连接和服务状态\n"
                    "2. 查看详细日志排查问题\n"
                    "3. 手动测试下单流程\n"
                    "4. 确认无误后重新启用自动下单"
                )
                self.logger.error(
                    f"🔥 自动下单彻底异常: {product.name} - {e!s} (已重试 {product.max_order_retries} 次)"
                )

            await self.send_notification(site_id, error_message, "🔥 自动下单异常", "error_alert")

    async def check_single_product(self, site_id: str, product_id: str, product: ProductConfig):
        """检查单个产品"""
        if not product.enabled:
            return

        try:
            current_status = await self.check_stock(site_id, product_id, product)

            if current_status is not None:
                # 只有当状态发生变化时才处理
                if product.order_failure_count == product.max_order_retries:
                    product.order_failure_count = 0

                # 记录详细的状态变化
                status_emoji = "🟢" if current_status else "🔴"
                status_text = "有货" if current_status else "缺货"
                self.logger.info(f"{status_emoji} {product.name}: {status_text}")

                if current_status != product.status or (product.order_failure_count > 0 and current_status):
                    # 启用下单且失败时不发送通知，因为不是第一次
                    should_send_notification = True
                    if product.order_failure_count > 0 and product.auto_order_enabled:
                        should_send_notification = False
                    await self.process_product_stock_change(
                        site_id,
                        product_id,
                        product,
                        current_status,
                        should_send_notification=should_send_notification,
                    )
                elif current_status:  # 如果继续有货，增加计数
                    product.in_stock_count += 1
                    product.last_check = datetime.now()
                else:  # 如果继续缺货，重置计数
                    product.in_stock_count = 0
                    product.last_check = datetime.now()

        except Exception as e:
            self.logger.error(f"❌ 检查产品 {product.name} 时发生错误: {e}")

    async def run_check_cycle(self):
        """运行一轮检查"""
        # 计算总产品数量
        total_products = sum(len(site.products) for site in self.config.whmcs_sites.values())
        enabled_products = sum(
            sum(1 for product in site.products.values() if product.enabled) for site in self.config.whmcs_sites.values()
        )

        self.logger.info(
            f"🔄 开始新一轮检查\n"
            f"   🌐 网站数量: {len(self.config.whmcs_sites)}\n"
            f"   📦 总产品: {total_products}\n"
            f"   ✅ 启用产品: {enabled_products}"
        )

        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(self.config.monitor.max_concurrent_checks)

        async def check_with_semaphore(site_id: str, product_id: str, product: ProductConfig):
            if not product.url:
                self.logger.warning(f"⚠️ 产品 {product.name} 没有URL，跳过检查")
                return
            async with semaphore:
                await self.check_single_product(site_id, product_id, product)

        # 并发检查所有网站的所有产品
        tasks = [
            check_with_semaphore(site_id, product_id, product)
            for site_id, site in self.config.whmcs_sites.items()
            for product_id, product in site.products.items()
            if product.enabled
        ]

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

        # 保存配置
        self.save_config()

        self.logger.info("✅ 本轮检查完成")

    async def start_monitoring(self):
        """开始监控"""
        self.logger.info(
            f"🚀 启动异步库存监控系统\n"
            f"   ⏱️  监控频率: {self.config.monitor.frequency} 秒\n"
            f"   🔄 并发检查: {self.config.monitor.max_concurrent_checks} 个\n"
            f"   🎯 有货阈值: {self.config.monitor.min_in_stock_count} 次"
        )

        total_products = sum(len(site.products) for site in self.config.whmcs_sites.values())
        self.logger.info(
            f"📊 监控统计\n   🌐 网站数量: {len(self.config.whmcs_sites)}\n   📦 总产品数量: {total_products}"
        )

        self.is_running = True

        # 预先登录保存cookies
        if (
            any(
                product.auto_order_enabled
                for site in self.config.whmcs_sites.values()
                for product in site.products.values()
            )
            and self.config.monitor.auto_login
        ):
            self.logger.info("🔐 预先登录WHMCS保存cookies...")
            try:
                for site_id, site in self.config.whmcs_sites.items():
                    # Only login if there is at least one auto-order enabled product for this site
                    if any(p.auto_order_enabled for p in site.products.values()):
                        login_success = await login_only(
                            str(site.base_url),
                            site.username,
                            site.password,
                            cookies_string=site.cookies_string,
                            use_cfbypass=getattr(site, 'use_cfbypass', False),
                            cfbypass_retries=getattr(site, 'cfbypass_retries', 3),
                        )
                        if login_success:
                            self.logger.info(f"✅ WHMCS预登录成功: {site_id}")
                        else:
                            self.logger.warning(f"⚠️ WHMCS预登录失败，后续下单可能需要重新登录: {site_id}")
            except Exception as e:
                self.logger.error(f"❌ WHMCS预登录异常: {e}")

        # 主监控循环
        try:
            while self.is_running:
                cycle_start = time.time()

                await self.run_check_cycle()

                # 计算等待时间
                cycle_duration = time.time() - cycle_start
                wait_time = max(0, self.config.monitor.frequency - cycle_duration)

                if wait_time > 0:
                    self.logger.debug(f"⏳ 等待 {wait_time:.1f} 秒后进行下一轮检查")
                    await asyncio.sleep(wait_time)
                else:
                    self.logger.warning(f"⚠️ 检查耗时 {cycle_duration:.1f} 秒，超过设定频率")

        except KeyboardInterrupt:
            self.logger.info("🛑 收到中断信号，停止监控")
        except Exception as e:
            self.logger.error(f"❌ 监控过程中发生错误: {e}")
        finally:
            self.is_running = False

    def stop_monitoring(self):
        """停止监控"""
        self.logger.info("🛑 停止库存监控")
        self.is_running = False

    async def add_product(self, site_id: str, product_id: str, product_config: dict):
        """添加新产品监控"""
        try:
            if site_id not in self.config.whmcs_sites:
                self.logger.error(f"❌ WHMCS网站不存在: {site_id}")
                return

            product = ProductConfig(**product_config)
            self.config.whmcs_sites[site_id].products[product_id] = product
            self.save_config()
            self.logger.info(
                f"✅ 已添加产品监控\n   🌐 网站: {site_id}\n   🆔 产品ID: {product_id}\n   📦 产品名称: {product.name}"
            )
        except Exception as e:
            self.logger.error(f"❌ 添加产品失败: {e}")

    async def remove_product(self, site_id: str, product_id: str):
        """移除产品监控"""
        if site_id in self.config.whmcs_sites and product_id in self.config.whmcs_sites[site_id].products:
            product_name = self.config.whmcs_sites[site_id].products[product_id].name
            del self.config.whmcs_sites[site_id].products[product_id]
            self.save_config()
            self.logger.info(
                f"🗑️ 已移除产品监控\n   🌐 网站: {site_id}\n   🆔 产品ID: {product_id}\n   📦 产品名称: {product_name}"
            )
        else:
            self.logger.warning(f"⚠️ 产品不存在: {site_id} - {product_id}")

    def get_status_summary(self) -> dict:
        """获取监控状态摘要"""
        total_sites = len(self.config.whmcs_sites)
        enabled_sites = sum(1 for site in self.config.whmcs_sites.values())

        total_products = sum(len(site.products) for site in self.config.whmcs_sites.values())
        enabled_products = sum(
            sum(1 for product in site.products.values() if product.enabled) for site in self.config.whmcs_sites.values()
        )
        in_stock_products = sum(
            sum(1 for product in site.products.values() if product.status) for site in self.config.whmcs_sites.values()
        )

        return {
            "total_sites": total_sites,
            "enabled_sites": enabled_sites,
            "total_products": total_products,
            "enabled_products": enabled_products,
            "in_stock_products": in_stock_products,
            "is_running": self.is_running,
            "last_update": datetime.now().isoformat(),
        }


# 便于直接运行的函数
async def main():
    """主函数"""
    config_path = "data/stock_config.json"

    async with AsyncStockMonitor(config_path) as monitor:
        try:
            await monitor.start_monitoring()
        except KeyboardInterrupt:
            print("\n🛑 停止监控")


if __name__ == "__main__":
    asyncio.run(main())
