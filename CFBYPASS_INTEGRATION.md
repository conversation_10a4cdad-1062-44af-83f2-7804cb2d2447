# CFBypass 集成功能

本文档介绍了 WHMCS 自动化系统中新增的 CFBypass 集成功能，用于绕过 Cloudflare 保护。

## 功能概述

CFBypass 是一个专门用于绕过 Cloudflare 保护的服务。当 WHMCS 网站受到 Cloudflare 保护时，传统的 HTTP 请求可能会被拦截。通过集成 CFBypass，系统可以自动获取有效的 cookies 和 user-agent，从而成功访问受保护的网站。

## 主要特性

- ✅ **自动 Cookie 获取**: 通过 CFBypass 服务自动获取绕过 Cloudflare 所需的 cookies
- ✅ **User-Agent 同步**: 自动使用 CFBypass 提供的 user-agent 确保一致性
- ✅ **重试机制**: 支持配置重试次数，提高成功率
- ✅ **无缝集成**: 与现有 WHMCS 自动化流程完全兼容
- ✅ **可选启用**: 可以为每个网站单独配置是否启用 CFBypass

## 配置说明

### 环境变量

确保在 `.env` 文件中配置了 CFBypass 服务地址：

```bash
CFPASS_BASE_URL=http://cfbypass:8000
```

### WHMCS 网站配置

在添加或编辑 WHMCS 网站时，可以配置以下 CFBypass 选项：

#### 1. 启用 CFBypass
- **字段**: `use_cfbypass`
- **类型**: 布尔值
- **默认值**: `false`
- **说明**: 是否为该网站启用 CFBypass 功能

#### 2. 重试次数
- **字段**: `cfbypass_retries`
- **类型**: 整数 (1-10)
- **默认值**: `3`
- **说明**: CFBypass 请求失败时的重试次数

### 配置示例

#### API 请求示例
```json
{
  "base_url": "https://example.com",
  "username": "your_username",
  "password": "your_password",
  "hostname_base": "vps",
  "rootpw": "your_root_password",
  "payment_method": "cryptomusgateway",
  "use_cfbypass": true,
  "cfbypass_retries": 5
}
```

#### 配置文件示例
```json
{
  "whmcs_sites": {
    "example_site": {
      "base_url": "https://example.com",
      "username": "your_username",
      "password": "your_password",
      "hostname_base": "vps",
      "rootpw": "your_root_password",
      "payment_method": "cryptomusgateway",
      "use_cfbypass": true,
      "cfbypass_retries": 3,
      "products": {}
    }
  }
}
```

## 使用场景

### 何时启用 CFBypass

建议在以下情况下启用 CFBypass：

1. **网站受 Cloudflare 保护**: 当目标 WHMCS 网站使用 Cloudflare 进行保护时
2. **频繁出现验证页面**: 访问网站时经常遇到 "Checking your browser" 页面
3. **传统登录失败**: 使用用户名密码登录经常失败或被拦截
4. **需要稳定访问**: 对自动化稳定性要求较高的场景

### 何时不启用 CFBypass

以下情况建议不启用 CFBypass：

1. **网站无保护**: 目标网站没有使用 Cloudflare 保护
2. **已有有效 Cookies**: 已经通过其他方式获得了有效的 cookies
3. **网络环境稳定**: 当前网络环境可以正常访问目标网站

## 工作原理

### 1. 初始化阶段
```python
# 创建启用 CFBypass 的 WHMCS 客户端
async with WHMCSAuto(
    base_url="https://example.com",
    hostname="vps",
    rootpw="password",
    use_cfbypass=True,
    cfbypass_retries=3
) as client:
    # 自动处理 CFBypass 初始化
```

### 2. Cookie 获取流程
1. 系统检测到启用了 CFBypass
2. 向 CFBypass 服务发送请求获取 cookies
3. 同时获取推荐的 user-agent
4. 将获取的 cookies 和 user-agent 应用到 HTTP 会话
5. 继续正常的 WHMCS 操作流程

### 3. 错误处理
- 如果 CFBypass 服务不可用，自动降级到传统方式
- 支持重试机制，提高成功率
- 详细的日志记录，便于问题排查

## API 接口

### CFBypass 服务端点

CFBypass 服务提供以下端点：

#### 1. 获取 Cookies
```
GET /cookies?url=<URL>&retries=<>&proxy=<>
```

**响应示例**:
```json
{
  "cookies": {
    "cf_clearance": "SJHuYhHrTZpXDUe8iMuzEUpJxocmOW8ougQVS0.aK5g-1723665177-*******-5_NOoP19LQZw4TQ4BLwJmtrXBoX8JbKF5ZqsAOxRNOnW2rmDUwv4hQ7BztnsOfB9DQ06xR5hR_hsg3n8xteUCw"
  },
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
}
```

#### 2. 获取 HTML 内容
```
GET /html?url=<URL>&retries=<>&proxy=<>
```

## 前端界面

在网站管理界面中，CFBypass 配置位于 "Cloudflare 绕过设置" 区域：

- **启用开关**: 控制是否启用 CFBypass 功能
- **重试次数**: 配置失败重试次数 (1-10)
- **状态指示**: 在网站卡片中显示 CFBypass 启用状态

## 测试和调试

### 运行测试脚本
```bash
python test_cfbypass.py
```

### 查看日志
CFBypass 相关的日志会包含以下关键信息：
- CFBypass 服务连接状态
- Cookie 获取成功/失败
- User-agent 更新
- 重试次数和结果

### 常见问题排查

1. **CFBypass 服务连接失败**
   - 检查 `CFPASS_BASE_URL` 环境变量
   - 确认 CFBypass 容器正在运行
   - 检查网络连接

2. **Cookie 获取失败**
   - 检查目标 URL 是否正确
   - 尝试增加重试次数
   - 查看 CFBypass 服务日志

3. **登录仍然失败**
   - 确认网站确实需要 CFBypass
   - 检查获取的 cookies 是否有效
   - 尝试手动测试 CFBypass 服务

## 注意事项

1. **合规使用**: 请确保使用 CFBypass 功能符合目标网站的使用条款
2. **性能影响**: CFBypass 会增加请求延迟，建议仅在必要时启用
3. **服务依赖**: 功能依赖于 CFBypass 服务的可用性
4. **更新维护**: 定期检查 CFBypass 服务的更新和维护状态

## 更新日志

- **v1.0.0**: 初始版本，支持基本的 CFBypass 集成
- 新增 CFBypass 客户端模块
- 集成到 WHMCS 核心功能
- 添加前端配置界面
- 完善错误处理和重试机制
