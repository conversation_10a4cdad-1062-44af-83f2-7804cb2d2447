[project]
name = "integrated-monitor"
version = "0.1.0"
description = "集成库存监控系统 - 支持WHMCS和LET多平台监控"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.9.0",
    "beautifulsoup4>=4.12.0",
    "fastapi>=0.115.13",
    "logfire>=3.21.1",
    "lxml>=4.9.0",
    "playwright>=1.40.0",
    "pydantic>=2.11.7",
    "pydantic-ai>=0.3.3",
    "python-dotenv>=1.1.0",
    "python-multipart>=0.0.20",
    "requests>=2.32.0",
    "urllib3>=2.5.0",
    "uvicorn>=0.34.3",
]


[tool.ruff]
line-length = 120
target-version = "py310"

[tool.ruff.format]
line-ending = "lf"

[tool.ruff.lint]
select = [
    "F",     # Pyflakes
    "W",     # pycodestyle warnings
    "E",     # pycodestyle errors
    "I",     # isort
    "UP",    # pyupgrade
    "ASYNC", # flake8-async
    "C4",    # flake8-comprehensions
    "T10",   # flake8-debugger
    "T20",   # flake8-print
    "PYI",   # flake8-pyi
    "PT",    # flake8-pytest-style
    "Q",     # flake8-quotes
    "TID",   # flake8-tidy-imports
    "RUF",   # Ruff-specific rules
]
ignore = [
    "E402",   # module-import-not-at-top-of-file
    "UP037",  # quoted-annotation
    "RUF001", # ambiguous-unicode-character-string
    "RUF002", # ambiguous-unicode-character-docstring
    "RUF003", # ambiguous-unicode-character-comment
    "W191",   # indentation contains tabs
    "TID252", # relative import
]


[tool.ruff.lint.isort]
force-sort-within-sections = true
known-first-party = ["tests/*"]
extra-standard-library = ["typing_extensions"]

[tool.ruff.lint.flake8-pytest-style]
fixture-parentheses = false
mark-parentheses = false

[tool.ruff.lint.pyupgrade]
keep-runtime-typing = true
