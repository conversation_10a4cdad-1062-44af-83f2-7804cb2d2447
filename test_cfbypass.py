#!/usr/bin/env python3
"""
CFBypass 集成测试脚本

测试 WHMCS 自动化系统的 CFBypass 功能
"""

import asyncio
import logging
import os
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_cfbypass_client():
    """测试 CFBypass 客户端"""
    logger.info("🧪 开始测试 CFBypass 客户端")
    
    try:
        from whmcs.cfbypass_client import CFBypassClient
        
        # 创建客户端
        client = CFBypassClient()
        
        # 测试连接
        logger.info("🔗 测试 CFBypass 服务连接...")
        connection_ok = await client.test_connection()
        
        if connection_ok:
            logger.info("✅ CFBypass 服务连接正常")
        else:
            logger.error("❌ CFBypass 服务连接失败")
            return False
            
        # 测试获取 cookies (使用一个简单的测试网站)
        test_url = "https://nopecha.com/demo/cloudflare"
        logger.info(f"🍪 测试获取 cookies: {test_url}")
        
        cookies, user_agent = await client.get_cookies_and_user_agent(test_url)
        
        if cookies and user_agent:
            logger.info(f"✅ 成功获取 cookies: {len(cookies)} 个")
            logger.info(f"✅ 获取到 user-agent: {user_agent[:50]}...")
            
            # 显示部分 cookies 信息
            for name, value in list(cookies.items())[:3]:  # 只显示前3个
                logger.info(f"   🍪 {name}: {value[:20]}...")
                
            return True
        else:
            logger.error("❌ 获取 cookies 失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ CFBypass 客户端测试失败: {e}")
        return False

async def test_whmcs_with_cfbypass():
    """测试 WHMCS 客户端的 CFBypass 集成"""
    logger.info("🧪 开始测试 WHMCS CFBypass 集成")
    
    try:
        from whmcs.core import WHMCSAuto
        
        # 使用一个测试 URL (不会实际登录)
        test_url = "https://example.com"
        
        logger.info("🔧 创建启用 CFBypass 的 WHMCS 客户端...")
        
        # 测试创建客户端
        async with WHMCSAuto(
            base_url=test_url,
            hostname="test",
            rootpw="test",
            use_cfbypass=True,
            cfbypass_retries=2
        ) as client:
            logger.info("✅ WHMCS 客户端创建成功")
            logger.info(f"✅ CFBypass 已启用，重试次数: {client.cfbypass_retries}")
            
            if client.cfbypass_client:
                logger.info("✅ CFBypass 客户端已初始化")
                return True
            else:
                logger.error("❌ CFBypass 客户端未初始化")
                return False
                
    except Exception as e:
        logger.error(f"❌ WHMCS CFBypass 集成测试失败: {e}")
        return False

async def test_models():
    """测试数据模型的 CFBypass 字段"""
    logger.info("🧪 开始测试数据模型")
    
    try:
        from whmcs.models import WHMCSConfig, WHMCSConfigRequest
        
        # 测试 WHMCSConfigRequest
        logger.info("📝 测试 WHMCSConfigRequest 模型...")
        request_data = {
            "base_url": "https://example.com",
            "username": "test",
            "hostname_base": "test",
            "use_cfbypass": True,
            "cfbypass_retries": 5
        }
        
        request = WHMCSConfigRequest(**request_data)
        logger.info(f"✅ WHMCSConfigRequest 创建成功: use_cfbypass={request.use_cfbypass}, retries={request.cfbypass_retries}")
        
        # 测试 WHMCSConfig
        logger.info("📝 测试 WHMCSConfig 模型...")
        config_data = {
            "base_url": "https://example.com",
            "username": "test",
            "password": "test",
            "hostname_base": "test",
            "rootpw": "test",
            "use_cfbypass": True,
            "cfbypass_retries": 3
        }
        
        config = WHMCSConfig(**config_data)
        logger.info(f"✅ WHMCSConfig 创建成功: use_cfbypass={config.use_cfbypass}, retries={config.cfbypass_retries}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据模型测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始 CFBypass 集成测试")
    
    # 检查环境变量
    cfbypass_url = os.getenv("CFPASS_BASE_URL")
    if cfbypass_url:
        logger.info(f"🔧 CFBypass 服务地址: {cfbypass_url}")
    else:
        logger.warning("⚠️ 未设置 CFPASS_BASE_URL 环境变量，使用默认值")
    
    tests = [
        ("数据模型测试", test_models),
        ("CFBypass 客户端测试", test_cfbypass_client),
        ("WHMCS CFBypass 集成测试", test_whmcs_with_cfbypass),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    logger.info(f"\n{'='*50}")
    logger.info("📊 测试结果总结")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{status} {test_name}")
    
    logger.info(f"\n🎯 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！CFBypass 集成功能正常")
        return True
    else:
        logger.error("💥 部分测试失败，请检查配置和服务状态")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
