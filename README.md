# 集成库存监控系统

一个支持多平台的自动库存监控和下单系统，目前支持 WHMCS 网站和 LowEndTalk (LET) 论坛的库存监控。

## 功能特性

- 🔍 **多平台库存监控**: 支持 WHMCS 网站和 LET 论坛的实时库存监控
- 🛒 **智能自动下单**: 当产品有库存时自动完成购买流程
- 🔄 **智能重试系统**: 可配置的下单重试机制，提高下单成功率
- 🎯 **多站点支持**: 支持同时监控多个不同平台的站点
- 📊 **统一Web管理界面**: 提供直观的 Web 界面统一管理所有平台
- 🔒 **安全认证**: 支持密码保护的管理界面
- 📱 **多渠道通知**: 支持邮件、Webhook、Discord、Telegram等通知方式
- 🐳 **容器化部署**: 支持 Docker 和 Docker Compose 一键部署

### 🌐 支持的平台

#### WHMCS 网站监控
- 实时库存状态检查
- 自动登录和下单
- 多产品并发监控
- 智能主机名生成

#### LowEndTalk (LET) 论坛监控
- 新帖子实时监控
- 关键词过滤
- 自动回复功能
- 论坛登录状态维护

### 🔄 智能重试下单系统

新增的智能重试功能确保下单流程更加可靠：

- **可配置重试次数**: 每个产品可独立设置最大重试次数（0-10 次）
- **渐进式延迟**: 重试时添加随机延迟，提高成功率
- **主机名冲突解决**: 自动生成新主机名避免重复
- **自动保护机制**: 达到最大重试次数后自动禁用
- **失败计数管理**: 支持手动重置失败计数
- **详细状态跟踪**: 完整记录重试历史和状态

## 快速开始

### 1. 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- Linux/macOS/Windows

### 2. 部署步骤

#### 2.1 克隆项目

```bash
git clone <repository-url>
cd integrated_monitor
```

#### 2.2 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
nano .env
```

**重要**: 请务必修改 `.env` 文件中的 `APP_PASSWORD`，设置一个安全的密码。

#### 2.3 启动服务

```bash
# 构建并启动服务
docker compose up -d

# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f api
```

#### 2.4 访问管理界面

打开浏览器访问: `http://localhost:8000`

使用您在 `.env` 文件中设置的密码登录。

### 3. 手动构建（可选）

如果您不使用 Docker，也可以手动安装和运行：

```bash
# 安装依赖 (需要Python 3.12+和uv)
uv sync

# 安装playwright浏览器
uv run playwright install chromium
uv run playwright install-deps

# 启动服务
uv run uvicorn api_server:app --host 0.0.0.0 --port 8000
```

## 配置说明

### 环境变量

| 变量名         | 描述             | 默认值             |
| -------------- | ---------------- | ------------------ |
| `APP_PASSWORD` | 管理界面访问密码 | `default_password` |
| `LOG_LEVEL`    | 日志级别         | `INFO`             |
| `DATA_DIR`     | 数据存储目录     | `./data`           |

### 平台配置

在 Web 管理界面中，您可以添加和配置不同平台的站点：

#### WHMCS 站点配置

1. **基本信息**:
   - 站点 URL
   - 用户名和密码
   - 主机名基础名称

2. **产品监控**:
   - 产品页面 URL
   - 监控间隔
   - 库存阈值

3. **自动下单**:
   - 支付方式
   - 下单参数
   - 通知设置

#### LET 论坛配置

1. **基本信息**:
   - 论坛用户名和密码
   - 监控关键词

2. **监控设置**:
   - 检查间隔
   - 关键词过滤规则
   - 是否启用自动回复

3. **通知配置**:
   - 通知方式选择
   - 通知内容模板

## 使用方法

### 1. 添加监控站点

#### 添加 WHMCS 站点
1. 登录管理界面
2. 在侧边栏点击"WHMCS 监控"
3. 点击"添加站点"
4. 填写站点信息：
   - 站点名称（ID）
   - 基础 URL
   - 登录凭据
   - 其他配置

#### 添加 LET 监控
1. 登录管理界面
2. 在侧边栏点击"LET 监控"
3. 配置监控设置：
   - 论坛登录信息
   - 关键词过滤
   - 通知配置

### 2. 配置产品监控

#### WHMCS 产品配置
1. 选择已添加的WHMCS站点
2. 点击"添加产品"
3. 配置产品信息：
   - 产品名称
   - 产品页面 URL
   - 是否启用监控
   - 是否启用自动下单
   - **最大重试次数**: 设置下单失败时的重试次数（0-10 次）

#### LET 监控配置
1. 在 LET 监控页面设置关键词
2. 配置监控频率
3. 设置通知方式和模板

#### 重试次数配置建议

- **0 次**: 禁用重试，只尝试一次（适合测试）
- **1-3 次**: 适合网络稳定的环境
- **4-6 次**: 适合偶有网络问题的环境
- **7-10 次**: 适合网络不稳定或高价值产品

#### 重试状态管理

- 在产品列表中可以查看当前失败次数
- 达到最大重试次数后系统会自动禁用自动下单
- 可以通过"重置"按钮重置失败计数并重新启用

### 3. 启动监控

1. 在管理界面点击"启动监控"
2. 系统将自动开始监控配置的所有平台
3. 可以在"仪表板"页面查看所有平台的实时状态

### 4. 查看日志

- Web 界面: 访问"日志"页面查看最近的运行日志
- 命令行: `docker compose logs -f api`
- 日志文件: `data/logs/integrated_monitor.log`

## 目录结构

```
integrated_monitor/
├── api_server.py           # API服务器主文件
├── integrated_monitor.py   # 集成监控核心逻辑
├── whmcs/                 # WHMCS监控模块
│   ├── models.py          # WHMCS数据模型
│   ├── core.py           # WHMCS核心逻辑
│   └── stock_monitor.py  # WHMCS库存监控
├── let/                   # LET监控模块
│   ├── models.py         # LET数据模型
│   ├── core.py          # LET核心逻辑
│   └── monitor.py       # LET论坛监控
├── notification.py        # 通知系统
├── control.py            # Playwright调试工具
├── frontend/             # 前端界面
│   ├── src/
│   │   ├── views/
│   │   │   ├── Dashboard.vue     # 仪表板
│   │   │   ├── Sites.vue         # WHMCS站点管理
│   │   │   ├── LetMonitor.vue    # LET监控管理
│   │   │   └── Settings.vue      # 系统设置
│   │   └── components/           # 共享组件
│   └── dist/             # 构建输出
├── data/                 # 数据存储目录
│   ├── config.json       # 集成配置
│   └── logs/            # 日志文件
├── docker-compose.yaml   # Docker编排文件
├── Dockerfile           # Docker镜像构建文件
├── pyproject.toml       # Python项目配置
└── .env                # 环境变量配置
```

## API 接口

系统提供完整的 RESTful API 接口：

### 认证

- `POST /api/login` - 用户登录
- `GET /api/logout` - 用户登出

### 集成监控管理

- `GET /api/status` - 获取所有平台监控状态
- `POST /api/monitor/start` - 启动集成监控
- `POST /api/monitor/stop` - 停止集成监控

### WHMCS 站点管理

- `GET /api/whmcs/sites` - 获取所有WHMCS站点
- `POST /api/whmcs/sites/{site_id}` - 添加/更新WHMCS站点
- `DELETE /api/whmcs/sites/{site_id}` - 删除WHMCS站点

### WHMCS 产品管理

- `GET /api/whmcs/sites/{site_id}/products` - 获取站点产品
- `POST /api/whmcs/sites/{site_id}/products/{product_id}` - 添加/更新产品
- `DELETE /api/whmcs/sites/{site_id}/products/{product_id}` - 删除产品

### LET 监控管理

- `GET /api/let/config` - 获取LET监控配置
- `POST /api/let/config` - 更新LET监控配置
- `GET /api/let/status` - 获取LET监控状态

### 系统管理

- `GET /api/logs` - 获取系统日志
- `GET /api/config` - 获取配置信息

## 故障排除

### 常见问题

1. **容器启动失败**

   ```bash
   # 检查日志
   docker compose logs api

   # 重新构建镜像
   docker compose build --no-cache
   ```

2. **Playwright 安装失败**

   - 确保使用的是 debian 基础镜像
   - 检查网络连接是否正常

3. **权限问题**

   ```bash
   # 确保data目录有正确权限
   sudo chown -R 1000:1000 data/
   ```

4. **端口被占用**
   ```bash
   # 修改docker-compose.yaml中的端口映射
   ports:
     - "8080:8000"  # 改为其他端口
   ```

### 调试模式

启用调试模式以获取更详细的日志：

```bash
# 在.env文件中设置
LOG_LEVEL=DEBUG

# 重启服务
docker compose restart
```

## 安全建议

1. **修改默认密码**: 务必修改 `.env` 文件中的 `APP_PASSWORD`
2. **网络访问控制**: 建议使用防火墙限制访问来源
3. **HTTPS**: 生产环境建议配置反向代理启用 HTTPS
4. **定期更新**: 定期更新依赖包和基础镜像

## 许可证

[许可证信息]

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 支持

如果您遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查 GitHub Issues 中的已知问题
3. 提交新的 Issue 描述您的问题
