APP_PASSWORD=your_password

PYDANTIC_AI_MODEL=openai:gpt-4.1
# 或者使用其他模型:
# PYDANTIC_AI_MODEL=anthropic:claude-3-5-sonnet-20241022
# PYDANTIC_AI_MODEL=gemini:gemini-1.5-pro

# OpenAI API配置 (如果使用OpenAI模型)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=your_api_base/v1

# Anthropic API配置 (如果使用Claude模型)  
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google API配置 (如果使用Gemini模型)
GOOGLE_API_KEY=your_google_api_key_here

# 代理配置 (可选，用于访问RSS)
HTTP_PROXY=http://127.0.0.1:7890
HTTPS_PROXY=http://127.0.0.1:7890

# 日志配置
LOG_LEVEL=INFO

# Logfire配置 (可选，用于监控)
LOGFIRE_TOKEN=your_logfire_token_here# LET RSS Monitor 配置示例

# AI模型配置 (pydantic-ai)
PYDANTIC_AI_MODEL=openai:gpt-4o
# 或者使用其他模型:
# PYDANTIC_AI_MODEL=anthropic:claude-3-5-sonnet-20241022
# PYDANTIC_AI_MODEL=gemini:gemini-1.5-pro

# OpenAI API配置 (如果使用OpenAI模型)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API配置 (如果使用Claude模型)  
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google API配置 (如果使用Gemini模型)
GOOGLE_API_KEY=your_google_api_key_here

# 代理配置 (可选，用于访问RSS)
HTTP_PROXY=http://127.0.0.1:7890
HTTPS_PROXY=http://127.0.0.1:7890

# 日志配置
LOG_LEVEL=INFO

# Logfire配置 (可选，用于监控)
LOGFIRE_TOKEN=your_logfire_token_here
CFPASS_BASE_URL=http://cfbypass:8000