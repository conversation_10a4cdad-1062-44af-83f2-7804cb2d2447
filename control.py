"""
控制脚本 - 用于调试的 Playwright 集成
"""

import asyncio
import json
import logging
from pathlib import Path
from urllib.parse import urlparse

from playwright.async_api import async_playwright

# 设置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class PlaywrightDebugger:
    """使用 Playwright 进行调试的控制器"""

    def __init__(self):
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=False, args=["--no-sandbox", "--disable-setuid-sandbox"]
        )
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

    def get_cookies_file_path(self, base_url: str, username: str) -> Path:
        """根据网站URL和用户名获取cookies文件路径"""
        cookies_dir = Path("data") / "cookies"
        cookies_dir.mkdir(parents=True, exist_ok=True)

        # 从 base_url 提取主机名
        hostname = urlparse(base_url).hostname
        if not hostname:
            hostname = base_url.replace("https://", "").replace("http://", "").split("/")[0]

        # 清理用户名
        safe_username = username.replace("@", "_")

        return cookies_dir / f"session_{hostname}_{safe_username}.json"

    async def load_cookies_from_file(self, cookies_file: Path, base_url: str) -> bool:
        """从文件加载 cookies 到 Playwright"""
        try:
            if not cookies_file.exists():
                logger.warning(f"Cookies 文件不存在: {cookies_file}")
                return False

            with open(cookies_file, encoding="utf-8") as f:
                cookies_data = json.load(f)

            if not cookies_data:
                logger.warning("Cookies 文件为空")
                return False

            # 转换为 Playwright 格式
            playwright_cookies = []
            domain = urlparse(base_url).hostname

            for cookie in cookies_data:
                # 确保 domain 是字符串
                cookie_domain = cookie.get("domain", "")
                if not cookie_domain or cookie_domain == "":
                    cookie_domain = domain

                playwright_cookie = {
                    "name": cookie["name"],
                    "value": cookie["value"],
                    "domain": str(cookie_domain),  # 确保是字符串
                    "path": cookie.get("path", "/"),
                }

                # 添加可选属性，并确保类型正确
                if cookie.get("expires") and isinstance(cookie.get("expires"), (int, float)):
                    playwright_cookie["expires"] = cookie["expires"]
                if cookie.get("secure") is True:
                    playwright_cookie["secure"] = True
                if cookie.get("httpOnly") is True:
                    playwright_cookie["httpOnly"] = True

                playwright_cookies.append(playwright_cookie)

            # 添加 cookies 到 context
            await self.context.add_cookies(playwright_cookies)
            logger.info(f"成功加载 {len(playwright_cookies)} 个 cookies")
            return True

        except Exception as e:
            logger.error(f"加载 cookies 失败: {e!s}")
            return False

    async def start_debug(self, base_url: str, username: str) -> None:
        """开始调试"""
        try:
            # 加载 cookies
            cookies_file = self.get_cookies_file_path(base_url, username)
            await self.load_cookies_from_file(cookies_file, base_url)

            # 导航到主页
            logger.info(f"正在导航到: {base_url}")
            await self.page.goto(base_url, wait_until="domcontentloaded")
            logger.info(f"成功导航到: {self.page.url}")

            logger.info("进入交互式调试模式...")
            logger.info("浏览器窗口已打开，可以手动操作")
            logger.info("按 Ctrl+C 退出")

            # 保持浏览器打开
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                logger.info("退出调试模式...")

        except Exception as e:
            logger.error(f"调试失败: {e!s}")


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="WHMCS Playwright 调试工具")
    parser.add_argument("--base-url", required=True, help="WHMCS 基础URL")
    parser.add_argument("--username", required=True, help="用户名（用于加载对应的cookies）")

    args = parser.parse_args()

    if not args.base_url.startswith("http"):
        args.base_url = "https://" + args.base_url

    async with PlaywrightDebugger() as debugger:
        await debugger.start_debug(args.base_url, args.username)


if __name__ == "__main__":
    asyncio.run(main())
