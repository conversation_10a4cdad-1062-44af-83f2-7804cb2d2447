@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

:root {
  --sidebar-width: 250px;
}

body {
  font-family: 'Inter', sans-serif;
  overflow-x: hidden;
}

/* 主题背景增强 */
body.dark {
  background: linear-gradient(-45deg, #0f172a, #1e293b, #0f172a, #1e293b);
  background-size: 400% 400%;
  animation: gradientShift 20s ease infinite;
}

/* 登录页面背景动画 - 亮色主题 */
.bg-animated-light {
  background: linear-gradient(-45deg, #f8fafc, #e2e8f0, #f1f5f9, #e2e8f0);
  background-size: 400% 400%;
  animation: gradientShift 20s ease infinite;
}

/* 登录页面背景动画 - 暗色主题 */
.bg-animated-dark {
  background: linear-gradient(-45deg, #0f172a, #1e293b, #334155, #1e293b);
  background-size: 400% 400%;
  animation: gradientShift 20s ease infinite;
}

/* 侧边栏样式 */
.sidebar {
  width: var(--sidebar-width);
  transition: transform 0.3s ease-in-out;
}

.dark .sidebar {
  background: rgba(15, 23, 42, 0.95);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.main-content {
  margin-left: var(--sidebar-width);
  transition: margin-left 0.3s ease-in-out;
  width: calc(100% - var(--sidebar-width));
}

.sidebar-closed .sidebar {
  transform: translateX(calc(var(--sidebar-width) * -1));
}

.sidebar-closed .main-content {
  margin-left: 0;
  width: 100%;
}

/* 统一的加载器样式 */
.spinner {
  border-top-color: transparent;
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 统一的按钮交互效果 */
.btn-interactive {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.btn-interactive:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark .btn-interactive:hover:not(:disabled) {
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

.btn-interactive:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-interactive:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 表格行悬停效果 */
.table-row-hover {
  transition: all 0.2s ease;
}

.table-row-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .table-row-hover:hover {
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.dark .card-hover:hover {
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

/* 状态指示器动画 */
.status-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 切换按钮动画 */
.toggle-btn {
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  transform: scale(1.05);
}

.toggle-btn:active {
  transform: scale(0.95);
}

/* 导航项悬停效果 */
.nav-item {
  transition: all 0.2s ease;
  border-radius: 8px;
}

.nav-item:hover {
  transform: translateX(4px);
}

/* 输入框焦点效果 */
.input-enhanced {
  transition: all 0.3s ease;
}

.input-enhanced:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

/* SweetAlert2 样式优化 */
.swal2-popup:not(.swal2-toast) {
  border-radius: 16px !important;
  padding: 2rem !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  background: #ffffff !important;
  border: 1px solid rgba(229, 231, 235, 0.5) !important;
}

.swal2-container.dark .swal2-popup:not(.swal2-toast),
html.dark .swal2-popup:not(.swal2-toast) {
  background: #1f2937 !important;
  color: #f3f4f6 !important;
  border: 1px solid rgba(75, 85, 99, 0.3) !important;
}

.swal2-popup:not(.swal2-toast) .swal2-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 1.5rem !important;
  text-align: center !important;
  color: #111827 !important;
}

.swal2-container.dark .swal2-title,
html.dark .swal2-title {
  color: #f3f4f6 !important;
}

.swal2-popup:not(.swal2-toast) .swal2-input,
.swal2-popup:not(.swal2-toast) .swal2-select,
.swal2-popup:not(.swal2-toast) .swal2-textarea {
  border-radius: 8px !important;
  border: 1px solid #d1d5db !important;
  padding: 8px 12px !important;
  margin: 8px 0 !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
  background: #f9fafb !important;
  color: #111827 !important;
  box-sizing: border-box !important;
}

.swal2-container.dark .swal2-input,
.swal2-container.dark .swal2-select,
.swal2-container.dark .swal2-textarea,
html.dark .swal2-input,
html.dark .swal2-select,
html.dark .swal2-textarea {
  background: #374151 !important;
  color: #f3f4f6 !important;
  border: 1px solid #4b5563 !important;
}

.swal2-input:focus,
.swal2-select:focus,
.swal2-textarea:focus {
  border-color: #6366f1 !important;
  outline: none !important;
}

.swal2-confirm {
  background: #6366f1 !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 8px 16px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
}

.swal2-confirm:hover {
  background: #4f46e5 !important;
}

.swal2-cancel {
  background: transparent !important;
  border: 1px solid #d1d5db !important;
  border-radius: 8px !important;
  padding: 8px 16px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
  color: #6b7280 !important;
}

.swal2-cancel:hover {
  background: #f3f4f6 !important;
}

.swal2-container.dark .swal2-cancel,
html.dark .swal2-cancel {
  color: #9ca3af !important;
  border-color: #4b5563 !important;
}

.swal2-container.dark .swal2-cancel:hover,
html.dark .swal2-cancel:hover {
  background: #374151 !important;
}

/* Toast 通知样式 */
.swal2-popup.swal2-toast {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: none !important;
  font-weight: 500 !important;
  min-width: 300px !important;
  max-width: 400px !important;
  width: auto !important;
  background: #ffffff !important;
  color: #111827 !important;
}

html.dark .swal2-popup.swal2-toast,
body.dark .swal2-popup.swal2-toast {
  background: #1f2937 !important;
  color: #f3f4f6 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.swal2-popup.swal2-toast .swal2-title {
  font-size: 14px !important;
  font-weight: 600 !important;
  margin: 0 !important;
  padding: 0 !important;
  color: inherit !important;
}

.swal2-popup.swal2-toast .swal2-icon {
  width: 20px !important;
  height: 20px !important;
  margin: 0 8px 0 0 !important;
  border-width: 2px !important;
}

/* Toast定时器条样式 */
.swal2-popup.swal2-toast .swal2-timer-progress-bar {
  height: 2px !important;
  background: #6366f1 !important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebar-closed .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar-closed .main-content {
    margin-left: 0;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .swal2-popup {
    width: 95% !important;
    margin: 0 auto !important;
    padding: 1.5rem !important;
  }

  .swal2-title {
    font-size: 1.25rem !important;
    margin-bottom: 1rem !important;
  }

  .swal2-input,
  .swal2-select,
  .swal2-textarea {
    font-size: 16px !important;
    padding: 12px 16px !important;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-8px); }
  20%, 40%, 60%, 80% { transform: translateX(8px); }
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
  50% { 
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.6);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-pulse-custom {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shake {
  animation: shake 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* 状态指示器 */
.status-indicator.running {
  color: #10b981;
  animation: glow 2s ease-in-out infinite;
}

.status-indicator.stopped {
  color: #ef4444;
}

/* 顶部导航栏增强 */
.dark header {
  background: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 日志面板样式 */
.log-panel {
  background: #0f172a;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  border-radius: 12px;
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
}

.dark .log-panel {
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 特殊效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
} 