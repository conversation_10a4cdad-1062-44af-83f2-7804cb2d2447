import Swal from 'sweetalert2'

// 时间格式化函数
export const formatTime = (isoString, short = false) => {
  if (!isoString) return 'N/A'
  
  let date
  try {
    // 尝试解析时间字符串
    if (typeof isoString === 'string') {
      // 处理可能的时间格式
      if (isoString.includes(' - ')) {
        // 处理日志时间格式: "2025-01-20 10:30:45,123"
        const cleanTime = isoString.split(' - ')[0].replace(',', '.')
        date = new Date(cleanTime)
      } else {
        date = new Date(isoString)
      }
    } else {
      date = new Date(isoString)
    }
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return 'N/A'
    }
  } catch (error) {
    console.warn('Time format error:', error, 'Input:', isoString)
    return 'N/A'
  }
  
  if (short) {
    const now = new Date()
    const diffMinutes = Math.floor((now - date) / (1000 * 60))
    
    if (diffMinutes < 1) return '刚刚'
    if (diffMinutes < 60) return `${diffMinutes}分钟前`
    
    const diffHours = Math.floor(diffMinutes / 60)
    if (diffHours < 24) return `${diffHours}小时前`
    
    const diffDays = Math.floor(diffHours / 24)
    if (diffDays > 365) return `${Math.floor(diffDays / 365)}年前`
    if (diffDays > 30) return `${Math.floor(diffDays / 30)}个月前`
    return `${diffDays}天前`
  }
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// Toast 提示函数
export const toast = (type, message, duration = 3000) => {
  // 如果全局Toast组件可用，使用它
  if (window.showToast) {
    return window.showToast(message, type, duration)
  }
  
  // 降级方案：使用SweetAlert2
  return Swal.fire({
    icon: type,
    title: message,
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: duration,
    timerProgressBar: true,
    didOpen: (toast) => {
      toast.addEventListener('mouseenter', Swal.stopTimer)
      toast.addEventListener('mouseleave', Swal.resumeTimer)
    }
  })
}

// 确认对话框函数
export const showConfirm = async (title, text) => {
  const isDarkMode = document.documentElement.classList.contains('dark')
  
  const result = await Swal.fire({
    title,
    text,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#d33',
    cancelButtonColor: '#3085d6',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    customClass: {
      container: isDarkMode ? 'dark' : '',
      popup: isDarkMode ? 'dark' : ''
    }
  })
  
  return result.isConfirmed
}

// 计费周期文本转换
export const getBillingCycleText = (cycle) => {
  const cycleMap = {
    'monthly': '月付',
    'quarterly': '季付',
    'semiannually': '半年付',
    'annually': '年付',
    'biennially': '两年付',
    'triennially': '三年付'
  }
  return cycleMap[cycle] || cycle
}

// 日志颜色获取
export const getLogColor = (level) => {
  const colorMap = {
    'INFO': 'text-blue-400',
    'WARNING': 'text-yellow-400',
    'ERROR': 'text-red-400',
    'SUCCESS': 'text-green-400',
    'DEBUG': 'text-gray-400'
  }
  return colorMap[level] || 'text-gray-400'
}

// 状态颜色获取
export const getStatusColor = (status) => {
  switch (status) {
    case 'running':
      return 'text-green-400'
    case 'stopped':
      return 'text-red-400'
    case 'error':
      return 'text-red-400'
    default:
      return 'text-gray-400'
  }
}

// 检查屏幕尺寸
export const checkScreenSize = () => {
  return window.innerWidth >= 1024
}

// 防抖函数
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
} 