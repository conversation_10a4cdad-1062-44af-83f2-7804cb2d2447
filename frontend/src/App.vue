<template>
  <div id="app" :class="[isSidebarOpen ? '' : 'sidebar-closed', theme]">
    <!-- 侧边栏 -->
    <Sidebar />
    
    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 顶部导航栏 -->
      <TopNavbar />
      
      <!-- 路由视图 -->
      <main class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-200">
        <RouterView />
      </main>
    </div>
    
    <!-- 移动端遮罩 -->
    <div 
      v-if="isSidebarOpen && !isDesktop" 
      @click="isSidebarOpen = false"
      class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
    ></div>
    
    <!-- Toast通知组件 -->
    <Toast />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, computed, nextTick } from 'vue'
import { useMainStore } from '@/stores/main'
import { checkScreenSize } from '@/utils/helpers'
import Sidebar from '@/components/Sidebar.vue'
import TopNavbar from '@/components/TopNavbar.vue'
import Toast from '@/components/Toast.vue'

const store = useMainStore()

// 计算属性
const theme = computed(() => store.theme)
const isSidebarOpen = computed({
  get: () => store.isSidebarOpen,
  set: (value) => store.isSidebarOpen = value
})

const isDesktop = computed(() => checkScreenSize())

// 检查屏幕尺寸
const handleResize = () => {
  if (window.innerWidth < 1024) {
    store.isSidebarOpen = false
  } else {
    store.isSidebarOpen = true
  }
}

// 异步初始化数据
const initializeData = async () => {
  // 使用Promise.allSettled确保即使某个API失败也不会影响其他API
  const promises = [
    store.getStatus(),
    store.getLogs(),
    store.getSites(),
    store.getConfig()
  ]
  
  // 并行执行所有API调用，不等待结果
  Promise.allSettled(promises).then((results) => {
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        const apiNames = ['getStatus', 'getLogs', 'getSites', 'getConfig']
        console.warn(`API ${apiNames[index]} 初始化失败:`, result.reason)
      }
    })
  })
}

// 生命周期钩子
onMounted(async () => {
  // 初始化主题
  store.initTheme()
  
  // 立即设置屏幕尺寸和监听器
  handleResize()
  window.addEventListener('resize', handleResize)
  
  // 等待下一个tick确保DOM已渲染，然后异步初始化数据
  await nextTick()
  initializeData()

  // 设置轮询 - 同样使用非阻塞方式
  const statusInterval = setInterval(() => {
    // 不等待结果，让API调用在后台执行
    Promise.allSettled([
      store.getStatus(),
      store.getLogs()
    ]).catch(() => {
      // 静默处理错误，避免影响用户体验
    })
  }, 5000)

  // 清理函数
  onUnmounted(() => {
    clearInterval(statusInterval)
    window.removeEventListener('resize', handleResize)
  })
})
</script> 