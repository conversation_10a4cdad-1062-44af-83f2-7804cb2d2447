import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'
import Sites from '../views/Sites.vue'
import Products from '../views/Products.vue'
import Settings from '../views/Settings.vue'
import LetMonitor from '../views/LetMonitor.vue'
import Notifications from '../views/Notifications.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'dashboard',
      component: Dashboard,
      meta: { requiresAuth: true }
    },
    {
      path: '/sites',
      name: 'sites',
      component: Sites,
      meta: { requiresAuth: true }
    },
    {
      path: '/sites/:siteId/products',
      name: 'products',
      component: Products,
      props: true,
      meta: { requiresAuth: true }
    },
    {
      path: '/let-monitor',
      name: 'let-monitor',
      component: LetMonitor,
      meta: { requiresAuth: true }
    },
    {
      path: '/notifications',
      name: 'notifications',
      component: Notifications,
      meta: { requiresAuth: true }
    },
    {
      path: '/settings',
      name: 'settings',
      component: Settings,
      meta: { requiresAuth: true }
    },
    // 重定向未知路由到首页
    {
      path: '/:pathMatch(.*)*',
      redirect: '/'
    }
  ]
})

// 缓存授权状态，避免重复请求
let authCache = {
  isAuthenticated: null,
  lastCheck: 0,
  cacheTimeout: 30000 // 30秒缓存
}

// 检查用户是否已授权
const checkAuth = async () => {
  const now = Date.now()
  
  // 如果缓存有效，直接返回缓存结果
  if (authCache.isAuthenticated !== null && 
      (now - authCache.lastCheck) < authCache.cacheTimeout) {
    return authCache.isAuthenticated
  }
  
  try {
    const response = await fetch('/api/status', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    })
    
    const isAuthenticated = response.status !== 401 && response.ok
    
    // 更新缓存
    authCache = {
      isAuthenticated,
      lastCheck: now,
      cacheTimeout: authCache.cacheTimeout
    }
    
    return isAuthenticated
  } catch (error) {
    console.error('授权检查失败:', error)
    // 网络错误时，如果有缓存且不太旧，使用缓存
    if (authCache.isAuthenticated !== null && 
        (now - authCache.lastCheck) < 60000) { // 1分钟内的缓存仍可用
      return authCache.isAuthenticated
    }
    
    // 否则认为未授权
    authCache.isAuthenticated = false
    authCache.lastCheck = now
    return false
  }
}

// 清除授权缓存的函数
export const clearAuthCache = () => {
  authCache = {
    isAuthenticated: null,
    lastCheck: 0,
    cacheTimeout: 30000
  }
}

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 如果路由需要授权
  if (to.matched.some(record => record.meta.requiresAuth)) {
    const isAuthenticated = await checkAuth()
    
    if (!isAuthenticated) {
      // 未授权，跳转到登录页面
      window.location.href = '/login.html'
      return
    }
  }
  
  next()
})

export default router 