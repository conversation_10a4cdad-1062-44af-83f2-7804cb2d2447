import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/utils/api'
import { clearAuthCache } from '@/router'

export const useMainStore = defineStore('main', () => {
  // State
  const theme = ref(localStorage.getItem('theme') || 'dark')
  const currentView = ref('dashboard')
  const isSidebarOpen = ref(true)
  const status = ref({})
  const monitorStatus = ref('stopped')
  const logs = ref([])
  const sites = ref([])
  const products = ref([])
  const selectedSiteId = ref(null)
  const currentSiteId = ref(null)
  const config = ref({
    whmcs: {},
    let_monitor: {},
    notifications: {}
  })
  const isLoading = ref({
    status: false,
    monitor: false,
    sites: false,
    products: false,
    config: false,
    testingLogin: null
  })

  // Getters
  const themeIcon = computed(() => theme.value === 'dark' ? '☀️' : '🌙')

  // Actions
  const toggleTheme = () => {
    theme.value = theme.value === 'dark' ? 'light' : 'dark'
    localStorage.setItem('theme', theme.value)
    
    if (theme.value === 'dark') {
      document.documentElement.classList.add('dark')
      document.body.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
      document.body.classList.remove('dark')
    }
  }

  const initTheme = () => {
    if (theme.value === 'dark') {
      document.documentElement.classList.add('dark')
      document.body.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
      document.body.classList.remove('dark')
    }
  }

  const getStatus = async () => {
    if (isLoading.value.status) {
      return
    }
    
    isLoading.value.status = true
    try {
      const data = await api.get('/api/status')
      status.value = data
      monitorStatus.value = (data.is_running || data.monitor_running) ? 'running' : 'stopped'
    } catch (error) {
      console.error('Failed to get status:', error)
    } finally {
      isLoading.value.status = false
    }
  }

  const getLogs = async () => {
    try {
      const data = await api.get('/api/logs')
      logs.value = data.logs || []
    } catch (error) {
      console.error('Failed to get logs:', error)
    }
  }

  const getSites = async () => {
    if (isLoading.value.sites) {
      return
    }
    
    isLoading.value.sites = true
    try {
      const data = await api.get('/api/sites')
      sites.value = Object.entries(data || {}).map(([siteId, siteData]) => ({
        site_id: siteId,
        ...siteData
      }))
    } catch (error) {
      console.error('Failed to get sites:', error)
    } finally {
      isLoading.value.sites = false
    }
  }

  const getProducts = async (siteId) => {
    if (isLoading.value.products) {
      return
    }
    
    isLoading.value.products = true
    try {
      const data = await api.get(`/api/sites/${siteId}/products`)
      products.value = Object.entries(data || {}).map(([productId, productData]) => ({
        product_id: productId,
        ...productData
      }))
      currentSiteId.value = siteId
    } catch (error) {
      console.error('Failed to get products:', error)
    } finally {
      isLoading.value.products = false
    }
  }

  const getConfig = async () => {
    if (isLoading.value.config) {
      return
    }
    
    isLoading.value.config = true
    try {
      const data = await api.get('/api/config')
      config.value = data
    } catch (error) {
      console.error('Failed to get config:', error)
    } finally {
      isLoading.value.config = false
    }
  }

  const startMonitor = async () => {
    isLoading.value.monitor = true
    try {
      await api.post('/api/monitor/start')
      await getStatus()
    } finally {
      isLoading.value.monitor = false
    }
  }

  const stopMonitor = async () => {
    isLoading.value.monitor = true
    try {
      await api.post('/api/monitor/stop')
      await getStatus()
    } finally {
      isLoading.value.monitor = false
    }
  }

  const deleteSite = async (siteId) => {
    try {
      await api.delete(`/api/sites/${siteId}`)
      await getSites()
      return true
    } catch (error) {
      return false
    }
  }

  const deleteProduct = async (siteId, productId) => {
    try {
      await api.delete(`/api/sites/${siteId}/products/${productId}`)
      await getProducts(siteId)
      return true
    } catch (error) {
      return false
    }
  }

  const toggleProduct = async (siteId, productId, enabled) => {
    try {
      await api.post(`/api/sites/${siteId}/products/${productId}/toggle`, { enabled })
      await getProducts(siteId)
      return true
    } catch (error) {
      return false
    }
  }

  const toggleAutoOrder = async (siteId, productId) => {
    try {
      await api.post(`/api/sites/${siteId}/products/${productId}/toggle-auto-order`)
      await getProducts(siteId)
      return true
    } catch (error) {
      return false
    }
  }

  const resetFailures = async (siteId, productId) => {
    try {
      await api.post(`/api/sites/${siteId}/products/${productId}/reset-failures`)
      await getProducts(siteId)
      return true
    } catch (error) {
      return false
    }
  }

  const testLogin = async (siteId) => {
    isLoading.value.testingLogin = siteId
    try {
      const data = await api.get(`/api/test-login/${siteId}`)
      return data
    } finally {
      isLoading.value.testingLogin = null
    }
  }

  const saveSettings = async (configType, payload) => {
    try {
      const data = await api.post(`/api/config/${configType}`, payload)
      await getConfig()
      return data
    } catch (error) {
      return false
    }
  }

  const logout = async () => {
    try {
      await api.post('/api/logout')
      clearAuthCache()
      window.location.href = '/login'
    } catch (error) {
      clearAuthCache()
      window.location.href = '/login'
    }
  }

  return {
    theme,
    currentView,
    isSidebarOpen,
    status,
    monitorStatus,
    logs,
    sites,
    products,
    selectedSiteId,
    currentSiteId,
    config,
    isLoading,
    
    themeIcon,
    
    toggleTheme,
    initTheme,
    getStatus,
    getLogs,
    getSites,
    getProducts,
    getConfig,
    startMonitor,
    stopMonitor,
    deleteSite,
    deleteProduct,
    toggleProduct,
    toggleAutoOrder,
    resetFailures,
    testLogin,
    saveSettings,
    logout
  }
}) 