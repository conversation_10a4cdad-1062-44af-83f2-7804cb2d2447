<template>
  <teleport to="body">
    <div class="fixed top-4 right-4 z-50 space-y-2">
      <transition-group
        name="toast"
        tag="div"
        class="space-y-2"
      >
        <div
          v-for="toast in toasts"
          :key="toast.id"
          :class="[
            'flex items-center p-4 rounded-lg shadow-lg backdrop-blur-sm border transform transition-all duration-300',
            getToastClasses(toast.type)
          ]"
          class="max-w-sm"
        >
          <!-- 图标 -->
          <div class="flex-shrink-0 mr-3">
            <svg v-if="toast.type === 'success'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <svg v-else-if="toast.type === 'error'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <svg v-else-if="toast.type === 'warning'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          </div>

          <!-- 消息内容 -->
          <div class="flex-1 mr-2">
            <p class="text-sm font-medium">{{ toast.message }}</p>
          </div>

          <!-- 关闭按钮 -->
          <button
            @click="removeToast(toast.id)"
            class="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>

          <!-- 进度条 -->
          <div
            v-if="toast.showProgress"
            class="absolute bottom-0 left-0 h-1 bg-current opacity-30 rounded-b-lg transition-all duration-100 ease-linear"
            :style="{ width: `${toast.progress}%` }"
          ></div>
        </div>
      </transition-group>
    </div>
  </teleport>
</template>

<script>
import { ref, reactive } from 'vue'

let toastId = 0

export default {
  name: 'Toast',
  setup() {
    const toasts = ref([])

    const getToastClasses = (type) => {
      const baseClasses = 'border backdrop-blur-sm'
      
      switch (type) {
        case 'success':
          return `${baseClasses} bg-green-50/90 dark:bg-green-900/90 border-green-200 dark:border-green-700 text-green-800 dark:text-green-200`
        case 'error':
          return `${baseClasses} bg-red-50/90 dark:bg-red-900/90 border-red-200 dark:border-red-700 text-red-800 dark:text-red-200`
        case 'warning':
          return `${baseClasses} bg-yellow-50/90 dark:bg-yellow-900/90 border-yellow-200 dark:border-yellow-700 text-yellow-800 dark:text-yellow-200`
        default:
          return `${baseClasses} bg-blue-50/90 dark:bg-blue-900/90 border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-200`
      }
    }

    const addToast = (message, type = 'info', duration = 3000) => {
      const id = ++toastId
      const toast = reactive({
        id,
        message,
        type,
        progress: 100,
        showProgress: duration > 0
      })

      toasts.value.push(toast)

      if (duration > 0) {
        let elapsed = 0
        const interval = 50
        const step = (interval / duration) * 100

        const timer = setInterval(() => {
          elapsed += interval
          toast.progress = Math.max(0, 100 - (elapsed / duration) * 100)

          if (elapsed >= duration) {
            clearInterval(timer)
            removeToast(id)
          }
        }, interval)

        // 鼠标悬停时暂停倒计时
        toast.pauseTimer = () => clearInterval(timer)
        toast.resumeTimer = () => {
          // 重新启动倒计时逻辑可以在这里实现
        }
      }

      return id
    }

    const removeToast = (id) => {
      const index = toasts.value.findIndex(t => t.id === id)
      if (index > -1) {
        toasts.value.splice(index, 1)
      }
    }

    const clearAllToasts = () => {
      toasts.value = []
    }

    // 暴露给全局使用的方法
    window.showToast = addToast

    return {
      toasts,
      getToastClasses,
      addToast,
      removeToast,
      clearAllToasts
    }
  }
}
</script>

<style scoped>
.toast-enter-active {
  transition: all 0.3s ease-out;
}

.toast-leave-active {
  transition: all 0.3s ease-in;
}

.toast-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.toast-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.toast-move {
  transition: transform 0.3s ease;
}
</style> 