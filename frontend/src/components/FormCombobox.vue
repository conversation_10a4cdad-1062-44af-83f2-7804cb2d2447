<template>
  <div :class="['form-combobox-field', { 'has-error': hasError, 'is-focused': isFocused }]">
    <!-- 标签 -->
    <label 
      v-if="label" 
      :for="fieldId" 
      :class="[
        'form-label',
        required && 'required',
        hasError && 'text-red-600 dark:text-red-400',
        isFocused && 'text-indigo-600 dark:text-indigo-400'
      ]"
    >
      <span class="flex items-center">
        <slot name="prefix">
          <component v-if="prefixIcon" :is="prefixIcon" class="w-4 h-4 mr-2" />
        </slot>
        {{ label }}
        <span v-if="required" class="text-red-500 ml-1">*</span>
      </span>
    </label>

    <!-- 输入框容器 -->
    <div class="form-combobox-container">
      <!-- 输入框 -->
      <input
        :id="fieldId"
        :value="modelValue"
        type="text"
        :placeholder="placeholder"
        :disabled="disabled"
        :required="required"
        :class="[
          'form-combobox-input',
          hasError && 'border-red-500 focus:border-red-500 focus:ring-red-500',
          !hasError && 'border-gray-300 dark:border-gray-600 focus:border-indigo-500 focus:ring-indigo-500'
        ]"
        @focus="handleFocus"
        @blur="handleBlur"
        @input="handleInput"
        @keydown="handleKeydown"
        autocomplete="off"
      />

      <!-- 下拉按钮 -->
      <button
        type="button"
        class="form-combobox-button"
        @click="toggleDropdown"
        :disabled="disabled"
      >
        <svg class="w-5 h-5 text-gray-400 transition-transform duration-200" :class="{ 'rotate-180': isOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <!-- 下拉选项列表 -->
      <transition name="dropdown">
        <div v-if="isOpen && filteredOptions.length > 0" class="form-combobox-dropdown">
          <div class="max-h-60 overflow-y-auto">
            <div
              v-for="(option, index) in filteredOptions"
              :key="option.value"
              :class="[
                'form-combobox-option',
                index === selectedIndex && 'selected',
                option.value === modelValue && 'active'
              ]"
              @click="selectOption(option)"
              @mouseenter="selectedIndex = index"
            >
              <div class="flex items-center justify-between">
                <div>
                  <div class="font-medium text-gray-900 dark:text-white">{{ option.label }}</div>
                  <div v-if="option.description" class="text-sm text-gray-500 dark:text-gray-400">{{ option.description }}</div>
                </div>
                <div v-if="option.value === modelValue" class="text-indigo-600 dark:text-indigo-400">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 自定义输入提示 -->
          <div v-if="modelValue && !exactMatch" class="form-combobox-custom">
            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400 px-3 py-2 border-t border-gray-200 dark:border-gray-600">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              使用自定义值: "{{ modelValue }}"
            </div>
          </div>
        </div>
      </transition>
    </div>

    <!-- 帮助文本和错误信息 -->
    <div class="form-message">
      <transition name="slide-down">
        <div v-if="hasError" class="form-error">
          <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {{ errorMessage }}
        </div>
      </transition>
      
      <div v-if="helpText && !hasError" class="form-help">
        {{ helpText }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'FormCombobox',
  props: {
    modelValue: {
      type: [String, Number],
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入或选择...'
    },
    options: {
      type: Array,
      default: () => [],
      validator: (options) => {
        return options.every(option => 
          typeof option === 'object' && 
          'value' in option && 
          'label' in option
        )
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    errorMessage: {
      type: String,
      default: ''
    },
    helpText: {
      type: String,
      default: ''
    },
    prefixIcon: {
      type: [String, Object],
      default: null
    },
    filterMatchMode: {
      type: String,
      default: 'contains', // 'contains', 'startsWith', 'exact'
      validator: value => ['contains', 'startsWith', 'exact'].includes(value)
    }
  },
  emits: ['update:modelValue', 'focus', 'blur', 'input', 'select'],
  setup(props, { emit }) {
    const isFocused = ref(false)
    const isOpen = ref(false)
    const selectedIndex = ref(-1)
    
    const fieldId = computed(() => {
      return `combobox-${Math.random().toString(36).substr(2, 9)}`
    })
    
    const hasError = computed(() => {
      return !!props.errorMessage
    })
    
    // 过滤选项
    const filteredOptions = computed(() => {
      if (!props.modelValue) return props.options
      
      const query = props.modelValue.toString().toLowerCase()
      return props.options.filter(option => {
        const label = option.label.toLowerCase()
        const value = option.value.toString().toLowerCase()
        
        switch (props.filterMatchMode) {
          case 'startsWith':
            return label.startsWith(query) || value.startsWith(query)
          case 'exact':
            return label === query || value === query
          case 'contains':
          default:
            return label.includes(query) || value.includes(query)
        }
      })
    })
    
    // 检查是否有完全匹配的选项
    const exactMatch = computed(() => {
      return props.options.some(option => 
        option.value === props.modelValue || option.label === props.modelValue
      )
    })
    
    const handleFocus = (event) => {
      isFocused.value = true
      emit('focus', event)
    }
    
    const handleBlur = (event) => {
      // 延迟关闭下拉框，允许点击选项
      setTimeout(() => {
        isFocused.value = false
        isOpen.value = false
        selectedIndex.value = -1
      }, 150)
      emit('blur', event)
    }
    
    const handleInput = (event) => {
      const value = event.target.value
      emit('update:modelValue', value)
      emit('input', event)
      
      // 输入时打开下拉框
      if (!isOpen.value) {
        isOpen.value = true
      }
      selectedIndex.value = -1
    }
    
    const handleKeydown = (event) => {
      if (!isOpen.value && (event.key === 'ArrowDown' || event.key === 'ArrowUp')) {
        isOpen.value = true
        selectedIndex.value = 0
        event.preventDefault()
        return
      }
      
      if (isOpen.value) {
        switch (event.key) {
          case 'ArrowDown':
            selectedIndex.value = Math.min(selectedIndex.value + 1, filteredOptions.value.length - 1)
            event.preventDefault()
            break
          case 'ArrowUp':
            selectedIndex.value = Math.max(selectedIndex.value - 1, 0)
            event.preventDefault()
            break
          case 'Enter':
            if (selectedIndex.value >= 0 && filteredOptions.value[selectedIndex.value]) {
              selectOption(filteredOptions.value[selectedIndex.value])
            }
            event.preventDefault()
            break
          case 'Escape':
            isOpen.value = false
            selectedIndex.value = -1
            event.preventDefault()
            break
        }
      }
    }
    
    const toggleDropdown = () => {
      if (props.disabled) return
      isOpen.value = !isOpen.value
      if (isOpen.value) {
        selectedIndex.value = -1
      }
    }
    
    const selectOption = (option) => {
      emit('update:modelValue', option.value)
      emit('select', option)
      isOpen.value = false
      selectedIndex.value = -1
    }
    
    // 点击外部关闭下拉框
    const handleClickOutside = (event) => {
      const element = event.target.closest('.form-combobox-field')
      if (!element) {
        isOpen.value = false
        selectedIndex.value = -1
      }
    }
    
    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
    })
    
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })
    
    return {
      isFocused,
      isOpen,
      selectedIndex,
      fieldId,
      hasError,
      filteredOptions,
      exactMatch,
      handleFocus,
      handleBlur,
      handleInput,
      handleKeydown,
      toggleDropdown,
      selectOption
    }
  }
}
</script>

<style scoped>
.form-combobox-field {
  @apply space-y-2 relative;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors duration-200;
}

.form-label.required {
  @apply font-semibold;
}

.form-combobox-container {
  @apply relative;
}

.form-combobox-input {
  @apply w-full py-3 px-4 pr-10 bg-white dark:bg-gray-700 border rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-0 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform;
}

.form-combobox-input:focus {
  @apply shadow-md scale-[1.01];
}

.form-combobox-field.is-focused .form-combobox-input {
  @apply shadow-lg;
}

.form-combobox-field.has-error .form-combobox-input {
  @apply bg-red-50 dark:bg-red-900/20;
}

.form-combobox-button {
  @apply absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 dark:hover:bg-gray-600 rounded-r-lg transition-colors duration-200;
}

.form-combobox-dropdown {
  @apply absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50;
}

.form-combobox-option {
  @apply px-3 py-2 cursor-pointer transition-colors duration-150;
}

.form-combobox-option:hover,
.form-combobox-option.selected {
  @apply bg-gray-100 dark:bg-gray-600;
}

.form-combobox-option.active {
  @apply bg-indigo-50 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300;
}

.form-combobox-custom {
  @apply bg-gray-50 dark:bg-gray-600;
}

.form-message {
  @apply min-h-[1.25rem];
}

.form-error {
  @apply text-sm text-red-600 dark:text-red-400 flex items-start;
}

.form-help {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* 动画 */
.dropdown-enter-active {
  @apply transition-all duration-200 ease-out;
}

.dropdown-leave-active {
  @apply transition-all duration-150 ease-in;
}

.dropdown-enter-from {
  @apply opacity-0 transform -translate-y-2 scale-95;
}

.dropdown-leave-to {
  @apply opacity-0 transform -translate-y-2 scale-95;
}

.slide-down-enter-active {
  @apply transition-all duration-200 ease-out;
}

.slide-down-leave-active {
  @apply transition-all duration-150 ease-in;
}

.slide-down-enter-from {
  @apply opacity-0 transform -translate-y-1;
}

.slide-down-leave-to {
  @apply opacity-0 transform -translate-y-1;
}

/* 悬停效果 */
.form-combobox-input:hover:not(:disabled) {
  @apply border-gray-400 dark:border-gray-500;
}

.form-combobox-field:hover .form-label {
  @apply text-gray-900 dark:text-gray-100;
}
</style>