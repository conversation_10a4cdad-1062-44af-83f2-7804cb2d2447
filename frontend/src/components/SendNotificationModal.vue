<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
          发送自定义通知
        </h3>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <form @submit.prevent="sendNotification" class="p-6 space-y-6">
        <!-- 消息标题 -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            消息标题 *
          </label>
          <input
            v-model="form.title"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="请输入通知标题"
            required
          />
        </div>

        <!-- 消息内容 -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            消息内容 *
          </label>
          <textarea
            v-model="form.content"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="请输入通知内容"
            required
          ></textarea>
        </div>

        <!-- 通知类型 -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            通知类型
          </label>
          <select
            v-model="form.notification_type"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="custom">自定义</option>
            <option value="whmcs_stock">WHMCS库存</option>
            <option value="whmcs_order">WHMCS订单</option>
            <option value="let_monitor">LET监控</option>
            <option value="system_status">系统状态</option>
            <option value="error_alert">错误警报</option>
          </select>
        </div>

        <!-- 优先级 -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            优先级
          </label>
          <select
            v-model="form.priority"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="low">低</option>
            <option value="normal">普通</option>
            <option value="high">高</option>
            <option value="critical">紧急</option>
          </select>
        </div>

        <!-- 目标渠道 -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            目标渠道
          </label>
          <div class="space-y-2 max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-3">
            <label class="flex items-center">
              <input
                v-model="selectAllChannels"
                type="checkbox"
                @change="toggleAllChannels"
                class="rounded text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300 font-medium">全选</span>
            </label>
            <hr class="border-gray-200 dark:border-gray-600">
            <label
              v-for="channel in enabledChannels"
              :key="channel.id"
              class="flex items-center"
            >
              <input
                v-model="form.target_channels"
                :value="channel.id"
                type="checkbox"
                class="rounded text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                {{ channel.name }} ({{ getChannelTypeName(channel.channel_type) }})
              </span>
            </label>
          </div>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            不选择任何渠道将发送到所有适用的渠道
          </p>
        </div>

        <!-- 标签 -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            标签 (可选)
          </label>
          <input
            v-model="tagsInput"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="用逗号分隔多个标签，如：urgent,manual"
          />
        </div>

        <!-- Markdown格式 -->
        <div class="mb-6">
          <label class="flex items-center">
            <input
              v-model="form.format_markdown"
              type="checkbox"
              class="rounded text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">启用 Markdown 格式</span>
          </label>
        </div>

        <!-- 预览区域 -->
        <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">预览</h4>
          <div class="text-sm">
            <div class="font-semibold text-gray-900 dark:text-white">{{ form.title || '标题预览' }}</div>
            <div class="text-gray-600 dark:text-gray-400 mt-1">{{ form.content || '内容预览' }}</div>
            <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
              <span class="flex items-center">
                <span :class="getPriorityColor(form.priority)" class="w-2 h-2 rounded-full mr-1"></span>
                优先级: {{ getPriorityName(form.priority) }}
              </span>
              <span>类型: {{ getNotificationTypeName(form.notification_type) }}</span>
              <span v-if="form.target_channels.length">
                目标: {{ form.target_channels.length }}个渠道
              </span>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-lg transition-colors"
          >
            取消
          </button>
          <button
            type="submit"
            :disabled="!form.title || !form.content || loading"
            class="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center space-x-2"
          >
            <svg v-if="loading" class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span>{{ loading ? '发送中...' : '发送通知' }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, defineProps, defineEmits } from 'vue'

const props = defineProps({
  visible: Boolean,
  channels: Array,
  notificationTypes: Array
})

const emit = defineEmits(['close', 'send'])

const loading = ref(false)
const selectAllChannels = ref(false)
const tagsInput = ref('')

const form = reactive({
  title: '',
  content: '',
  notification_type: 'custom',
  priority: 'normal',
  target_channels: [],
  format_markdown: true,
  source: 'manual',
  tags: [],
  extra_data: {}
})

// 计算属性
const enabledChannels = computed(() => {
  return props.channels.filter(channel => channel.enabled)
})

// 监听标签输入
watch(tagsInput, (newValue) => {
  form.tags = newValue.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
})

// 监听目标渠道变化
watch(() => form.target_channels, (newChannels) => {
  selectAllChannels.value = newChannels.length === enabledChannels.value.length && enabledChannels.value.length > 0
})

// 方法
const toggleAllChannels = () => {
  if (selectAllChannels.value) {
    form.target_channels = enabledChannels.value.map(channel => channel.id)
  } else {
    form.target_channels = []
  }
}

const sendNotification = async () => {
  try {
    loading.value = true
    
    const notificationData = {
      title: form.title,
      content: form.content,
      notification_type: form.notification_type,
      priority: form.priority,
      target_channels: form.target_channels.length > 0 ? form.target_channels : null,
      format_markdown: form.format_markdown,
      source: form.source,
      tags: form.tags,
      extra_data: form.extra_data
    }
    
    emit('send', notificationData)
    
  } catch (error) {
    console.error('发送通知失败:', error)
  } finally {
    loading.value = false
  }
}

// 辅助方法
const getChannelTypeName = (type) => {
  const names = {
    telegram: 'Telegram',
    discord: 'Discord',
    wechat: '微信',
    webhook: 'Webhook'
  }
  return names[type] || type
}

const getPriorityName = (priority) => {
  const names = {
    low: '低',
    normal: '普通',
    high: '高',
    critical: '紧急'
  }
  return names[priority] || priority
}

const getPriorityColor = (priority) => {
  const colors = {
    low: 'bg-gray-400',
    normal: 'bg-blue-400',
    high: 'bg-yellow-400',
    critical: 'bg-red-400'
  }
  return colors[priority] || 'bg-gray-400'
}

const getNotificationTypeName = (type) => {
  const names = {
    whmcs_stock: 'WHMCS库存',
    whmcs_order: 'WHMCS订单',
    let_monitor: 'LET监控',
    let_match: 'LET智能匹配',
    system_status: '系统状态',
    error_alert: '错误警报',
    custom: '自定义'
  }
  return names[type] || type
}

// 重置表单（在组件显示时）
watch(() => props.visible, (visible) => {
  if (visible) {
    Object.assign(form, {
      title: '',
      content: '',
      notification_type: 'custom',
      priority: 'normal',
      target_channels: [],
      format_markdown: true,
      source: 'manual',
      tags: [],
      extra_data: {}
    })
    tagsInput.value = ''
    selectAllChannels.value = false
  }
})
</script>

<style scoped>
/* 确保模态框在其他元素之上 */
.fixed {
  z-index: 50;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style> 