<template>
  <header class="relative bg-white/80 dark:bg-gray-900/90 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50 shadow-lg shadow-gray-200/20 dark:shadow-gray-900/20">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-gradient-to-r from-blue-50/30 via-indigo-50/20 to-purple-50/30 dark:from-blue-900/5 dark:via-indigo-900/3 dark:to-purple-900/5"></div>
    <div class="relative px-6 py-3">
      <div class="flex items-center justify-between">
        <!-- 左侧：菜单按钮和标题 -->
        <div class="flex items-center space-x-4">
          <!-- 移动端菜单按钮 -->
          <button
            @click="store.isSidebarOpen = !store.isSidebarOpen"
            class="lg:hidden p-2.5 rounded-xl text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-white/60 dark:hover:bg-gray-800/60 backdrop-blur-sm border border-transparent hover:border-gray-200/60 dark:hover:border-gray-700/60 transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          
          <!-- 页面标题 -->
          <div class="flex items-center space-x-3">
            <div class="w-1 h-8 bg-gradient-to-b from-indigo-500 to-purple-600 rounded-full"></div>
            <h1 class="text-2xl font-bold bg-gradient-to-r from-gray-900 via-indigo-900 to-purple-900 dark:from-white dark:via-blue-100 dark:to-purple-100 bg-clip-text text-transparent">
              {{ currentPageTitle }}
            </h1>
          </div>
        </div>

        <!-- 右侧：操作按钮 -->
        <div class="flex items-center space-x-4">
          <!-- 监控状态控制中心 -->
          <div class="flex items-center space-x-3">
            <!-- 监控状态指示器 - 科技感设计 -->
            <div class="relative">
              <div 
                :class="[
                  'relative w-11 h-11 rounded-2xl border-2 transition-all duration-500 cursor-pointer group backdrop-blur-sm',
                  monitorStatus === 'running' 
                    ? 'border-green-400/60 bg-gradient-to-br from-green-50/80 to-emerald-100/80 dark:from-green-900/40 dark:to-emerald-900/40 shadow-xl shadow-green-200/30 dark:shadow-green-900/30 hover:shadow-2xl hover:shadow-green-200/40' 
                    : 'border-gray-300/60 dark:border-gray-600/60 bg-gradient-to-br from-gray-50/80 to-gray-100/80 dark:from-gray-800/60 dark:to-gray-700/60 shadow-lg hover:shadow-xl'
                ]"
                @click="toggleMonitor"
                :title="monitorStatus === 'running' ? '点击停止监控' : '点击启动监控'"
              >
                <!-- 中心图标 -->
                <div 
                  :class="[
                    'absolute inset-1 rounded-xl flex items-center justify-center transition-all duration-500 transform',
                    monitorStatus === 'running' 
                      ? 'bg-gradient-to-br from-green-400 to-emerald-500 scale-100 rotate-0' 
                      : 'bg-gradient-to-br from-gray-400 to-gray-500 scale-75 rotate-180'
                  ]"
                >
                  <!-- 监控图标 -->
                  <svg 
                    v-if="!isLoading"
                    :class="[
                      'w-5 h-5 transition-all duration-500',
                      monitorStatus === 'running' ? 'text-white animate-pulse' : 'text-gray-200'
                    ]" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path 
                      stroke-linecap="round" 
                      stroke-linejoin="round" 
                      stroke-width="2" 
                      :d="monitorStatus === 'running' ? 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z' : 'M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L17 17M15 12a3 3 0 11-6 0 3 3 0 016 0z'"
                    />
                  </svg>
                  <!-- 加载图标 -->
                  <svg 
                    v-else
                    class="w-5 h-5 text-white animate-spin" 
                    fill="none" 
                    viewBox="0 0 24 24" 
                    stroke="currentColor"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </div>
                
                <!-- 状态指示灯 -->
                <div 
                  :class="[
                    'absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full border border-white transition-all duration-300',
                    monitorStatus === 'running' 
                      ? 'bg-green-500 animate-ping' 
                      : 'bg-red-500'
                  ]"
                ></div>
                
                <!-- 能量波纹效果（仅在运行时显示） -->
                <div 
                  v-if="monitorStatus === 'running'"
                  class="absolute inset-0 rounded-full border border-green-400 animate-ping opacity-30"
                ></div>
              </div>
            </div>

            <!-- 主题切换控制器 - 创新设计 -->
            <div class="relative">
              <div 
                class="relative w-11 h-11 cursor-pointer group transition-all duration-500 transform hover:scale-110 backdrop-blur-sm"
                @click="store.toggleTheme"
                :title="theme === 'dark' ? '切换到亮色主题' : '切换到暗色主题'"
              >
                <!-- 动态背景容器 -->
                <div 
                  :class="[
                    'w-full h-full transition-all duration-700 transform rounded-2xl shadow-xl',
                    theme === 'dark' 
                      ? 'bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-800 rotate-0' 
                      : 'bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rotate-180'
                  ]"
                ></div>
                
                <!-- 主题图标容器 -->
                <div 
                  :class="[
                    'absolute inset-1 rounded-xl flex items-center justify-center transition-all duration-500',
                    theme === 'dark' 
                      ? 'bg-white/10 backdrop-blur-sm' 
                      : 'bg-white/20 backdrop-blur-sm'
                  ]"
                >
                  <!-- 太阳/月亮图标 -->
                  <div class="relative w-6 h-6">
                    <!-- 太阳图标 -->
                    <svg 
                      v-if="theme === 'light'"
                      class="w-6 h-6 text-white animate-spin-slow transition-all duration-500" 
                      fill="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z" />
                    </svg>
                    
                    <!-- 月亮图标 -->
                    <svg 
                      v-else
                      class="w-6 h-6 text-white animate-pulse transition-all duration-500" 
                      fill="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z" />
                    </svg>
                  </div>
                </div>
                
                <!-- 装饰性光晕 -->
                <div 
                  :class="[
                    'absolute inset-0 rounded-xl opacity-30 animate-pulse',
                    theme === 'dark' 
                      ? 'bg-gradient-to-br from-indigo-400 to-purple-600' 
                      : 'bg-gradient-to-br from-yellow-300 to-orange-400'
                  ]"
                ></div>
              </div>
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="h-8 w-px bg-gradient-to-b from-transparent via-gray-300/60 dark:via-gray-600/60 to-transparent"></div>

          <!-- 用户菜单 -->
          <div class="relative">
            <button
              @click="showUserMenu = !showUserMenu"
              class="flex items-center space-x-2 p-2.5 rounded-2xl text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-white/60 dark:hover:bg-gray-800/60 backdrop-blur-sm border border-transparent hover:border-gray-200/60 dark:hover:border-gray-700/60 transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-lg"
            >
              <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </button>

            <!-- 用户下拉菜单 -->
            <div
              v-if="showUserMenu"
              v-click-outside="() => showUserMenu = false"
              class="absolute right-0 mt-3 w-48 bg-white/90 dark:bg-gray-800/90 rounded-2xl shadow-2xl border border-gray-200/60 dark:border-gray-700/60 py-2 z-50 backdrop-blur-xl"
            >
              <button
                @click="logout"
                class="w-full flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100/60 dark:hover:bg-gray-700/60 transition-all duration-200 rounded-xl mx-1 backdrop-blur-sm"
              >
                <div class="p-1.5 bg-red-100 dark:bg-red-900/30 rounded-xl mr-3">
                  <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                </div>
                <span class="font-medium">退出登录</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useMainStore } from '@/stores/main'
import { toast } from '@/utils/helpers'

const route = useRoute()
const store = useMainStore()

// 响应式数据
const showUserMenu = ref(false)
const isLoading = ref(false)

// 计算属性
const theme = computed(() => store.theme)
const themeIcon = computed(() => store.themeIcon)
const monitorStatus = computed(() => store.monitorStatus)

const currentPageTitle = computed(() => {
  const titleMap = {
    'Dashboard': '仪表板',
    'Sites': '网站管理',
    'Products': '产品管理',
    'Settings': '系统设置'
  }
  return titleMap[route.name] || '管理面板'
})

// 方法
const toggleMonitor = async () => {
  isLoading.value = true
  try {
    if (monitorStatus.value === 'running') {
      await store.stopMonitor()
      toast('success', '监控已停止')
    } else {
      await store.startMonitor()
      toast('success', '监控已启动')
    }
  } catch (error) {
    toast('error', '操作失败')
  } finally {
    isLoading.value = false
  }
}

const logout = () => {
  showUserMenu.value = false
  store.logout()
}

// 点击外部关闭下拉菜单指令
const vClickOutside = {
  beforeMount(el, binding) {
    el.clickOutsideEvent = function(event) {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value()
      }
    }
    document.addEventListener('click', el.clickOutsideEvent)
  },
  unmounted(el) {
    document.removeEventListener('click', el.clickOutsideEvent)
  }
}
</script> 

<style scoped>
@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}
</style> 