<template>
  <div :class="['form-select-field', { 'has-error': hasError, 'is-focused': isFocused }]">
    <!-- 标签 -->
    <label 
      v-if="label" 
      :for="fieldId" 
      :class="[
        'form-label',
        required && 'required',
        hasError && 'text-red-600 dark:text-red-400',
        isFocused && 'text-indigo-600 dark:text-indigo-400'
      ]"
    >
      <span class="flex items-center">
        <slot name="prefix">
          <component v-if="prefixIcon" :is="prefixIcon" class="w-4 h-4 mr-2" />
        </slot>
        {{ label }}
        <span v-if="required" class="text-red-500 ml-1">*</span>
      </span>
    </label>

    <!-- 选择框容器 -->
    <div class="form-select-container">
      <!-- 选择框 -->
      <select
        :id="fieldId"
        :value="modelValue"
        :disabled="disabled"
        :required="required"
        :class="[
          'form-select',
          hasError && 'border-red-500 focus:border-red-500 focus:ring-red-500',
          !hasError && 'border-gray-300 dark:border-gray-600 focus:border-indigo-500 focus:ring-indigo-500'
        ]"
        @focus="handleFocus"
        @blur="handleBlur"
        @change="handleChange"
      >
        <option v-if="placeholder" value="" disabled>
          {{ placeholder }}
        </option>
        <option 
          v-for="option in options" 
          :key="option.value" 
          :value="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </option>
      </select>

      <!-- 下拉箭头 -->
      <div class="form-arrow">
        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>
    </div>

    <!-- 帮助文本和错误信息 -->
    <div class="form-message">
      <transition name="slide-down">
        <div v-if="hasError" class="form-error">
          <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {{ errorMessage }}
        </div>
      </transition>
      
      <div v-if="helpText && !hasError" class="form-help">
        {{ helpText }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'FormSelect',
  props: {
    modelValue: {
      type: [String, Number],
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择...'
    },
    options: {
      type: Array,
      default: () => [],
      validator: (options) => {
        return options.every(option => 
          typeof option === 'object' && 
          'value' in option && 
          'label' in option
        )
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    errorMessage: {
      type: String,
      default: ''
    },
    helpText: {
      type: String,
      default: ''
    },
    icon: {
      type: [String, Object],
      default: null
    },
    prefixIcon: {
      type: [String, Object],
      default: null
    }
  },
  emits: ['update:modelValue', 'focus', 'blur', 'change'],
  setup(props, { emit }) {
    const isFocused = ref(false)
    
    const fieldId = computed(() => {
      return `select-${Math.random().toString(36).substr(2, 9)}`
    })
    
    const hasError = computed(() => {
      return !!props.errorMessage
    })
    
    const handleFocus = (event) => {
      isFocused.value = true
      emit('focus', event)
    }
    
    const handleBlur = (event) => {
      isFocused.value = false
      emit('blur', event)
    }
    
    const handleChange = (event) => {
      emit('update:modelValue', event.target.value)
      emit('change', event)
    }
    
    return {
      isFocused,
      fieldId,
      hasError,
      handleFocus,
      handleBlur,
      handleChange
    }
  }
}
</script>

<style scoped>
.form-select-field {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors duration-200;
}

.form-label.required {
  @apply font-semibold;
}

.form-select-container {
  @apply relative;
}

.form-arrow {
  @apply absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10;
}

.form-select {
  @apply w-full py-3 px-4 pr-10 bg-white dark:bg-gray-700 border rounded-lg text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-offset-0 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform appearance-none cursor-pointer;
}

.form-select:focus {
  @apply shadow-md scale-[1.01];
}

.form-select-field.is-focused .form-select {
  @apply shadow-lg;
}

.form-select-field.has-error .form-select {
  @apply bg-red-50 dark:bg-red-900/20;
}

.form-message {
  @apply min-h-[1.25rem];
}

.form-error {
  @apply text-sm text-red-600 dark:text-red-400 flex items-start;
}

.form-help {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* 动画 */
.slide-down-enter-active {
  @apply transition-all duration-200 ease-out;
}

.slide-down-leave-active {
  @apply transition-all duration-150 ease-in;
}

.slide-down-enter-from {
  @apply opacity-0 transform -translate-y-1;
}

.slide-down-leave-to {
  @apply opacity-0 transform -translate-y-1;
}

/* 悬停效果 */
.form-select:hover:not(:disabled) {
  @apply border-gray-400 dark:border-gray-500;
}

.form-select-field:hover .form-label {
  @apply text-gray-900 dark:text-gray-100;
}

.form-select-field:hover .form-arrow svg {
  @apply text-gray-600 dark:text-gray-300;
}
</style>