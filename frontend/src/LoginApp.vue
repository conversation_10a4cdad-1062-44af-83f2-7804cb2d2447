<template>
  <div :class="['min-h-screen p-4 transition-colors duration-300', theme === 'dark' ? 'bg-animated-dark' : 'bg-animated-light']">
    <div class="flex items-center justify-center min-h-screen">
      <div class="w-full max-w-md">
        <!-- 主题切换按钮 -->
        <div class="flex justify-end mb-4">
          <button
            @click="toggleTheme"
            class="p-2 rounded-lg bg-white/10 dark:bg-gray-800/50 backdrop-blur-sm hover:bg-white/20 dark:hover:bg-gray-700/50 transition-all duration-200"
            :title="theme === 'dark' ? '切换到亮色主题' : '切换到暗色主题'"
          >
            <span class="text-xl">{{ theme === 'dark' ? '☀️' : '🌙' }}</span>
          </button>
        </div>

        <!-- 登录卡片 -->
        <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-2xl p-8 space-y-8 animate-fadeInUp border border-gray-200/50 dark:border-gray-700/50"
             :class="{ 'animate-shake': shakeForm }">
          
          <!-- 标题区域 -->
          <div class="text-center">
            <div class="mx-auto h-16 w-16 bg-indigo-600 dark:bg-indigo-500 rounded-full flex items-center justify-center mb-4 shadow-lg">
              <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">欢迎回来</h2>
            <p class="text-gray-600 dark:text-gray-400">请输入密码访问监控面板</p>
          </div>

          <!-- 登录表单 -->
          <form @submit.prevent="handleLogin" class="space-y-6">
            <!-- 密码输入框 -->
            <div class="relative">
              <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">密码</label>
              <div class="relative">
                <input 
                  id="password" 
                  :type="showPassword ? 'text' : 'password'" 
                  v-model="password" 
                  required
                  :disabled="isLoading" 
                  :class="[
                    'input-focus w-full px-4 py-3 pl-12 pr-12 bg-gray-50 dark:bg-gray-900/50 border rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-colors',
                    hasError ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 dark:border-gray-600'
                  ]" 
                  placeholder="请输入您的密码" 
                  @input="clearError" 
                  @keydown.enter="handleLogin"
                >

                <!-- 密码图标 -->
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>

                <!-- 显示/隐藏密码按钮 -->
                <button 
                  type="button" 
                  @click="togglePasswordVisibility" 
                  :disabled="isLoading"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 transition-colors"
                >
                  <svg v-if="showPassword" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                  <svg v-else class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </button>
              </div>

              <!-- 错误提示 -->
              <div v-if="hasError" class="mt-2 text-sm text-red-500 dark:text-red-400 flex items-center">
                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {{ errorMessage }}
              </div>
            </div>

            <!-- 登录按钮 -->
            <button 
              type="submit" 
              :disabled="isLoading || !password.trim()" 
              :class="[
                'btn-interactive w-full flex justify-center items-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 focus:ring-offset-white dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200',
                isLoading ? 'bg-indigo-600 dark:bg-indigo-500' : 'bg-indigo-600 dark:bg-indigo-500 hover:bg-indigo-700 dark:hover:bg-indigo-600'
              ]"
            >
              <!-- 加载状态 -->
              <div v-if="isLoading" class="flex items-center">
                <div class="spinner border-2 border-white border-t-transparent rounded-full mr-2"></div>
                正在登录...
              </div>

              <!-- 正常状态 -->
              <div v-else class="flex items-center">
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
                登录面板
              </div>
            </button>
          </form>

          <!-- 底部信息 -->
          <div class="text-center">
            <p class="text-xs text-gray-500 dark:text-gray-400">WHMCS库存监控系统 v1.0</p>
          </div>
        </div>

        <!-- 连接状态指示器 -->
        <div class="mt-4 text-center">
          <div class="inline-flex items-center text-sm text-gray-600 dark:text-gray-400">
            <div :class="['w-2 h-2 rounded-full mr-2', isOnline ? 'bg-green-400 animate-pulse-custom' : 'bg-red-400']"></div>
            {{ isOnline ? '服务器连接正常' : '服务器连接异常' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { toast } from '@/utils/api'

// 响应式数据
const password = ref('')
const isLoading = ref(false)
const showPassword = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const shakeForm = ref(false)
const isOnline = ref(true)
const theme = ref(localStorage.getItem('theme') || 'light')

// 检查服务器连接状态
const checkConnection = async () => {
  try {
    await fetch('/api/status', { method: 'HEAD' })
    isOnline.value = true
  } catch (error) {
    isOnline.value = false
  }
}

// 切换密码显示状态
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 清除错误状态
const clearError = () => {
  hasError.value = false
  errorMessage.value = ''
}

// 显示错误并触发震动动画
const showError = (message) => {
  hasError.value = true
  errorMessage.value = message
  shakeForm.value = true
  setTimeout(() => {
    shakeForm.value = false
  }, 500)
}

// 处理登录
const handleLogin = async () => {
  if (isLoading.value || !password.value.trim()) return

  clearError()
  isLoading.value = true

  try {
    const response = await fetch('/api/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: `password=${encodeURIComponent(password.value)}`
    })

    const result = await response.json()

    if (response.ok && result.success) {
      toast('success', '登录成功', '正在跳转到管理面板...')
      
      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        window.location.href = '/'
      }, 1000)
    } else {
      const errorMsg = result.detail || result.message || '登录失败'
      showError(errorMsg)
      toast('error', '登录失败', errorMsg)
      
      // 清空密码框
      password.value = ''
    }
  } catch (error) {
    console.error('Login error:', error)
    const errorMsg = '网络连接异常，请检查网络后重试'
    showError(errorMsg)
    toast('error', '连接失败', errorMsg)
    isOnline.value = false
  } finally {
    isLoading.value = false
  }
}

// 切换主题
const toggleTheme = () => {
  theme.value = theme.value === 'light' ? 'dark' : 'light'
  localStorage.setItem('theme', theme.value)
  applyTheme()
}

// 应用主题
const applyTheme = () => {
  if (theme.value === 'dark') {
    document.documentElement.classList.add('dark')
    document.body.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
    document.body.classList.remove('dark')
  }
}

// 组件挂载后
onMounted(() => {
  // 初始化主题
  applyTheme()
  
  checkConnection()
  // 定期检查连接状态
  setInterval(checkConnection, 30000)

  // 自动聚焦到密码输入框
  setTimeout(() => {
    document.getElementById('password')?.focus()
  }, 300)
})
</script> 