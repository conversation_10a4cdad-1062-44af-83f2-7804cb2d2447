<template>
  <div class="space-y-4">
    <!-- 欢迎标题区域 -->
    <div class="relative overflow-hidden bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 rounded-2xl p-4 text-white">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 bg-black/20"></div>
      <div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-indigo-600/20 backdrop-blur-sm"></div>
      <div class="absolute top-0 right-0 -translate-y-12 translate-x-12 w-64 h-64 bg-white/10 rounded-full blur-3xl"></div>
      <div class="absolute bottom-0 left-0 translate-y-12 -translate-x-12 w-48 h-48 bg-white/5 rounded-full blur-2xl"></div>
      
      <div class="relative z-10">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-bold mb-1 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
              集成监控管理系统
            </h1>
            <p class="text-blue-100 text-sm mb-2">WHMCS库存监控 + LET RSS监控，实时预警、智能管理</p>
            <div class="flex items-center space-x-6">
              <div class="flex items-center space-x-2">
                <div :class="['w-3 h-3 rounded-full', isRunning ? 'bg-green-400 animate-pulse shadow-lg shadow-green-400/50' : 'bg-red-400 animate-pulse shadow-lg shadow-red-400/50']">
                </div>
                <span class="text-sm font-medium">
                  {{ isRunning ? '系统运行中' : '系统已停止' }}
                </span>
              </div>
              <div class="text-sm text-blue-100">
                上次更新: {{ new Date().toLocaleTimeString('zh-CN') }}
              </div>
            </div>
          </div>
          <div class="hidden md:flex items-center space-x-4">
            <div class="p-2 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 增强的状态概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
      <!-- 监控状态卡片 - 增强版 -->
      <div class="group relative overflow-hidden bg-gradient-to-br from-white via-blue-50/30 to-indigo-100/20 dark:from-gray-800 dark:via-blue-900/10 dark:to-indigo-900/20 rounded-2xl shadow-xl border border-blue-200/30 dark:border-blue-700/30 transition-all duration-500 hover:shadow-2xl hover:scale-[1.05] hover:-translate-y-2">
        <!-- 动态背景 -->
        <div class="absolute inset-0">
          <div class="absolute inset-0 bg-gradient-to-br from-blue-500/[0.03] via-indigo-500/[0.02] to-purple-500/[0.03] dark:from-blue-400/[0.05] dark:via-indigo-400/[0.03] dark:to-purple-400/[0.05]"></div>
          <div class="absolute top-0 right-0 -translate-y-6 translate-x-6 w-24 h-24 bg-blue-400/10 rounded-full blur-xl animate-pulse"></div>
          <div class="absolute bottom-0 left-0 translate-y-6 -translate-x-6 w-20 h-20 bg-indigo-400/10 rounded-full blur-lg animate-pulse" style="animation-delay: 1s"></div>
        </div>
        
        <div class="relative p-4">
          <!-- 头部图标区域 -->
          <div class="flex items-center justify-between mb-3">
            <div class="relative">
              <div class="p-2.5 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg transform transition-transform group-hover:scale-110 group-hover:rotate-3">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <!-- 脉冲效果 -->
                <div v-if="isRunning" class="absolute inset-0 bg-blue-400 rounded-2xl animate-ping opacity-20"></div>
              </div>
            </div>
            
            <!-- 状态指示器 -->
            <div class="relative">
              <div :class="[
                'w-4 h-4 rounded-full transition-all duration-300 shadow-lg',
                isRunning 
                  ? 'bg-green-400 animate-pulse shadow-green-400/50' 
                  : 'bg-red-400 animate-pulse shadow-red-400/50'
              ]">
                <div :class="[
                  'absolute inset-0 rounded-full animate-ping',
                  isRunning ? 'bg-green-400' : 'bg-red-400'
                ]" style="animation-duration: 2s"></div>
              </div>
            </div>
          </div>
          
          <!-- 文本内容 -->
          <div class="space-y-3">
            <h3 class="text-lg font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">监控状态</h3>
            
            <div class="flex items-baseline space-x-3">
              <p class="text-3xl font-black tracking-tight" :class="isRunning ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                {{ isRunning ? '运行中' : '已停止' }}
              </p>
              <div class="flex items-center space-x-1">
                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <div class="w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 0.5s"></div>
                <div class="w-1 h-1 bg-blue-300 rounded-full animate-pulse" style="animation-delay: 1s"></div>
              </div>
            </div>
            
            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
              {{ isRunning ? '系统正在实时监控库存状态，为您提供7x24小时服务' : '监控服务已停止，请启动监控服务' }}
            </p>
            
            <!-- 进度条 -->
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
              <div :class="[
                'h-full rounded-full transition-all duration-1000 ease-out',
                isRunning 
                  ? 'bg-gradient-to-r from-green-400 to-emerald-500 w-full' 
                  : 'bg-gradient-to-r from-red-400 to-red-500 w-0'
              ]"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 有货产品卡片 - 增强版 -->
      <div class="group relative overflow-hidden bg-gradient-to-br from-white via-emerald-50/30 to-green-100/20 dark:from-gray-800 dark:via-emerald-900/10 dark:to-green-900/20 rounded-2xl shadow-xl border border-emerald-200/30 dark:border-emerald-700/30 transition-all duration-500 hover:shadow-2xl hover:scale-[1.05] hover:-translate-y-2">
        <!-- 动态背景 -->
        <div class="absolute inset-0">
          <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/[0.03] via-green-500/[0.02] to-teal-500/[0.03] dark:from-emerald-400/[0.05] dark:via-green-400/[0.03] dark:to-teal-400/[0.05]"></div>
          <div class="absolute top-0 right-0 -translate-y-8 translate-x-8 w-28 h-28 bg-emerald-400/10 rounded-full blur-xl animate-pulse" style="animation-delay: 0.5s"></div>
        </div>
        
        <div class="relative p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="relative">
              <div class="p-2.5 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl shadow-lg transform transition-transform group-hover:scale-110 group-hover:rotate-3">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <div class="absolute inset-0 bg-emerald-400 rounded-2xl animate-ping opacity-20"></div>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-emerald-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-green-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              <div class="w-1.5 h-1.5 bg-teal-400 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
            </div>
          </div>
          
          <div class="space-y-3">
            <h3 class="text-lg font-bold text-gray-900 dark:text-white group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors">有货产品</h3>
            
            <div class="flex items-baseline space-x-2">
              <p class="text-3xl font-black text-emerald-600 dark:text-emerald-400 tracking-tight">
                {{ status.in_stock_products || 0 }}
              </p>
              <div class="flex flex-col">
                <span class="text-xs text-emerald-600 dark:text-emerald-400 font-semibold">款</span>
                <div class="flex items-center space-x-1 mt-1">
                  <svg class="w-3 h-3 text-emerald-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                  <span class="text-xs text-emerald-500 font-medium">可购买</span>
                </div>
              </div>
            </div>
            
            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
              当前库存充足且可立即购买的产品
            </p>
            
            <!-- 数据可视化 -->
            <div class="flex items-center justify-between pt-2">
              <div class="flex space-x-1">
                <div class="w-2 h-8 bg-emerald-300 rounded-full"></div>
                <div class="w-2 h-6 bg-emerald-400 rounded-full"></div>
                <div class="w-2 h-10 bg-emerald-500 rounded-full"></div>
                <div class="w-2 h-4 bg-emerald-300 rounded-full"></div>
                <div class="w-2 h-7 bg-emerald-400 rounded-full"></div>
              </div>
              <div class="text-xs text-emerald-600 dark:text-emerald-400 font-semibold bg-emerald-100 dark:bg-emerald-900/30 px-2 py-1 rounded-full">
                +{{ Math.floor(Math.random() * 10) }}%
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 网站数量卡片 - 增强版 -->
      <div class="group relative overflow-hidden bg-gradient-to-br from-white via-purple-50/30 to-pink-100/20 dark:from-gray-800 dark:via-purple-900/10 dark:to-pink-900/20 rounded-2xl shadow-xl border border-purple-200/30 dark:border-purple-700/30 transition-all duration-500 hover:shadow-2xl hover:scale-[1.05] hover:-translate-y-2">
        <!-- 动态背景 -->
        <div class="absolute inset-0">
          <div class="absolute inset-0 bg-gradient-to-br from-purple-500/[0.03] via-pink-500/[0.02] to-rose-500/[0.03] dark:from-purple-400/[0.05] dark:via-pink-400/[0.03] dark:to-rose-400/[0.05]"></div>
          <div class="absolute top-0 right-0 -translate-y-10 translate-x-10 w-32 h-32 bg-purple-400/10 rounded-full blur-2xl animate-pulse" style="animation-delay: 1s"></div>
        </div>
        
        <div class="relative p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="relative">
              <div class="p-2.5 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl shadow-lg transform transition-transform group-hover:scale-110 group-hover:rotate-3">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <div class="absolute inset-0 bg-purple-400 rounded-2xl animate-ping opacity-20"></div>
              </div>
            </div>
            
            <div class="grid grid-cols-2 gap-1">
              <div class="w-2 h-2 bg-purple-400 rounded-sm animate-pulse"></div>
              <div class="w-2 h-2 bg-pink-400 rounded-sm animate-pulse" style="animation-delay: 0.3s"></div>
              <div class="w-2 h-2 bg-purple-300 rounded-sm animate-pulse" style="animation-delay: 0.6s"></div>
              <div class="w-2 h-2 bg-pink-300 rounded-sm animate-pulse" style="animation-delay: 0.9s"></div>
            </div>
          </div>
          
          <div class="space-y-3">
            <h3 class="text-lg font-bold text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">网站数量</h3>
            
            <div class="flex items-baseline space-x-2">
              <p class="text-3xl font-black text-purple-600 dark:text-purple-400 tracking-tight">
                {{ status.total_sites || 0 }}
              </p>
              <div class="flex flex-col items-center">
                <span class="text-xs text-purple-600 dark:text-purple-400 font-semibold">个</span>
                <svg class="w-4 h-4 text-purple-500 mt-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" />
                </svg>
              </div>
            </div>
            
            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
              正在实时监控的WHMCS网站总数
            </p>
            
            <!-- 网站状态指示 -->
            <div class="flex items-center justify-between pt-2">
              <div class="flex space-x-2">
                <div class="flex items-center space-x-1">
                  <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span class="text-xs text-gray-600 dark:text-gray-400">在线</span>
                </div>
                <div class="flex items-center space-x-1">
                  <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span class="text-xs text-gray-600 dark:text-gray-400">等待</span>
                </div>
              </div>
              <div class="text-xs text-purple-600 dark:text-purple-400 font-semibold bg-purple-100 dark:bg-purple-900/30 px-2 py-1 rounded-full">
                全部正常
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品总数卡片 - 增强版 -->
      <div class="group relative overflow-hidden bg-gradient-to-br from-white via-orange-50/30 to-red-100/20 dark:from-gray-800 dark:via-orange-900/10 dark:to-red-900/20 rounded-2xl shadow-xl border border-orange-200/30 dark:border-orange-700/30 transition-all duration-500 hover:shadow-2xl hover:scale-[1.05] hover:-translate-y-2">
        <!-- 动态背景 -->
        <div class="absolute inset-0">
          <div class="absolute inset-0 bg-gradient-to-br from-orange-500/[0.03] via-red-500/[0.02] to-pink-500/[0.03] dark:from-orange-400/[0.05] dark:via-red-400/[0.03] dark:to-pink-400/[0.05]"></div>
          <div class="absolute bottom-0 left-0 translate-y-8 -translate-x-8 w-24 h-24 bg-orange-400/10 rounded-full blur-xl animate-pulse" style="animation-delay: 1.5s"></div>
          <div class="absolute top-0 right-0 -translate-y-4 translate-x-4 w-16 h-16 bg-red-400/10 rounded-full blur-lg animate-pulse" style="animation-delay: 0.8s"></div>
        </div>
        
        <div class="relative p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="relative">
              <div class="p-2.5 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl shadow-lg transform transition-transform group-hover:scale-110 group-hover:rotate-3">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                <div class="absolute inset-0 bg-orange-400 rounded-2xl animate-ping opacity-20"></div>
              </div>
            </div>
            
            <div class="flex flex-col space-y-1">
              <div class="flex space-x-1">
                <div class="w-1.5 h-1.5 bg-orange-400 rounded-full animate-pulse"></div>
                <div class="w-1.5 h-1.5 bg-red-400 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
              </div>
              <div class="flex space-x-1">
                <div class="w-1.5 h-1.5 bg-orange-300 rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
                <div class="w-1.5 h-1.5 bg-red-300 rounded-full animate-pulse" style="animation-delay: 0.6s"></div>
              </div>
            </div>
          </div>
          
          <div class="space-y-3">
            <h3 class="text-lg font-bold text-gray-900 dark:text-white group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors">产品总数</h3>
            
            <div class="flex items-baseline space-x-2">
              <p class="text-3xl font-black text-orange-600 dark:text-orange-400 tracking-tight">
                {{ status.total_products || 0 }}
              </p>
              <div class="flex flex-col items-center">
                <span class="text-xs text-orange-600 dark:text-orange-400 font-semibold">种</span>
                <svg class="w-4 h-4 text-orange-500 mt-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h4v-6h4v6h4a1 1 0 001-1V7l-7-5z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
            
            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
              跨所有网站的产品类目总数量
            </p>
            
            <!-- 产品分类显示 -->
            <div class="flex items-center justify-between pt-2">
              <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-1">
                  <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span class="text-xs text-gray-600 dark:text-gray-400">可用</span>
                </div>
                <div class="flex items-center space-x-1">
                  <div class="w-2 h-2 bg-orange-400 rounded-full"></div>
                  <span class="text-xs text-gray-600 dark:text-gray-400">监控</span>
                </div>
              </div>
              <div class="text-xs text-orange-600 dark:text-orange-400 font-semibold bg-orange-100 dark:bg-orange-900/30 px-2 py-1 rounded-full">
                多样化
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- LET监控卡片 - 新增 -->
      <div class="group relative overflow-hidden bg-gradient-to-br from-white via-teal-50/30 to-cyan-100/20 dark:from-gray-800 dark:via-teal-900/10 dark:to-cyan-900/20 rounded-2xl shadow-xl border border-teal-200/30 dark:border-teal-700/30 transition-all duration-500 hover:shadow-2xl hover:scale-[1.05] hover:-translate-y-2">
        <!-- 动态背景 -->
        <div class="absolute inset-0">
          <div class="absolute inset-0 bg-gradient-to-br from-teal-500/[0.03] via-cyan-500/[0.02] to-blue-500/[0.03] dark:from-teal-400/[0.05] dark:via-cyan-400/[0.03] dark:to-blue-400/[0.05]"></div>
          <div class="absolute bottom-0 right-0 translate-y-6 translate-x-6 w-20 h-20 bg-teal-400/10 rounded-full blur-xl animate-pulse" style="animation-delay: 2s"></div>
          <div class="absolute top-0 left-0 -translate-y-4 -translate-x-4 w-16 h-16 bg-cyan-400/10 rounded-full blur-lg animate-pulse" style="animation-delay: 1.2s"></div>
        </div>
        
        <div class="relative p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="relative">
              <div class="p-2.5 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-2xl shadow-lg transform transition-transform group-hover:scale-110 group-hover:rotate-3">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <div class="absolute inset-0 bg-teal-400 rounded-2xl animate-ping opacity-20"></div>
              </div>
            </div>
            
            <div class="flex items-center space-x-1">
              <div :class="['w-3 h-3 rounded-full transition-all duration-300', (status.let_monitor_running || status.let_processed_posts > 0) ? 'bg-green-400 animate-pulse' : 'bg-gray-400']"></div>
              <span class="text-xs font-medium" :class="(status.let_monitor_running || status.let_processed_posts > 0) ? 'text-green-600 dark:text-green-400' : 'text-gray-500'">
                {{ (status.let_monitor_running || status.let_processed_posts > 0) ? '活跃' : '待机' }}
              </span>
            </div>
          </div>
          
          <div class="space-y-3">
            <h3 class="text-lg font-bold text-gray-900 dark:text-white group-hover:text-teal-600 dark:group-hover:text-teal-400 transition-colors">LET监控</h3>
            
            <div class="flex items-baseline space-x-2">
              <p class="text-3xl font-black text-teal-600 dark:text-teal-400 tracking-tight">
                {{ status.let_stored_posts || 0 }}
              </p>
              <div class="flex flex-col items-center">
                <span class="text-xs text-teal-600 dark:text-teal-400 font-semibold">帖</span>
                <svg class="w-4 h-4 text-teal-500 mt-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                  <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z" />
                </svg>
              </div>
            </div>
            
            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
              已监控并存储的LET优惠帖子
            </p>
            
            <!-- LET状态显示 -->
            <div class="flex items-center justify-between pt-2">
              <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-1">
                  <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span class="text-xs text-gray-600 dark:text-gray-400">RSS</span>
                </div>
                <div class="flex items-center space-x-1">
                  <div class="w-2 h-2 bg-teal-400 rounded-full"></div>
                  <span class="text-xs text-gray-600 dark:text-gray-400">解析</span>
                </div>
              </div>
              <div class="text-xs text-teal-600 dark:text-teal-400 font-semibold bg-teal-100 dark:bg-teal-900/30 px-2 py-1 rounded-full">
                {{ (status.let_monitor_running || status.let_processed_posts > 0) ? '运行中' : '离线' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时日志 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">实时日志</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">系统运行状态和监控信息</p>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <span class="text-xs text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded-full">
              {{ logs.length }} 条记录
            </span>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span class="text-sm text-gray-500 dark:text-gray-400">实时更新</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="logs-container relative">
        <div class="p-4 max-h-48 overflow-y-auto bg-gray-50 dark:bg-gray-900">
          <div v-if="logs.length === 0" class="text-center py-8">
            <div class="w-12 h-12 mx-auto mb-4 bg-gray-200 dark:bg-gray-800 rounded-full flex items-center justify-center">
              <svg class="w-6 h-6 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <p class="text-gray-500 dark:text-gray-400 font-medium">暂无日志记录</p>
            <p class="text-gray-400 dark:text-gray-500 text-sm mt-1">启动监控后将显示实时日志信息</p>
          </div>
          <div v-else class="space-y-1">
            <div 
              v-for="(log, index) in logs.slice(-50).reverse()" 
              :key="index"
              class="log-item group flex items-start space-x-3 py-2 px-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800/30 transition-all duration-200 border-l-2 border-transparent hover:border-blue-500/50 dark:hover:border-green-500/50"
            >
              <!-- 时间戳 -->
              <div class="flex-shrink-0 flex flex-col items-center">
                <div class="w-2 h-2 bg-blue-500 dark:bg-green-400 rounded-full opacity-60 group-hover:opacity-100 transition-opacity"></div>
                <div class="w-px h-4 bg-gray-300 dark:bg-gray-700 mt-1 opacity-40"></div>
              </div>
              
              <!-- 日志内容 -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2 mb-1">
                  <span class="timestamp inline-flex items-center px-2 py-1 rounded-md text-xs font-mono bg-blue-500/10 dark:bg-blue-500/10 text-blue-600 dark:text-blue-400 border border-blue-500/20">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {{ formatLogTime(log) }}
                  </span>
                  <span :class="getLogLevelBadge(log)" class="text-xs font-medium px-2 py-1 rounded-md">
                    {{ getLogLevel(log) }}
                  </span>
                </div>
                <div class="log-message text-sm text-gray-700 dark:text-gray-300 leading-relaxed group-hover:text-gray-900 dark:group-hover:text-gray-200 transition-colors">
                  {{ formatLogMessage(log) }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 渐变遮罩，表示可滚动 -->
        <div class="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-gray-50 dark:from-gray-900 via-gray-50/80 dark:via-gray-900/80 to-transparent pointer-events-none"></div>
        <div class="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-gray-50 dark:from-gray-900 via-gray-50/80 dark:via-gray-900/80 to-transparent pointer-events-none"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMainStore } from '../stores/main'
import { toast } from '../utils/helpers'

export default {
  name: 'Dashboard',
  setup() {
    const store = useMainStore()
    
    // 直接使用store中的数据，避免重复API调用
    const status = computed(() => store.status)
    const logs = computed(() => store.logs)
    const isLoading = computed(() => store.isLoading)
    
    // 计算监控运行状态
    const isRunning = computed(() => {
      return status.value.is_running || status.value.monitor_running || store.monitorStatus === 'running' || false
    })
    
    // 优化的时间格式化函数
    const formatLogTime = (log) => {
      if (!log) return 'N/A'
      
      // 尝试获取时间戳
      let timeValue = null
      if (log.timestamp) timeValue = log.timestamp
      else if (log.time) timeValue = log.time
      else if (log.created_at) timeValue = log.created_at
      else if (log.datetime) timeValue = log.datetime
      else if (typeof log === 'string' && log.includes(':')) {
        // 如果日志本身包含时间格式
        const timeMatch = log.match(/(\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2})/);
        if (timeMatch) timeValue = timeMatch[1]
      }
      
      if (!timeValue) return new Date().toLocaleTimeString('zh-CN')
      
      try {
        const date = new Date(timeValue)
        if (isNaN(date.getTime())) {
          // 如果无法解析，尝试其他格式
          return timeValue.toString()
        }
        
        const now = new Date()
        const diff = now - date
        
        // 如果是今天的日志，只显示时间
        if (diff < 86400000 && date.toDateString() === now.toDateString()) {
          return date.toLocaleTimeString('zh-CN', { 
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          })
        }
        
        // 否则显示完整日期时间
        return date.toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      } catch (error) {
        return timeValue.toString()
      }
    }
    
    const formatLogMessage = (log) => {
      if (!log) return ''
      
      // 如果log是字符串且包含时间，提取消息部分
      if (typeof log === 'string') {
        // 移除时间戳前缀
        const withoutTimestamp = log.replace(/^\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2}[^\s]*\s*/, '')
        return withoutTimestamp || log
      }
      
      // 如果是对象，尝试获取消息字段
      return log.message || log.msg || log.content || log.text || String(log)
    }
    
    // 获取日志级别
    const getLogLevel = (log) => {
      if (!log) return 'INFO'
      
      const message = typeof log === 'string' ? log : (log.message || log.msg || String(log))
      const upperMessage = message.toUpperCase()
      
      if (upperMessage.includes('ERROR') || upperMessage.includes('失败') || upperMessage.includes('错误')) {
        return 'ERROR'
      } else if (upperMessage.includes('WARNING') || upperMessage.includes('WARN') || upperMessage.includes('警告')) {
        return 'WARN'
      } else if (upperMessage.includes('SUCCESS') || upperMessage.includes('成功') || upperMessage.includes('完成')) {
        return 'SUCCESS'
      } else if (upperMessage.includes('DEBUG')) {
        return 'DEBUG'
      } else {
        return 'INFO'
      }
    }
    
    // 获取日志级别样式
    const getLogLevelBadge = (log) => {
      const level = getLogLevel(log)
      
      switch (level) {
        case 'ERROR':
          return 'bg-red-500/10 text-red-600 dark:text-red-400 border border-red-500/20'
        case 'WARN':
          return 'bg-yellow-500/10 text-yellow-600 dark:text-yellow-400 border border-yellow-500/20'
        case 'SUCCESS':
          return 'bg-green-500/10 text-green-600 dark:text-green-400 border border-green-500/20'
        case 'DEBUG':
          return 'bg-gray-500/10 text-gray-600 dark:text-gray-400 border border-gray-500/20'
        default:
          return 'bg-blue-500/10 text-blue-600 dark:text-blue-400 border border-blue-500/20'
      }
    }
    
    // 组件挂载时，如果数据为空，异步加载但不阻塞渲染
    onMounted(async () => {
      // 如果store中没有数据，异步加载但不等待
      if (!status.value || Object.keys(status.value).length === 0) {
        store.getStatus().catch(console.error)
      }
      if (!logs.value || logs.value.length === 0) {
        store.getLogs().catch(console.error)
      }
    })
    
    return {
      status,
      logs,
      isLoading,
      isRunning,
      formatLogTime,
      formatLogMessage,
      getLogLevel,
      getLogLevelBadge
    }
  }
}
</script>

<style scoped>
.logs-container {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
  font-size: 13px;
  line-height: 1.6;
}

.timestamp {
  font-weight: 600;
  font-variant-numeric: tabular-nums;
}

.log-message {
  word-break: break-word;
  white-space: pre-wrap;
}

.log-item {
  position: relative;
}

.log-item::before {
  content: '';
  position: absolute;
  left: 13px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(to bottom, transparent 0%, rgba(59, 130, 246, 0.3) 20%, rgba(59, 130, 246, 0.3) 80%, transparent 100%);
}

.dark .log-item::before {
  background: linear-gradient(to bottom, transparent 0%, rgba(34, 197, 94, 0.2) 20%, rgba(34, 197, 94, 0.2) 80%, transparent 100%);
}

.log-item:last-child::before {
  background: linear-gradient(to bottom, transparent 0%, rgba(59, 130, 246, 0.3) 20%, transparent 70%);
}

.dark .log-item:last-child::before {
  background: linear-gradient(to bottom, transparent 0%, rgba(34, 197, 94, 0.2) 20%, transparent 70%);
}

/* 自定义滚动条 */
.logs-container::-webkit-scrollbar {
  width: 6px;
}

.logs-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.dark .logs-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

.logs-container::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.4);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.dark .logs-container::-webkit-scrollbar-thumb {
  background: rgba(34, 197, 94, 0.3);
}

.logs-container::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.6);
}

.dark .logs-container::-webkit-scrollbar-thumb:hover {
  background: rgba(34, 197, 94, 0.5);
}

/* 动画效果 */
@keyframes logEntry {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.log-item:last-child {
  animation: logEntry 0.3s ease-out;
}
</style> 