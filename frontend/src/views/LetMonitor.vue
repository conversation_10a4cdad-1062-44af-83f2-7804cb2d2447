<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="relative overflow-hidden bg-gradient-to-r from-teal-600 via-cyan-600 to-blue-600 rounded-2xl p-6 text-white">
      <div class="absolute inset-0 bg-black/20"></div>
      <div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-cyan-600/20 to-teal-600/20 backdrop-blur-sm"></div>
      <div class="absolute top-0 right-0 -translate-y-6 translate-x-6 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
      <div class="relative z-10 flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold mb-2 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            LET RSS 监控
          </h1>
          <p class="text-blue-100">监控 LowEndTalk 论坛的服务器优惠信息，智能解析和通知</p>
        </div>
        
        <!-- 测试RSS获取按钮 -->
        <button
          @click="testRssFetch"
          :disabled="isLoading.testRss"
          class="relative px-6 py-3 bg-white/20 hover:bg-white/30 disabled:opacity-50 text-white rounded-xl shadow-lg backdrop-blur-sm transition-all duration-200 transform hover:scale-105 border border-white/30"
        >
          <div class="flex items-center">
            <svg v-if="isLoading.testRss" class="animate-spin h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" class="opacity-25"></circle>
              <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" class="opacity-75"></path>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {{ isLoading.testRss ? '测试中...' : '测试RSS获取' }}
          </div>
        </button>
      </div>
    </div>

    <!-- 状态卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- 监控状态 -->
      <div class="group bg-gradient-to-br from-white via-teal-50/30 to-cyan-100/20 dark:from-gray-800 dark:via-teal-900/10 dark:to-cyan-900/20 rounded-xl shadow-lg border border-teal-200/30 dark:border-teal-700/30 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] hover:-translate-y-1 p-4 relative overflow-hidden">
        <div class="absolute inset-0">
          <div class="absolute top-0 right-0 -translate-y-6 translate-x-6 w-24 h-24 bg-teal-400/10 rounded-full blur-xl animate-pulse"></div>
        </div>
        
        <div class="relative z-10">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-base font-semibold text-gray-900 dark:text-white mb-1">监控状态</h3>
              <p class="text-xl font-bold" :class="letStatus.enabled && letStatus.running ? 'text-emerald-500' : 'text-red-500'">
                {{ letStatus.enabled ? (letStatus.running ? '运行中' : '已停止') : '未启用' }}
              </p>
              <div class="mt-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                <div class="h-1.5 rounded-full transition-all duration-500" 
                     :class="letStatus.enabled && letStatus.running ? 'bg-emerald-500 w-full' : 'bg-red-500 w-1/3'"></div>
              </div>
            </div>
            <div class="p-3 rounded-xl transform group-hover:rotate-12 transition-transform duration-300" 
                 :class="letStatus.enabled && letStatus.running ? 'bg-emerald-100 dark:bg-emerald-900/50' : 'bg-red-100 dark:bg-red-900/50'">
              <svg class="w-8 h-8" :class="letStatus.enabled && letStatus.running ? 'text-emerald-600 dark:text-emerald-400' : 'text-red-600 dark:text-red-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- 已处理帖子 -->
      <div class="group bg-gradient-to-br from-white via-blue-50/30 to-indigo-100/20 dark:from-gray-800 dark:via-blue-900/10 dark:to-indigo-900/20 rounded-xl shadow-lg border border-blue-200/30 dark:border-blue-700/30 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] hover:-translate-y-1 p-4 relative overflow-hidden">
        <div class="absolute inset-0">
          <div class="absolute top-0 right-0 -translate-y-6 translate-x-6 w-20 h-20 bg-blue-400/10 rounded-full blur-xl animate-pulse"></div>
        </div>
        
        <div class="relative z-10">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-base font-semibold text-gray-900 dark:text-white mb-1">已处理帖子</h3>
              <p class="text-xl font-bold text-blue-500">{{ letStatus.processed_posts || 0 }}</p>
              <p class="text-xs text-gray-600 dark:text-gray-400 mt-0.5">总计处理数量</p>
            </div>
            <div class="p-3 bg-blue-100 dark:bg-blue-900/50 rounded-xl transform group-hover:rotate-12 transition-transform duration-300">
              <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- 存储帖子 -->
      <div class="group bg-gradient-to-br from-white via-green-50/30 to-emerald-100/20 dark:from-gray-800 dark:via-green-900/10 dark:to-emerald-900/20 rounded-xl shadow-lg border border-green-200/30 dark:border-green-700/30 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] hover:-translate-y-1 p-4 relative overflow-hidden">
        <div class="absolute inset-0">
          <div class="absolute top-0 right-0 -translate-y-6 translate-x-6 w-20 h-20 bg-green-400/10 rounded-full blur-xl animate-pulse"></div>
        </div>
        
        <div class="relative z-10">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-base font-semibold text-gray-900 dark:text-white mb-1">存储帖子</h3>
              <p class="text-xl font-bold text-green-500">{{ letStatus.stored_posts || 0 }}</p>
              <p class="text-xs text-gray-600 dark:text-gray-400 mt-0.5">本地存储文件</p>
            </div>
            <div class="p-3 bg-green-100 dark:bg-green-900/50 rounded-xl transform group-hover:rotate-12 transition-transform duration-300">
              <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 配置区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- LET监控配置 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div>
              <h3 class="text-base font-semibold text-gray-900 dark:text-white">LET监控配置</h3>
              <p class="text-xs text-gray-500 dark:text-gray-400">配置RSS监控参数</p>
            </div>
          </div>
        </div>
        
        <form @submit.prevent="saveLetConfig" class="p-4 space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              检查间隔 (秒)
            </label>
            <input 
              v-model.number="letConfig.check_interval" 
              type="number" 
              min="60" 
              max="7200"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="建议: 300-1800秒"
            />
            <p class="mt-0.5 text-xs text-gray-500 dark:text-gray-400">LET RSS检查的时间间隔</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              数据存储目录
            </label>
            <input 
              v-model="letConfig.data_dir" 
              type="text" 
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="data/let_posts"
            />
            <p class="mt-0.5 text-xs text-gray-500 dark:text-gray-400">LET帖子数据的存储目录</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              代理URL (可选)
            </label>
            <input 
              v-model="letConfig.proxy_url" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="http://127.0.0.1:7890"
            />
            <p class="mt-0.5 text-xs text-gray-500 dark:text-gray-400">如需使用代理访问RSS，请设置代理URL</p>
          </div>

          <div class="pt-3">
            <button 
              type="submit"
              :disabled="isLoading.saveConfig"
              class="w-full flex items-center justify-center px-4 py-2.5 bg-gradient-to-r from-teal-500 to-cyan-600 hover:from-teal-600 hover:to-cyan-700 disabled:opacity-50 text-white rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200"
            >
              <svg v-if="isLoading.saveConfig" class="animate-spin h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" class="opacity-25"></circle>
                <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" class="opacity-75"></path>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              {{ isLoading.saveConfig ? '保存中...' : '保存配置' }}
            </button>
          </div>
        </form>
      </div>

      <!-- 最近帖子预览 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h3 class="text-base font-semibold text-gray-900 dark:text-white">最近帖子</h3>
              <p class="text-xs text-gray-500 dark:text-gray-400">已解析的优惠信息</p>
            </div>
          </div>
        </div>
        
        <div class="p-4">
          <!-- 帖子统计 -->
          <div v-if="!isLoading.posts && posts.length > 0" class="flex items-center justify-between mb-3 p-2 bg-gradient-to-r from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 rounded-lg border border-teal-200 dark:border-teal-700">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-teal-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span class="text-sm font-medium text-teal-700 dark:text-teal-300">
                已显示 {{ posts.length }} / {{ totalPosts }} 个帖子
              </span>
            </div>
            <button 
              v-if="hasMorePosts"
              @click.prevent="loadMorePosts"
              :disabled="isLoading.loadingMore"
              class="flex items-center px-3 py-1 bg-teal-500 hover:bg-teal-600 disabled:opacity-50 text-white text-xs rounded-md transition-colors"
            >
              <svg v-if="isLoading.loadingMore" class="animate-spin h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" class="opacity-25"></circle>
                <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" class="opacity-75"></path>
              </svg>
              <svg v-else class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
              {{ isLoading.loadingMore ? '加载中' : '加载更多' }}
            </button>
          </div>

          <div v-if="isLoading.posts" class="flex justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
          
          <div v-else-if="posts.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-12 h-12 mx-auto mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p class="text-lg font-medium">暂无帖子数据</p>
            <p class="text-sm mt-1">LET监控尚未发现任何帖子</p>
          </div>
          
          <!-- 帖子列表 - 固定高度滚动 -->
          <div v-else class="h-72 overflow-y-auto space-y-2 pr-2">
            <div v-for="post in posts" :key="post.file_name" 
                 class="group relative bg-gradient-to-br from-white via-gray-50/50 to-gray-100/30 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800 rounded-lg p-3 shadow-sm border border-gray-200/60 dark:border-gray-700/60 hover:shadow-md hover:border-teal-300/50 dark:hover:border-teal-500/50 transition-all duration-200 cursor-pointer"
                 @click="viewPostDetail(post)">
              <div class="absolute inset-0 bg-gradient-to-r from-teal-500/5 to-cyan-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              
              <div class="relative z-10">
                <div class="flex items-start justify-between">
                  <div class="flex-1 min-w-0">
                    <h3 class="font-medium text-sm text-gray-900 dark:text-white truncate group-hover:text-teal-600 dark:group-hover:text-teal-400 transition-colors">{{ post.post_title }}</h3>
                    <div class="flex items-center mt-1 space-x-2 flex-wrap">
                      <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                        {{ post.provider_name || '未知供应商' }}
                      </span>
                      <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-teal-100 dark:bg-teal-900/50 text-teal-800 dark:text-teal-200">
                        {{ post.product_count }} 个产品
                      </span>
                      <span class="text-xs text-gray-500 dark:text-gray-400">{{ formatDate(post.created_time) }}</span>
                    </div>
                  </div>
                  <div class="ml-3 flex-shrink-0">
                    <svg class="w-4 h-4 text-gray-400 group-hover:text-teal-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 帖子详情模态框 -->
    <div v-if="selectedPost" class="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden border border-gray-200 dark:border-gray-700">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <svg class="w-6 h-6 mr-2 text-teal-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            帖子详情
          </h2>
          <button @click="selectedPost = null" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div v-if="postDetail">
            <!-- 解析数据，兼容新旧格式 -->
            <div v-if="analysisData">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ analysisData.post_title }}</h3>
              
              <!-- RSS信息（如果可用） -->
              <div v-if="rssData" class="bg-blue-50 dark:bg-blue-900/50 rounded-lg p-4 mb-4">
                <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">原始帖子信息</h4>
                <p class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">作者:</strong> {{ rssData.creator }}</p>
                <p class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">分类:</strong> {{ rssData.category }}</p>
                <p class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">发布时间:</strong> {{ formatDate(rssData.pub_date) }}</p>
                <div v-if="rssData.link" class="mt-2">
                  <a :href="rssData.link" target="_blank" class="text-blue-600 dark:text-blue-400 hover:underline">
                    查看原帖 →
                  </a>
                </div>
              </div>
              
              <!-- 供应商信息 -->
              <div v-if="analysisData.provider" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                <h4 class="font-medium text-gray-900 dark:text-white mb-2">供应商信息</h4>
                <p class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">名称:</strong> {{ analysisData.provider?.name || '未知' }}</p>
                <p v-if="analysisData.provider?.website" class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">网站:</strong> 
                  <a :href="analysisData.provider.website" target="_blank" class="text-blue-600 dark:text-blue-400 hover:underline">
                    {{ analysisData.provider.website }}
                  </a>
                </p>
              </div>
              
              <!-- 全局优惠信息 -->
              <div v-if="analysisData.global_promo_code || analysisData.global_discount_info" class="bg-yellow-50 dark:bg-yellow-900/50 rounded-lg p-4 mb-4">
                <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-2">全局优惠</h4>
                <p v-if="analysisData.global_promo_code" class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">优惠码:</strong> <code class="bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white px-2 py-1 rounded">{{ analysisData.global_promo_code }}</code></p>
                <p v-if="analysisData.global_discount_info" class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">优惠信息:</strong> {{ analysisData.global_discount_info }}</p>
              </div>
              
              <!-- 标签 -->
              <div v-if="analysisData.tags?.length" class="mb-4">
                <h4 class="font-medium text-gray-900 dark:text-white mb-2">标签</h4>
                <div class="flex flex-wrap gap-2">
                  <span v-for="tag in analysisData.tags" :key="tag" 
                        class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200">
                    {{ tag }}
                  </span>
                </div>
              </div>
              
              <!-- 产品列表 -->
              <div v-if="analysisData.products?.length" class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-white">产品列表 ({{ analysisData.products.length }})</h4>
                <div v-for="(product, index) in analysisData.products" :key="index" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h5 class="font-medium text-gray-900 dark:text-white">{{ product.title }}</h5>
                  
                  <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">CPU:</strong> {{ product.specs?.cpu || 'N/A' }}</p>
                      <p class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">内存:</strong> {{ product.specs?.ram || 'N/A' }}</p>
                      <p class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">存储:</strong> {{ product.specs?.storage || 'N/A' }}</p>
                      <p v-if="product.specs?.bandwidth" class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">带宽:</strong> {{ product.specs.bandwidth }}</p>
                    </div>
                    <div>
                      <p class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">价格:</strong> ${{ product.pricing?.price || 'N/A' }}/{{ product.pricing?.billing_cycle || 'month' }}</p>
                      <p class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">位置:</strong> {{ product.location?.city || 'N/A' }}, {{ product.location?.country || 'N/A' }}</p>
                      <p v-if="product.promo_code" class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">优惠码:</strong> <code class="bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white px-2 py-1 rounded">{{ product.promo_code }}</code></p>
                      <p class="text-gray-700 dark:text-gray-300"><strong class="text-gray-900 dark:text-white">可用性:</strong> 
                        <span v-if="product.availability === null || product.availability === undefined" class="text-gray-500 dark:text-gray-400">
                          不确定
                        </span>
                        <span v-else-if="product.availability && (product.availability.toLowerCase().includes('stock') || product.availability.toLowerCase().includes('available') || product.availability.toLowerCase().includes('in stock'))" class="text-green-600 dark:text-green-400">
                          有货
                        </span>
                        <span v-else-if="product.availability && (product.availability.toLowerCase().includes('out') || product.availability.toLowerCase().includes('sold') || product.availability.toLowerCase().includes('unavailable'))" class="text-red-600 dark:text-red-400">
                          缺货
                        </span>
                        <span v-else class="text-yellow-600 dark:text-yellow-400">
                          {{ product.availability || '不确定' }}
                        </span>
                      </p>
                    </div>
                  </div>
                  
                  <div v-if="product.order_url" class="mt-3">
                    <a :href="product.order_url" target="_blank" 
                       class="inline-flex items-center px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors">
                      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                      立即订购
                    </a>
                  </div>
                </div>
              </div>
              
              <!-- 处理信息 -->
              <div v-if="postDetail.processed_at" class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 text-sm text-gray-500 dark:text-gray-400">
                <p><strong class="text-gray-900 dark:text-white">处理时间:</strong> {{ formatDate(postDetail.processed_at) }}</p>
              </div>
            </div>
            
            <!-- 原始数据调试信息 -->
            <div v-else class="bg-red-50 dark:bg-red-900/50 rounded-lg p-4">
              <h4 class="font-medium text-red-800 dark:text-red-200 mb-2">数据格式异常</h4>
              <p class="text-sm text-red-700 dark:text-red-300">无法解析帖子数据，请检查数据格式。</p>
              <details class="mt-2">
                <summary class="cursor-pointer text-red-600 dark:text-red-400">查看原始数据</summary>
                <pre class="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-auto">{{ JSON.stringify(postDetail, null, 2) }}</pre>
              </details>
            </div>
          </div>
          
          <div v-else class="flex justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { toast } from '../utils/helpers'
import { api } from '../utils/api'

export default {
  name: 'LetMonitor',
  setup() {
    
    // 响应式数据
    const letStatus = ref({
      enabled: false,
      running: false,
      processed_posts: 0,
      stored_posts: 0,
      check_interval: 300,
      proxy_url: null
    })
    
    const letConfig = reactive({
      check_interval: 300,
      data_dir: 'data/let_posts',
      proxy_url: ''
    })
    
    const posts = ref([])
    const selectedPost = ref(null)
    const postDetail = ref(null)
    const totalPosts = ref(0)
    const postsLimit = ref(20)
    
    const isLoading = reactive({
      status: false,
      posts: false,
      saveConfig: false,
      testRss: false,
      postDetail: false,
      loadingMore: false
    })
    
    // 计算属性
    const formatDate = (dateString) => {
      if (!dateString) return 'N/A'
      try {
        return new Date(dateString).toLocaleString('zh-CN')
      } catch {
        return 'N/A'
      }
    }
    
    // 方法
    const loadLetStatus = async () => {
      isLoading.status = true
      try {
        const data = await api.get('/api/let/status')
        letStatus.value = { ...letStatus.value, ...data }
        
        if (data.enabled) {
          // 同步配置
          letConfig.check_interval = data.check_interval || 300
          letConfig.proxy_url = data.proxy_url || ''
        }
      } catch (error) {
        console.error('获取LET状态失败:', error)
        toast('error', '获取LET状态失败')
      } finally {
        isLoading.status = false
      }
    }
    
    const loadPosts = async (reset = true) => {
      isLoading.posts = true
      try {
        const limit = reset ? postsLimit.value : posts.value.length + postsLimit.value
        const data = await api.get(`/api/let/posts?limit=${limit}`)
        posts.value = data.posts || []
        totalPosts.value = data.total || 0
      } catch (error) {
        console.error('获取帖子列表失败:', error)
        toast('error', '获取帖子列表失败')
      } finally {
        isLoading.posts = false
      }
    }
    
    const saveLetConfig = async () => {
      isLoading.saveConfig = true
      try {
        await api.post('/api/let/config', letConfig)
        toast('success', 'LET监控配置已保存')
        await loadLetStatus()
      } catch (error) {
        console.error('保存配置失败:', error)
        toast('error', '保存配置失败')
      } finally {
        isLoading.saveConfig = false
      }
    }
    
    const testRssFetch = async () => {
      isLoading.testRss = true
      try {
        const data = await api.post('/api/let/test-fetch')
        if (data.success) {
          toast('success', data.message)
        } else {
          toast('error', data.message)
        }
      } catch (error) {
        console.error('RSS测试失败:', error)
        toast('error', 'RSS测试失败')
      } finally {
        isLoading.testRss = false
      }
    }
    
    const viewPostDetail = async (post) => {
      selectedPost.value = post
      postDetail.value = null
      isLoading.postDetail = true
      
      try {
        // 使用post_id而不是file_name
        const postId = post.post_id || post.file_name.replace('.json', '')
        const data = await api.get(`/api/let/posts/${postId}`)
        postDetail.value = data
      } catch (error) {
        console.error('获取帖子详情失败:', error)
        toast('error', '获取帖子详情失败')
        selectedPost.value = null
      } finally {
        isLoading.postDetail = false
      }
    }
    
    const loadMorePosts = async () => {
      if (isLoading.loadingMore || posts.value.length >= totalPosts.value) {
        return
      }
      
      isLoading.loadingMore = true
      try {
        const currentCount = posts.value.length
        const data = await api.get(`/api/let/posts?limit=${currentCount + postsLimit.value}`)
        posts.value = data.posts || []
        totalPosts.value = data.total || 0
      } catch (error) {
        console.error('加载更多帖子失败:', error)
        toast('error', '加载更多帖子失败')
      } finally {
        isLoading.loadingMore = false
      }
    }
    
    // 计算属性：是否还有更多帖子
    const hasMorePosts = computed(() => {
      return posts.value.length < totalPosts.value
    })
    
    // 计算属性：解析帖子详情数据
    const analysisData = computed(() => {
      if (!postDetail.value) return null
      
      // 新格式：包含 rss_item 和 analysis
      if (postDetail.value.analysis) {
        return postDetail.value.analysis
      }
      
      // 旧格式：直接就是分析数据
      if (postDetail.value.post_title || postDetail.value.provider) {
        return postDetail.value
      }
      
      return null
    })
    
    // 计算属性：RSS原始数据
    const rssData = computed(() => {
      if (!postDetail.value) return null
      
      // 新格式包含rss_item
      if (postDetail.value.rss_item) {
        return postDetail.value.rss_item
      }
      
      return null
    })
    
    // 生命周期
    onMounted(() => {
      loadLetStatus()
      loadPosts()
    })
    
    return {
      letStatus,
      letConfig,
      posts,
      selectedPost,
      postDetail,
      isLoading,
      formatDate,
      hasMorePosts,
      totalPosts,
      analysisData,
      rssData,
      loadLetStatus,
      loadPosts,
      loadMorePosts,
      saveLetConfig,
      testRssFetch,
      viewPostDetail
    }
  }
}
</script>

<style scoped>
/* 动画效果 */
.fixed {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.bg-white.dark\:bg-gray-800 {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background-color: rgb(243 244 246);
  border-radius: 9999px;
}

.dark .overflow-y-auto::-webkit-scrollbar-track {
  background-color: rgb(55 65 81);
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: rgb(209 213 219);
  border-radius: 9999px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: rgb(156 163 175);
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: rgb(107 114 128);
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: rgb(156 163 175);
}
</style>