<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white">网站管理</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">管理 WHMCS 网站和产品配置</p>
      </div>
      <button 
        @click="openSiteModal()" 
        class="bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200 flex items-center space-x-2"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        <span>添加网站</span>
      </button>
    </div>

    <!-- 网站列表 -->
    <div v-if="isLoading.sites" class="text-center p-12">
      <div class="spinner border-4 border-indigo-500 rounded-full w-8 h-8 mx-auto mb-4"></div>
      <p class="text-gray-500">加载网站数据中...</p>
    </div>

    <div v-else-if="siteList.length === 0" class="text-center p-12 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无网站</h3>
      <p class="text-gray-500 mb-4">开始添加您的第一个 WHMCS 网站</p>
      <button 
        @click="openSiteModal()" 
        class="bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200"
      >
        添加第一个网站
      </button>
    </div>

    <!-- 网站卡片列表 -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
      <div 
        v-for="site in siteList" 
        :key="site.site_id"
        class="group bg-gradient-to-br from-white via-gray-50 to-gray-100 dark:from-gray-800 dark:via-gray-800 dark:to-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md hover:border-indigo-300 dark:hover:border-indigo-500 transition-all duration-300 transform hover:-translate-y-0.5 hover:scale-[1.01]"
      >
        <!-- 网站信息头部 -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-start justify-between mb-3">
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-2">
                <!-- 网站状态指示器 -->
                <div class="relative">
                  <div class="w-2.5 h-2.5 bg-indigo-500 rounded-full animate-pulse"></div>
                  <div class="absolute inset-0 w-2.5 h-2.5 bg-indigo-400 rounded-full animate-ping opacity-75"></div>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                  {{ site.site_id }}
                </h3>
              </div>
              <a :href="site.base_url" target="_blank" 
                 class="text-indigo-500 hover:text-indigo-600 dark:text-indigo-400 dark:hover:text-indigo-300 text-sm flex items-center space-x-1 group/link">
                <span class="truncate">{{ formatUrl(site.base_url) }}</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 transform group-hover/link:translate-x-0.5 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
              <div class="mt-2 flex items-center space-x-2">
                <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span class="text-xs text-gray-600 dark:text-gray-300">{{ site.username }}</span>
                <!-- 认证方式徽章 -->
                <span v-if="site.cookies_string" class="inline-flex items-center px-1.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded text-xs font-medium">
                  <svg class="w-2.5 h-2.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Cookies
                </span>
                <span v-else class="inline-flex items-center px-1.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded text-xs font-medium">
                  <svg class="w-2.5 h-2.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  密码
                </span>
              </div>
            </div>
            <!-- 操作按钮 -->
            <div class="flex space-x-2">
              <!-- 测试登录按钮 - 特别醒目 -->
              <button 
                @click="testLogin(site.site_id)"
                :disabled="isLoading.testingLogin === site.site_id"
                class="relative p-2 bg-gradient-to-r from-emerald-500 to-green-500 hover:from-emerald-600 hover:to-green-600 text-white rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 disabled:opacity-70 disabled:cursor-not-allowed"
                title="测试登录"
              >
                <!-- 加载状态动画 -->
                <div v-if="isLoading.testingLogin === site.site_id" class="absolute inset-0 bg-gradient-to-r from-emerald-600 to-green-600 rounded-lg opacity-75 animate-pulse"></div>
                <svg v-if="isLoading.testingLogin === site.site_id" class="animate-spin h-4 w-4 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <!-- 脉动指示器 -->
                <div v-if="!isLoading.testingLogin" class="absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full animate-ping"></div>
              </button>
              
              <!-- 编辑按钮 -->
              <button 
                @click="openSiteModal(site.site_id, site)"
                class="p-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-indigo-100 dark:hover:bg-indigo-800 hover:text-indigo-600 dark:hover:text-indigo-400 rounded-lg shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-200"
                title="编辑网站"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
              
              <!-- 删除按钮 -->
              <button 
                @click="deleteSite(site.site_id)"
                class="p-2 bg-gray-100 dark:bg-gray-700 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-600 rounded-lg shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-200"
                title="删除网站"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- 产品管理区域 -->
        <div class="p-4">
          <!-- 产品统计 -->
          <div class="grid grid-cols-3 gap-2 mb-3">
            <div class="text-center p-2 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded border border-blue-200/50 dark:border-blue-700/50">
              <div class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ site.product_count || 0 }}</div>
              <div class="text-xs text-gray-600 dark:text-gray-400 font-medium">总产品</div>
            </div>
            <div class="text-center p-2 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded border border-green-200/50 dark:border-green-700/50">
              <div class="text-lg font-bold text-green-600 dark:text-green-400">{{ getEnabledProductsCount(site.site_id) }}</div>
              <div class="text-xs text-gray-600 dark:text-gray-400 font-medium">启用</div>
            </div>
            <div class="text-center p-2 bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded border border-orange-200/50 dark:border-orange-700/50">
              <div class="text-lg font-bold text-orange-600 dark:text-orange-400">{{ getAutoOrderProductsCount(site.site_id) }}</div>
              <div class="text-xs text-gray-600 dark:text-gray-400 font-medium">自动</div>
            </div>
          </div>
          
          <!-- 管理产品按钮 -->
          <router-link 
            :to="{ name: 'products', params: { siteId: site.site_id } }"
            class="w-full flex items-center justify-center p-2.5 bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 hover:from-indigo-100 hover:to-blue-100 dark:hover:from-indigo-900/30 dark:hover:to-blue-900/30 rounded border border-indigo-200/50 dark:border-indigo-700/50 hover:border-indigo-300 dark:hover:border-indigo-500 group/link transition-all duration-300 transform hover:scale-[1.02]"
          >
            <div class="flex items-center space-x-2">
              <div class="p-1.5 bg-indigo-100 dark:bg-indigo-800/50 rounded group-hover/link:bg-indigo-200 dark:group-hover/link:bg-indigo-700/50 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-indigo-600 dark:text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
              <span class="text-gray-800 dark:text-gray-200 group-hover/link:text-indigo-700 dark:group-hover/link:text-indigo-300 font-medium text-sm">管理产品</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400 group-hover/link:text-indigo-500 transform group-hover/link:translate-x-0.5 transition-all duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </router-link>
        </div>
      </div>
    </div>

    <!-- 网站编辑模态框 -->
    <div v-if="showSiteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
            {{ editingSiteId ? '编辑网站' : '添加网站' }}
          </h3>
          <button @click="closeSiteModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <form @submit.prevent="saveSite" class="p-6 space-y-6">
          <!-- 基本信息区域 -->
          <div class="space-y-4">
            <div class="flex items-center space-x-2 mb-4">
              <div class="w-1 h-6 bg-indigo-500 rounded-full"></div>
              <h4 class="text-lg font-semibold text-gray-900 dark:text-white">基本信息</h4>
            </div>
            
            <FormField
              v-model="siteForm.siteId"
              label="网站ID"
              :disabled="!!editingSiteId"
              required
              placeholder="例如: site1"
              help-text="用于在系统中标识此网站的唯一ID"
            >
              <template #prefix>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </template>
            </FormField>
            
            <FormField
              v-model="siteForm.base_url"
              label="网站URL"
              type="url"
              required
              placeholder="https://example.com"
              help-text="WHMCS网站的完整URL地址"
            >
              <template #prefix>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                </svg>
              </template>
            </FormField>
            
            <FormField
              v-model="siteForm.hostname_base"
              label="主机名前缀"
              required
              placeholder="例如: vps"
              help-text="用于生成主机名的前缀，如 vps001、vps002"
            >
              <template #prefix>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h6a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h6a2 2 0 002-2v-4a2 2 0 00-2-2m8-8V6a2 2 0 012-2h4a2 2 0 012 2v2a2 2 0 01-2 2h-4a2 2 0 01-2-2z" />
                </svg>
              </template>
            </FormField>
          </div>

          <!-- 认证信息区域 -->
          <div class="space-y-4">
            <div class="flex items-center space-x-2 mb-4">
              <div class="w-1 h-6 bg-green-500 rounded-full"></div>
              <h4 class="text-lg font-semibold text-gray-900 dark:text-white">认证信息</h4>
            </div>
            
            <FormField
              v-model="siteForm.username"
              label="用户名"
              required
              placeholder="WHMCS登录用户名"
              help-text="用于登录WHMCS的账户用户名"
            >
              <template #prefix>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </template>
            </FormField>
            
            <FormField
              v-model="siteForm.password"
              label="密码"
              type="password"
              :required="!editingSiteId"
              :placeholder="editingSiteId ? '留空保持不变' : '请输入密码'"
              :help-text="editingSiteId ? '如需修改密码请输入新密码，否则留空' : '用于登录WHMCS的账户密码'"
            >
              <template #prefix>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </template>
            </FormField>
            
            <FormField
              v-model="siteForm.rootpw"
              label="Root密码"
              type="password"
              :required="!editingSiteId"
              :placeholder="editingSiteId ? '留空保持不变' : '请输入Root密码'"
              :help-text="editingSiteId ? '如需修改Root密码请输入新密码，否则留空' : '用于VPS的Root密码'"
            >
              <template #prefix>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
              </template>
            </FormField>
          </div>

          <!-- 配置选项区域 -->
          <div class="space-y-4">
            <div class="flex items-center space-x-2 mb-4">
              <div class="w-1 h-6 bg-purple-500 rounded-full"></div>
              <h4 class="text-lg font-semibold text-gray-900 dark:text-white">配置选项</h4>
            </div>
            
            <FormCombobox
              v-model="siteForm.payment_method"
              label="支付方式"
              :options="paymentMethods"
              placeholder="输入或选择支付网关"
              help-text="可以从推荐列表中选择，或输入自定义的支付网关名称"
            >
              <template #prefix>
                <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
              </template>
            </FormCombobox>
            
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                <span class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Cookies字符串 (可选)
                </span>
              </label>
              <textarea 
                v-model="siteForm.cookies_string"
                rows="3"
                class="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="如果提供，将使用Cookies进行认证，否则使用用户名密码"
              ></textarea>
              <p class="text-xs text-gray-500 dark:text-gray-400">可选择直接使用Cookies进行认证，跳过登录步骤</p>
            </div>
          </div>
          
          <div class="flex justify-end space-x-3 pt-4">
            <button 
              type="button"
              @click="closeSiteModal"
              class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-lg transition-colors"
            >
              取消
            </button>
            <button 
              type="submit"
              :disabled="isLoading.savingSite"
              class="px-4 py-2 bg-indigo-500 hover:bg-indigo-600 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center space-x-2"
            >
              <svg v-if="isLoading.savingSite" class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span>{{ editingSiteId ? '更新' : '添加' }}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useMainStore } from '../stores/main'
import { api } from '../utils/api'
import { formatTime, toast, showConfirm } from '../utils/helpers'
import Swal from 'sweetalert2'
import FormField from '../components/FormField.vue'
import FormSelect from '../components/FormSelect.vue'
import FormCombobox from '../components/FormCombobox.vue'

export default {
  name: 'Sites',
  components: {
    FormField,
    FormSelect,
    FormCombobox
  },
  setup() {
    const router = useRouter()
    const store = useMainStore()
    
    const isLoading = reactive({
      sites: false,
      testingLogin: null,
      savingSite: false
    })
    
    // 直接使用store中的sites数据
    const sites = computed(() => {
      // 将store中的sites数组转换为对象格式以兼容现有逻辑
      const sitesObj = {}
      store.sites.forEach(site => {
        sitesObj[site.site_id] = site
      })
      return sitesObj
    })
    
    const showSiteModal = ref(false)
    const editingSiteId = ref(null)
    
    const siteForm = reactive({
      siteId: '',
      base_url: '',
      username: '',
      password: '',
      hostname_base: '',
      rootpw: '',
      payment_method: 'cryptomusgateway',
      cookies_string: ''
    })
    
    // 支付方式选项 - 提供详细的描述和分类
    const paymentMethods = [
      // 加密货币支付
      { value: 'cryptomusgateway', label: 'CryptoMus Gateway', description: '加密货币支付网关' },
      { value: 'coinbase', label: 'Coinbase Commerce', description: '正规加密货币支付平台' },
      { value: 'coinpayments', label: 'CoinPayments', description: '多种加密货币支持' },
      { value: 'btcpay', label: 'BTCPay Server', description: '去中心化比特币支付' },
      
      // 传统支付
      { value: 'paypal', label: 'PayPal', description: '全球领先的在线支付平台' },
      { value: 'stripe', label: 'Stripe', description: '专业的在线支付解决方案' },
      { value: 'square', label: 'Square', description: '综合商业支付解决方案' },
      { value: '2checkout', label: '2Checkout (Verifone)', description: '全球支付处理平台' },
      
      // 中国本土支付
      { value: 'alipay', label: '支付宝', description: '蚂蚁金服旗下支付平台' },
      { value: 'wechatpay', label: '微信支付', description: '腾讯旗下移动支付平台' },
      { value: 'unionpay', label: '中国银联', description: '中国银行卡统一支付平台' },
      
      // 其他地区支付
      { value: 'mollie', label: 'Mollie', description: '欧洲本土化支付平台' },
      { value: 'razorpay', label: 'Razorpay', description: '印度领先支付网关' },
      { value: 'payu', label: 'PayU', description: '新兴市场支付解决方案' },
      
      // 银行支付
      { value: 'banktransfer', label: 'Bank Transfer', description: '银行转账/电汇' },
      { value: 'ach', label: 'ACH Transfer', description: '美国自动结算系统' },
      { value: 'sepa', label: 'SEPA Direct Debit', description: '欧元区直接借记' },
      
      // 电子钱包
      { value: 'skrill', label: 'Skrill', description: '数字钱包和支付服务' },
      { value: 'neteller', label: 'NETELLER', description: '在线资金转移服务' },
      { value: 'perfectmoney', label: 'Perfect Money', description: '数字货币系统' },
      
      // 其他常用
      { value: 'authorize', label: 'Authorize.Net', description: '老牌支付处理商' },
      { value: 'worldpay', label: 'Worldpay', description: '全球支付技术公司' },
      { value: 'adyen', label: 'Adyen', description: '全渠道支付平台' },
      { value: 'braintree', label: 'Braintree', description: 'PayPal 旗下支付平台' },
      
      // 免费/手动
      { value: 'free', label: 'Free Account', description: '免费账户，无需支付' },
      { value: 'manual', label: 'Manual Payment', description: '手动处理支付' },
      { value: 'invoice', label: 'Invoice Payment', description: '发票后付款' }
    ]
    
    // 将对象格式转换为数组格式以便显示
    const siteList = computed(() => {
      return Object.entries(sites.value).map(([siteId, siteData]) => ({
        site_id: siteId,
        ...siteData
      }))
    })
    
    // 格式化URL显示
    const formatUrl = (url) => {
      try {
        const urlObj = new URL(url)
        return urlObj.hostname
      } catch {
        return url
      }
    }
    
    // 获取网站启用产品数量
    const getEnabledProductsCount = (siteId) => {
      const site = sites.value[siteId]
      return site?.enabled_products || 0
    }
    
    // 获取网站自动下单产品数量
    const getAutoOrderProductsCount = (siteId) => {
      const site = sites.value[siteId]
      return site?.auto_order_products || 0
    }
    
    // 打开网站模态框
    const openSiteModal = (siteId = null, siteData = null) => {
      editingSiteId.value = siteId
      
      if (siteData) {
        // 编辑模式
        Object.assign(siteForm, {
          siteId: siteId,
          base_url: siteData.base_url,
          username: siteData.username,
          password: '',
          hostname_base: siteData.hostname_base,
          rootpw: '',
          payment_method: siteData.payment_method || 'cryptomusgateway',
          cookies_string: siteData.cookies_string || ''
        })
      } else {
        // 新增模式
        Object.assign(siteForm, {
          siteId: '',
          base_url: '',
          username: '',
          password: '',
          hostname_base: '',
          rootpw: '',
          payment_method: 'cryptomusgateway',
          cookies_string: ''
        })
      }
      
      showSiteModal.value = true
    }
    
    // 关闭网站模态框
    const closeSiteModal = () => {
      showSiteModal.value = false
      editingSiteId.value = null
    }
    
    // 保存网站
    const saveSite = async () => {
      isLoading.savingSite = true
      try {
        const payload = {
          base_url: siteForm.base_url,
          username: siteForm.username,
          hostname_base: siteForm.hostname_base,
          payment_method: siteForm.payment_method,
          cookies_string: siteForm.cookies_string
        }
        
        if (siteForm.password) {
          payload.password = siteForm.password
        }
        
        if (siteForm.rootpw) {
          payload.rootpw = siteForm.rootpw
        }
        
        const data = await api.post(`/api/sites/${siteForm.siteId}`, payload)
        toast('success', data.message)
        closeSiteModal()
        // 使用store方法刷新数据
        await store.getSites()
      } catch (error) {
        console.error('保存网站失败:', error)
        toast('error', '保存网站失败')
      } finally {
        isLoading.savingSite = false
      }
    }
    
    // 删除网站
    const deleteSite = async (siteId) => {
      const confirmed = await showConfirm(
        '确认删除网站',
        `确定要删除网站 "${siteId}" 吗？此操作不可撤销。`
      )
      
      if (confirmed) {
        try {
          const success = await store.deleteSite(siteId)
          if (success) {
            toast('success', '网站删除成功')
          } else {
            toast('error', '删除网站失败')
          }
        } catch (error) {
          console.error('删除网站失败:', error)
          toast('error', '删除网站失败')
        }
      }
    }
    
    // 测试登录
    const testLogin = async (siteId) => {
      isLoading.testingLogin = siteId
      try {
        const result = await store.testLogin(siteId)
        if (result.success) {
          toast('success', '登录测试成功')
        } else {
          toast('error', `登录测试失败: ${result.message}`)
        }
      } catch (error) {
        console.error('测试登录失败:', error)
        toast('error', '测试登录失败')
      } finally {
        isLoading.testingLogin = null
      }
    }
    
    // 生命周期 - 异步加载数据但不阻塞渲染
    onMounted(async () => {
      // 如果store中没有sites数据，异步加载但不等待
      if (!store.sites || store.sites.length === 0) {
        store.getSites().catch(console.error)
      }
    })
    
    return {
      isLoading: computed(() => ({
        ...isLoading,
        sites: store.isLoading.sites
      })),
      sites,
      siteList,
      showSiteModal,
      editingSiteId,
      siteForm,
      formatUrl,
      openSiteModal,
      closeSiteModal,
      saveSite,
      deleteSite,
      testLogin,
      getEnabledProductsCount,
      getAutoOrderProductsCount,
      paymentMethods
    }
  }
}
</script>

<style scoped>
.spinner {
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style> 