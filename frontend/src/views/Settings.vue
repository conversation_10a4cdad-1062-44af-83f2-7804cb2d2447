<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white">系统设置</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">配置监控系统的各项参数</p>
      </div>
    </div>

    <!-- 监控设置卡片 -->
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
      <!-- 监控配置 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">监控设置</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">配置库存监控参数</p>
            </div>
          </div>
        </div>
        
        <form @submit.prevent="saveMonitorSettings" class="p-6 space-y-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              检查频率 (秒)
            </label>
            <input 
              v-model.number="monitorConfig.frequency" 
              type="number" 
              min="1" 
              max="3600"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="建议: 30-300秒"
            />
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">监控系统检查库存的时间间隔</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              连续有货次数阈值
            </label>
            <input 
              v-model.number="monitorConfig.min_in_stock_count" 
              type="number" 
              min="0"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="建议: 1-5次"
            />
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">连续检测到有货多少次后触发通知</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              重试延迟 (秒)
            </label>
            <input 
              v-model.number="monitorConfig.retry_delay" 
              type="number" 
              min="1"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="建议: 5-30秒"
            />
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">失败后重试的等待时间</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-500 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              最大并发检查数
            </label>
            <input 
              v-model.number="monitorConfig.max_concurrent_checks" 
              type="number" 
              min="1" 
              max="20"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="建议: 5-10个"
            />
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">同时进行库存检查的最大任务数量</p>
          </div>

          <div class="pt-4">
            <button 
              type="submit"
              :disabled="isLoading.monitor"
              class="w-full flex items-center justify-center px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:opacity-50 text-white rounded-lg transition-colors"
            >
              <svg v-if="isLoading.monitor" class="animate-spin h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              保存监控设置
            </button>
          </div>
        </form>
      </div>

      <!-- 通知设置 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.5 5v9a1.5 1.5 0 001.5 1.5h13a1.5 1.5 0 001.5-1.5V5a1.5 1.5 0 00-1.5-1.5H6A1.5 1.5 0 004.5 5z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">通知设置</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">配置消息推送参数</p>
            </div>
          </div>
        </div>
        
        <form @submit.prevent="saveNotificationSettings" class="p-6 space-y-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-500 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2c0-1.1.9-2 2-2h6c1.1 0 2 .9 2 2v2M7 4h10M7 4v16c0 1.1.9 2 2 2h6c1.1 0 2 .9 2 2V4M7 4l3 3m0 0l3-3M10 7v10" />
              </svg>
              通知类型
            </label>
            <select v-model="notificationConfig.notice_type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
              <option value="telegram">Telegram Bot</option>
              <option value="wechat">微信推送</option>
              <option value="custom">自定义URL</option>
            </select>
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">选择通知推送方式</p>
          </div>

          <!-- Telegram 设置 -->
          <div v-if="notificationConfig.notice_type === 'telegram'" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Telegram Bot Token
              </label>
              <input 
                v-model="notificationConfig.telegram_token" 
                type="text" 
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                placeholder="1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZ"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Chat ID
              </label>
              <input 
                v-model="notificationConfig.chat_id" 
                type="text" 
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                placeholder="-1234567890"
              />
            </div>
          </div>

          <!-- 微信推送设置 -->
          <div v-if="notificationConfig.notice_type === 'wechat'">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              微信推送密钥
            </label>
            <input 
              v-model="notificationConfig.wechat_key" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              placeholder="微信推送服务的密钥"
            />
          </div>

          <!-- 自定义URL设置 -->
          <div v-if="notificationConfig.notice_type === 'custom'">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              自定义通知URL
            </label>
            <input 
              v-model="notificationConfig.custom_url" 
              type="url" 
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              placeholder="https://api.example.com/webhook"
            />
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">接收通知的URL地址</p>
          </div>

          <div class="pt-4">
            <button 
              type="submit"
              :disabled="isLoading.notification"
              class="w-full flex items-center justify-center px-4 py-2 bg-purple-500 hover:bg-purple-600 disabled:opacity-50 text-white rounded-lg transition-colors"
            >
              <svg v-if="isLoading.notification" class="animate-spin h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              保存通知设置
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 代理设置 - 全宽卡片 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">代理设置</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">配置网络代理参数</p>
            </div>
          </div>
          <label class="flex items-center">
            <input 
              v-model="proxyConfig.use_proxy" 
              type="checkbox" 
              class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
            />
            <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">启用代理</span>
          </label>
        </div>
      </div>
      
      <div v-if="proxyConfig.use_proxy" class="p-6">
        <form @submit.prevent="saveProxySettings" class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h6a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h6a2 2 0 002-2v-4a2 2 0 00-2-2m8-8V6a2 2 0 012-2h4a2 2 0 012 2v2a2 2 0 01-2 2h-4a2 2 0 01-2-2z" />
              </svg>
              代理主机地址
            </label>
            <input 
              v-model="proxyConfig.proxy_host" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="例如: http://proxy.example.com:8080"
            />
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">包含协议、主机名和端口的完整代理地址</p>
          </div>

          <div class="flex items-end">
            <button 
              type="submit"
              :disabled="isLoading.proxy"
              class="w-full flex items-center justify-center px-4 py-2 bg-green-500 hover:bg-green-600 disabled:opacity-50 text-white rounded-lg transition-colors"
            >
              <svg v-if="isLoading.proxy" class="animate-spin h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              保存代理设置
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 系统信息 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">系统信息</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">当前系统运行状态</p>
          </div>
        </div>
      </div>
      
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {{ status.total_sites || 0 }}
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">网站数量</div>
          </div>
          
          <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
              {{ status.total_products || 0 }}
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">产品总数</div>
          </div>
          
          <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {{ status.is_running ? '运行中' : '已停止' }}
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">监控状态</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useMainStore } from '../stores/main'
import { api } from '../utils/api'
import { toast } from '../utils/helpers'

export default {
  name: 'Settings',
  setup() {
    const store = useMainStore()
    
    // 使用store中的数据
    const config = computed(() => store.config)
    const status = computed(() => store.status)
    
    const isLoading = reactive({
      monitor: false,
      notification: false,
      proxy: false
    })
    
    // 监控配置
    const monitorConfig = reactive({
      frequency: 30,
      min_in_stock_count: 1,
      retry_delay: 5,
      max_concurrent_checks: 5
    })
    
    // 通知配置
    const notificationConfig = reactive({
      notice_type: 'telegram',
      telegram_token: '',
      chat_id: '',
      wechat_key: '',
      custom_url: ''
    })
    
    // 代理配置
    const proxyConfig = reactive({
      use_proxy: false,
      proxy_host: ''
    })
    
    // 通知类型选项
    const notificationTypes = [
      { value: 'telegram', label: 'Telegram Bot' },
      { value: 'wechat', label: '微信推送' },
      { value: 'custom', label: '自定义URL' }
    ]
    
    // 从store数据更新本地配置
    const updateLocalConfig = () => {
      if (config.value && config.value.monitor) {
        Object.assign(monitorConfig, config.value.monitor)
      }
      
      if (config.value && config.value.notification) {
        Object.assign(notificationConfig, config.value.notification)
      }
      
      if (config.value && config.value.proxy) {
        Object.assign(proxyConfig, config.value.proxy)
      }
    }
    
    // 保存监控设置
    const saveMonitorSettings = async () => {
      isLoading.monitor = true
      try {
        const data = await store.saveSettings('monitor', monitorConfig)
        if (data) {
          toast('success', '监控设置保存成功')
        }
      } catch (error) {
        console.error('保存监控设置失败:', error)
        toast('error', '保存监控设置失败')
      } finally {
        isLoading.monitor = false
      }
    }
    
    // 保存通知设置
    const saveNotificationSettings = async () => {
      isLoading.notification = true
      try {
        const data = await store.saveSettings('notification', notificationConfig)
        if (data) {
          toast('success', '通知设置保存成功')
        }
      } catch (error) {
        console.error('保存通知设置失败:', error)
        toast('error', '保存通知设置失败')
      } finally {
        isLoading.notification = false
      }
    }
    
    // 保存代理设置
    const saveProxySettings = async () => {
      isLoading.proxy = true
      try {
        const data = await store.saveSettings('proxy', proxyConfig)
        if (data) {
          toast('success', '代理设置保存成功')
        }
      } catch (error) {
        console.error('保存代理设置失败:', error)
        toast('error', '保存代理设置失败')
      } finally {
        isLoading.proxy = false
      }
    }
    
    // 生命周期 - 异步加载数据但不阻塞渲染
    onMounted(async () => {
      // 如果store中没有配置数据，异步加载但不等待
      if (!config.value || Object.keys(config.value).length === 0) {
        store.getConfig().catch(console.error)
      }
      if (!status.value || Object.keys(status.value).length === 0) {
        store.getStatus().catch(console.error)
      }
      
      // 在下一个tick更新本地配置
      setTimeout(updateLocalConfig, 100)
    })
    
    // 监听config变化，自动更新本地配置
    const unwatchConfig = computed(() => {
      updateLocalConfig()
      return config.value
    })
    
    // 表单验证方法
    const validateFrequency = () => {
      if (!monitorConfig.frequency) return ''
      if (monitorConfig.frequency < 1 || monitorConfig.frequency > 3600) {
        return '检查频率必须在 1-3600 秒之间'
      }
      if (monitorConfig.frequency < 10) {
        return '建议检查频率不小于 10 秒，避免过于频繁的请求'
      }
      return ''
    }
    
    const validateMinInStockCount = () => {
      if (!monitorConfig.min_in_stock_count && monitorConfig.min_in_stock_count !== 0) return ''
      if (monitorConfig.min_in_stock_count < 0 || monitorConfig.min_in_stock_count > 10) {
        return '连续有货次数阈值必须在 0-10 之间'
      }
      return ''
    }
    
    const validateRetryDelay = () => {
      if (!monitorConfig.retry_delay) return ''
      if (monitorConfig.retry_delay < 1 || monitorConfig.retry_delay > 300) {
        return '重试延迟必须在 1-300 秒之间'
      }
      return ''
    }
    
    const validateMaxConcurrentChecks = () => {
      if (!monitorConfig.max_concurrent_checks) return ''
      if (monitorConfig.max_concurrent_checks < 1 || monitorConfig.max_concurrent_checks > 20) {
        return '最大并发检查数必须在 1-20 之间'
      }
      return ''
    }
    
    const validateTelegramToken = () => {
      if (notificationConfig.notice_type !== 'telegram') return ''
      if (!notificationConfig.telegram_token) return 'Telegram Bot Token 是必填项'
      if (!/^\d+:[A-Za-z0-9_-]+$/.test(notificationConfig.telegram_token)) {
        return 'Telegram Bot Token 格式不正确'
      }
      return ''
    }
    
    const validateChatId = () => {
      if (notificationConfig.notice_type !== 'telegram') return ''
      if (!notificationConfig.chat_id) return 'Chat ID 是必填项'
      if (!/^-?\d+$/.test(notificationConfig.chat_id)) {
        return 'Chat ID 必须是数字'
      }
      return ''
    }
    
    const validateCustomUrl = () => {
      if (notificationConfig.notice_type !== 'custom') return ''
      if (!notificationConfig.custom_url) return '自定义URL是必填项'
      try {
        new URL(notificationConfig.custom_url)
      } catch {
        return '请输入有效的URL地址'
      }
      return ''
    }
    
    const validateProxyHost = () => {
      if (!proxyConfig.use_proxy || !proxyConfig.proxy_host) return ''
      try {
        new URL(proxyConfig.proxy_host)
      } catch {
        return '请输入有效的代理地址'
      }
      return ''
    }

    return {
      config,
      status,
      isLoading,
      monitorConfig,
      notificationConfig,
      proxyConfig,
      notificationTypes,
      saveMonitorSettings,
      saveNotificationSettings,
      saveProxySettings,
      validateFrequency,
      validateMinInStockCount,
      validateRetryDelay,
      validateMaxConcurrentChecks,
      validateTelegramToken,
      validateChatId,
      validateCustomUrl,
      validateProxyHost
    }
  }
}
</script> 