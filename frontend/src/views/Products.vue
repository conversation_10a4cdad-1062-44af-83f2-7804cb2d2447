<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <!-- 返回按钮 -->
        <router-link 
          to="/sites"
          class="flex items-center px-3 py-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          <span>返回网站列表</span>
        </router-link>
        
        <div class="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
        
        <div>
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white">{{ siteId }} - 产品管理</h2>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            管理网站的产品配置和监控设置
          </p>
        </div>
      </div>
      
      <!-- 添加产品按钮 -->
      <button
        @click="openProductModal()"
        class="flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"
      >
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        添加产品
      </button>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="group bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center">
          <div class="p-3 rounded-xl bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 group-hover:from-blue-200 group-hover:to-indigo-200 dark:group-hover:from-blue-800/40 dark:group-hover:to-indigo-800/40 transition-all duration-300">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors">总产品数</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">{{ productList.length }}</p>
          </div>
        </div>
      </div>

      <div class="group bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-green-300 dark:hover:border-green-500 transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center">
          <div class="p-3 rounded-xl bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 group-hover:from-green-200 group-hover:to-emerald-200 dark:group-hover:from-green-800/40 dark:group-hover:to-emerald-800/40 transition-all duration-300">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors">启用产品</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">{{ enabledProducts }}</p>
          </div>
        </div>
      </div>

      <div class="group bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-orange-300 dark:hover:border-orange-500 transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center">
          <div class="p-3 rounded-xl bg-gradient-to-br from-orange-100 to-amber-100 dark:from-orange-900/30 dark:to-amber-900/30 group-hover:from-orange-200 group-hover:to-amber-200 dark:group-hover:from-orange-800/40 dark:group-hover:to-amber-800/40 transition-all duration-300">
            <svg class="w-6 h-6 text-orange-600 dark:text-orange-400 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors">自动下单</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors">{{ autoOrderProducts }}</p>
          </div>
        </div>
      </div>

      <div class="group bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-emerald-300 dark:hover:border-emerald-500 transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center">
          <div class="p-3 rounded-xl bg-gradient-to-br from-emerald-100 to-teal-100 dark:from-emerald-900/30 dark:to-teal-900/30 group-hover:from-emerald-200 group-hover:to-teal-200 dark:group-hover:from-emerald-800/40 dark:group-hover:to-teal-800/40 transition-all duration-300">
            <svg class="w-6 h-6 text-emerald-600 dark:text-emerald-400 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors">有货产品</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors">{{ inStockProducts }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品列表 -->
    <div v-if="isLoading" class="text-center py-12">
      <div class="spinner border-4 border-indigo-200 border-t-indigo-600 rounded-full w-8 h-8 mx-auto"></div>
      <p class="mt-4 text-gray-500 dark:text-gray-400">加载产品数据中...</p>
    </div>

    <div v-else-if="productList.length === 0" class="text-center py-12 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
      <svg class="w-16 h-16 mx-auto text-gray-400 dark:text-gray-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7" />
      </svg>
      <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">暂无产品</h3>
      <p class="text-gray-500 dark:text-gray-400 mb-6">开始添加您的第一个产品进行监控</p>
      <button
        @click="openProductModal()"
        class="bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200"
      >
        添加第一个产品
      </button>
    </div>

    <div v-else class="space-y-6">
      <div
        v-for="product in productList"
        :key="product.product_id"
        class="group relative bg-gradient-to-br from-white via-gray-50/50 to-gray-100/30 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800 rounded-xl p-6 shadow-lg border border-gray-200/60 dark:border-gray-700/60 hover:shadow-xl hover:border-indigo-300/50 dark:hover:border-indigo-500/50 transition-all duration-300 transform hover:-translate-y-1 overflow-hidden"
      >
        <!-- 装饰性背景纹理 -->
        <div class="absolute inset-0 bg-gradient-to-br from-transparent via-indigo-50/20 to-blue-50/10 dark:via-indigo-900/10 dark:to-blue-900/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        <!-- 主要内容区域 -->
        <div class="relative z-10">
          <!-- 产品头部信息 -->
          <div class="flex items-start justify-between mb-4">
            <!-- 左侧：产品标识和基本信息 -->
            <div class="flex-1 space-y-3">
              <!-- 产品名称和主要状态 -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="relative">
                    <div class="w-2.5 h-2.5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full animate-pulse-custom"></div>
                    <div class="absolute inset-0 w-2.5 h-2.5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full animate-ping opacity-20"></div>
                  </div>
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-300">
                    {{ product.name || product.product_id }}
                  </h3>
                </div>
                
                <!-- 简化的状态徽章 -->
                <div class="flex items-center gap-2">
                  <div
                    :class="[
                      'flex items-center px-3 py-1 rounded-lg text-xs font-semibold border transition-all duration-200',
                      product.enabled 
                        ? 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-700/30' 
                        : 'bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600/50'
                    ]"
                  >
                    <div :class="['w-1.5 h-1.5 rounded-full mr-1.5', product.enabled ? 'bg-green-500 animate-pulse' : 'bg-gray-400']"></div>
                    {{ product.enabled ? '监控中' : '已停止' }}
                  </div>
                  
                  <div
                    v-if="product.auto_order_enabled"
                    class="flex items-center px-3 py-1 rounded-lg text-xs font-semibold border bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-700/30 transition-all duration-200"
                  >
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    自动
                  </div>
                  
                  <div
                    :class="[
                      'flex items-center px-3 py-1 rounded-lg text-xs font-semibold border transition-all duration-200',
                      product.status 
                        ? 'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-700/30' 
                        : 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-700/30'
                    ]"
                  >
                    <div :class="['w-1.5 h-1.5 rounded-full mr-1.5', product.status ? 'bg-emerald-500' : 'bg-red-500']"></div>
                    {{ product.status ? '有货' : '缺货' }}
                  </div>
                </div>
              </div>
              
              <!-- 产品信息 - 根据类型显示 -->
              <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <div v-if="product.product_type === 'let_smart'" class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                  <span class="text-purple-600 dark:text-purple-400 font-medium">🧠 LET智能匹配</span>
                  <span class="text-gray-500">|</span>
                  <span class="truncate">{{ product.let_match_criteria.natural_language || '智能匹配产品' }}</span>
                  <div v-if="product.let_match_criteria?.keywords?.length" class="flex items-center space-x-1">
                    <span class="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                      {{ product.let_match_criteria.keywords.slice(0, 2).join(', ') }}
                      <span v-if="product.let_match_criteria.keywords.length > 2">...</span>
                    </span>
                  </div>
                </div>
                <div v-else class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                  <a :href="product.url" target="_blank" class="flex-1 text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300 truncate font-medium transition-colors">
                    {{ product.url }}
                  </a>
                </div>
              </div>
              
              <!-- 关键信息行 -->
              <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                <div class="flex items-center space-x-4">
                  <div v-if="product.product_type === 'let_smart'" class="flex items-center space-x-4">
                    <span v-if="product.let_match_criteria?.min_price || product.let_match_criteria?.max_price" class="flex items-center">
                      💰 ${{ product.let_match_criteria?.min_price || '0' }} - ${{ product.let_match_criteria?.max_price || '∞' }}
                    </span>
                    <span v-if="product.let_match_criteria?.min_cpu_cores" class="flex items-center">
                      🔧 {{ product.let_match_criteria?.min_cpu_cores }}+ 核心
                    </span>
                    <span v-if="product.let_match_criteria?.min_ram_gb" class="flex items-center">
                      💾 {{ product.let_match_criteria?.min_ram_gb }}+ GB
                    </span>
                    <span v-if="product.total_let_matches" class="text-purple-600 dark:text-purple-400 font-medium">
                      🎯 匹配 {{ product.total_let_matches }} 次
                    </span>
                  </div>
                  <div v-else class="flex items-center space-x-4">
                    <span>{{ product.promo_code || '无优惠码' }}</span>
                    <span>{{ getBillingCycleText(product.billing_cycle) }}</span>
                  </div>
                  <span v-if="product.order_failure_count > 0" class="text-yellow-600 dark:text-yellow-400 font-medium">
                    失败 {{ product.order_failure_count }}/{{ product.max_order_retries }}
                  </span>
                </div>
                <div v-if="product.last_check" class="flex items-center space-x-1">
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span class="text-xs">{{ formatTime(product.last_check, true) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 控制面板 -->
          <div class="flex items-center justify-between pt-4 border-t border-gray-200/50 dark:border-gray-700/50">
            <!-- 左侧：简化状态显示 -->
            <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
              <span v-if="product.product_type === 'let_smart'">
                LET匹配: {{ product.last_let_match_count || 0 }}
              </span>
              <span v-else>
                有货计数: {{ product.in_stock_count }}
              </span>
              <span v-if="product.successful_let_orders" class="text-green-600 dark:text-green-400">
                成功订单: {{ product.successful_let_orders }}
              </span>
            </div>

            <!-- 右侧：精简的控制中心 -->
            <div class="flex items-center space-x-6">
              <!-- 监控控制器 - 中等尺寸 -->
              <div class="flex flex-col items-center space-y-1">
                <div class="relative">
                  <div 
                    :class="[
                      'relative w-14 h-14 rounded-full border-3 transition-all duration-500 cursor-pointer group',
                      product.enabled 
                        ? 'border-green-400 bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 shadow-lg shadow-green-200/30 dark:shadow-green-900/20' 
                        : 'border-gray-300 dark:border-gray-600 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 shadow-md'
                    ]"
                    @click="toggleProductEnabled(product.product_id, !product.enabled)"
                  >
                    <div 
                      :class="[
                        'absolute inset-1 rounded-full flex items-center justify-center transition-all duration-500 transform',
                        product.enabled 
                          ? 'bg-gradient-to-br from-green-400 to-emerald-500 scale-100 rotate-0' 
                          : 'bg-gradient-to-br from-gray-400 to-gray-500 scale-75 rotate-180'
                      ]"
                    >
                      <svg 
                        :class="[
                          'w-5 h-5 transition-all duration-500',
                          product.enabled ? 'text-white animate-pulse' : 'text-gray-200'
                        ]" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path 
                          stroke-linecap="round" 
                          stroke-linejoin="round" 
                          stroke-width="2" 
                          :d="product.enabled ? 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z' : 'M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L17 17M15 12a3 3 0 11-6 0 3 3 0 016 0z'"
                        />
                      </svg>
                    </div>
                    
                    <div 
                      :class="[
                        'absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full border border-white transition-all duration-300',
                        product.enabled ? 'bg-green-500 animate-ping' : 'bg-red-500'
                      ]"
                    ></div>
                    
                    <div 
                      v-if="product.enabled"
                      class="absolute inset-0 rounded-full border border-green-400 animate-ping opacity-20"
                    ></div>
                  </div>
                </div>
                <span :class="['text-xs font-medium', product.enabled ? 'text-green-600 dark:text-green-400' : 'text-gray-500']">
                  {{ product.enabled ? '监控' : '停止' }}
                </span>
              </div>

              <!-- 自动下单控制器 -->
              <div class="flex flex-col items-center space-y-1">
                <div class="relative">
                  <div 
                    :class="[
                      'relative w-14 h-14 cursor-pointer group transition-all duration-500 transform hover:scale-105',
                      product.auto_order_enabled ? 'animate-pulse-slow' : ''
                    ]"
                    @click="toggleAutoOrder(product.product_id, !product.auto_order_enabled)"
                  >
                    <div 
                      :class="[
                        'w-full h-full transition-all duration-500 transform shadow-lg',
                        product.auto_order_enabled 
                          ? 'bg-gradient-to-br from-orange-400 via-red-500 to-pink-500 rotate-0 shadow-orange-300/30 dark:shadow-orange-900/20' 
                          : 'bg-gradient-to-br from-gray-300 to-gray-400 dark:from-gray-600 dark:to-gray-700 rotate-45 shadow-gray-300/30'
                      ]"
                      style="clip-path: polygon(50% 0%, 93.3% 25%, 93.3% 75%, 50% 100%, 6.7% 75%, 6.7% 25%);"
                    ></div>
                    
                    <div 
                      :class="[
                        'absolute inset-2 rounded-full flex items-center justify-center transition-all duration-500',
                        product.auto_order_enabled ? 'bg-white/20 backdrop-blur-sm' : 'bg-white/30'
                      ]"
                    >
                      <svg 
                        :class="[
                          'w-6 h-6 transition-all duration-500 transform',
                          product.auto_order_enabled ? 'text-white animate-bounce' : 'text-gray-500 scale-75'
                        ]" 
                        fill="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    
                    <div 
                      v-if="product.auto_order_enabled"
                      class="absolute inset-0 animate-spin-slow"
                      style="clip-path: polygon(50% 0%, 93.3% 25%, 93.3% 75%, 50% 100%, 6.7% 75%, 6.7% 25%);"
                    >
                      <div class="w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
                    </div>
                    
                    <div 
                      :class="[
                        'absolute -top-1 -right-1 w-4 h-4 rounded-full border border-white flex items-center justify-center transition-all duration-300',
                        product.auto_order_enabled ? 'bg-yellow-400 text-orange-600 animate-pulse' : 'bg-gray-400 text-gray-600'
                      ]"
                    >
                      <span class="text-xs font-bold">
                        {{ product.auto_order_enabled ? '⚡' : '○' }}
                      </span>
                    </div>
                  </div>
                </div>
                <span :class="['text-xs font-medium', product.auto_order_enabled ? 'text-orange-600 dark:text-orange-400' : 'text-gray-500']">
                  {{ product.auto_order_enabled ? '自动' : '手动' }}
                </span>
              </div>

              <!-- 分隔线 -->
              <div class="h-12 w-px bg-gradient-to-b from-transparent via-gray-300 dark:via-gray-600 to-transparent"></div>

              <!-- 操作按钮组 - 水平排列 -->
              <div class="flex items-center space-x-2">
                <button
                  v-if="product.order_failure_count > 0"
                  @click="resetFailures(product.product_id)"
                  class="group inline-flex items-center px-3 py-2 text-xs font-medium text-yellow-700 bg-gradient-to-r from-yellow-100 to-amber-100 hover:from-yellow-200 hover:to-amber-200 dark:from-yellow-900/20 dark:to-amber-900/20 dark:text-yellow-400 dark:hover:from-yellow-900/30 dark:hover:to-amber-900/30 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md border border-yellow-200 dark:border-yellow-800"
                  title="重置失败次数"
                >
                  <svg class="w-3 h-3 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </button>

                <button
                  @click="openProductModal(product.product_id, product)"
                  class="group inline-flex items-center px-3 py-2 text-xs font-medium text-gray-700 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 dark:from-gray-700 dark:to-gray-600 dark:text-gray-300 dark:hover:from-gray-600 dark:hover:to-gray-500 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md border border-gray-200 dark:border-gray-600"
                  title="编辑产品"
                >
                  <svg class="w-3 h-3 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>

                <button
                  @click="confirmDeleteProduct(product.product_id)"
                  class="group inline-flex items-center px-3 py-2 text-xs font-medium text-red-700 bg-gradient-to-r from-red-100 to-rose-100 hover:from-red-200 hover:to-rose-200 dark:from-red-900/20 dark:to-rose-900/20 dark:text-red-400 dark:hover:from-red-900/30 dark:hover:to-rose-900/30 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md border border-red-200 dark:border-red-800"
                  title="删除产品"
                >
                  <svg class="w-3 h-3 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品编辑模态框 -->
    <div v-if="showProductModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
            {{ editingProductId ? '编辑产品' : '添加产品' }}
          </h3>
          <button @click="closeProductModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <form @submit.prevent="saveProduct" class="p-6 space-y-6">
          <!-- 产品类型选择 -->
          <div class="space-y-4">
            <div class="flex items-center space-x-2 mb-4">
              <div class="w-1 h-6 bg-blue-500 rounded-full"></div>
              <h4 class="text-lg font-semibold text-gray-900 dark:text-white">产品类型</h4>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div 
                :class="[
                  'relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200',
                  productForm.productType === 'traditional' 
                    ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20' 
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                ]"
                @click="productForm.productType = 'traditional'"
              >
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg">
                    <svg class="w-6 h-6 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                  </div>
                  <div>
                    <h5 class="font-medium text-gray-900 dark:text-white">传统监控</h5>
                    <p class="text-sm text-gray-500 dark:text-gray-400">监控WHMCS产品页面库存状态</p>
                  </div>
                </div>
                <div 
                  v-if="productForm.productType === 'traditional'"
                  class="absolute top-2 right-2 w-5 h-5 bg-indigo-500 rounded-full flex items-center justify-center"
                >
                  <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
              
              <div 
                :class="[
                  'relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200',
                  productForm.productType === 'let_smart' 
                    ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20' 
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                ]"
                @click="productForm.productType = 'let_smart'"
              >
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <div>
                    <h5 class="font-medium text-gray-900 dark:text-white">🧠 LET智能匹配</h5>
                    <p class="text-sm text-gray-500 dark:text-gray-400">AI分析LET帖子并自动匹配</p>
                  </div>
                </div>
                <div 
                  v-if="productForm.productType === 'let_smart'"
                  class="absolute top-2 right-2 w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center"
                >
                  <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- 基本信息 -->
          <div class="space-y-4">
            <div class="flex items-center space-x-2 mb-4">
              <div class="w-1 h-6 bg-indigo-500 rounded-full"></div>
              <h4 class="text-lg font-semibold text-gray-900 dark:text-white">基本信息</h4>
            </div>
            
            <FormField
              v-model="productForm.productId"
              label="产品ID"
              :disabled="!!editingProductId"
              required
              placeholder="例如: product1"
              help-text="用于在系统中标识此产品的唯一ID"
            >
              <template #prefix>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </template>
            </FormField>
            
            <FormField
              v-model="productForm.name"
              label="产品名称"
              required
              placeholder="产品显示名称"
              help-text="在管理界面中显示的产品名称"
            >
              <template #prefix>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </template>
            </FormField>
            
            <!-- 传统产品字段 -->
            <div v-if="productForm.productType === 'traditional'" class="space-y-4">
              <FormField
                v-model="productForm.url"
                label="产品URL"
                type="url"
                required
                placeholder="https://example.com/cart.php?a=add&pid=123"
                help-text="WHMCS产品页面的完整URL地址"
              >
                <template #prefix>
                  <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                </template>
              </FormField>
            </div>

            <!-- LET智能产品字段 -->
            <div v-if="productForm.productType === 'let_smart'" class="space-y-4">
              <!-- LET匹配条件 -->
              <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
                <h5 class="font-medium text-purple-900 dark:text-purple-200 mb-3 flex items-center">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                  </svg>
                  智能匹配条件
                </h5>

                <!-- 自然语言描述 - 主要字段 -->
                <div class="mb-4">
                  <FormField
                    v-model="productForm.naturalLanguage"
                    label="🤖 自然语言需求描述"
                    type="textarea"
                    :rows="3"
                    placeholder="请用自然语言描述您的需求，例如：我需要一台便宜的美国VPS，最好有大硬盘，适合做存储..."
                    help-text="AI将主要根据此描述进行智能匹配，这是最重要的匹配条件"
                  >
                    <template #prefix>
                      <svg class="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </template>
                  </FormField>
                </div>

                <!-- 详细条件 -->
                <div class="border-t border-purple-200 dark:border-purple-700 pt-4">
                  <h6 class="text-sm font-medium text-purple-800 dark:text-purple-300 mb-3">📋 补充技术条件（可选）</h6>
                  
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    v-model="productForm.keywords"
                    label="关键词"
                    placeholder="VPS, 独立服务器, 美国, 等等"
                    help-text="用逗号分隔多个关键词"
                  >
                    <template #prefix>
                      <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                    </template>
                  </FormField>

                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">价格范围 (USD)</label>
                    <div class="grid grid-cols-2 gap-2">
                      <FormField
                        v-model.number="productForm.minPrice"
                        label=""
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="最低价格"
                        help-text=""
                      />
                      <FormField
                        v-model.number="productForm.maxPrice"
                        label=""
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="最高价格"
                        help-text=""
                      />
                    </div>
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <FormField
                    v-model.number="productForm.minCpuCores"
                    label="最少CPU核心数"
                    type="number"
                    min="1"
                    placeholder="如: 2"
                    help-text=""
                  >
                    <template #prefix>
                      <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                      </svg>
                    </template>
                  </FormField>

                  <FormField
                    v-model.number="productForm.minRamGb"
                    label="最少内存 (GB)"
                    type="number"
                    min="1"
                    placeholder="如: 4"
                    help-text=""
                  >
                    <template #prefix>
                      <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                    </template>
                  </FormField>

                  <FormField
                    v-model.number="productForm.minStorageGb"
                    label="最少存储 (GB)"
                    type="number"
                    min="1"
                    placeholder="如: 100"
                    help-text=""
                  >
                    <template #prefix>
                      <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                      </svg>
                    </template>
                  </FormField>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <FormField
                    v-model="productForm.preferredLocations"
                    label="偏好地区"
                    placeholder="美国, 欧洲, 亚洲, 等等"
                    help-text="用逗号分隔多个地区"
                  >
                    <template #prefix>
                      <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </template>
                  </FormField>

                  <FormField
                    v-model="productForm.excludedLocations"
                    label="排除地区"
                    placeholder="中国, 俄罗斯, 等等"
                    help-text="用逗号分隔多个地区"
                  >
                    <template #prefix>
                      <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                      </svg>
                    </template>
                  </FormField>
                </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 选项配置 -->
          <div class="space-y-4">
            <div class="flex items-center space-x-2 mb-4">
              <div class="w-1 h-6 bg-green-500 rounded-full"></div>
              <h4 class="text-lg font-semibold text-gray-900 dark:text-white">选项配置</h4>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                v-model="productForm.promo_code"
                label="优惠码"
                placeholder="请输入优惠码（可选）"
                help-text="可选的优惠码，在下单时自动应用"
              >
                <template #prefix>
                  <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </template>
              </FormField>
              
              <FormSelect
                v-model="productForm.billing_cycle"
                label="计费周期"
                :options="billingCycleOptions"
                help-text="选择产品的计费周期"
              >
                <template #prefix>
                  <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </template>
              </FormSelect>
            </div>
            
            <FormField
              v-model.number="productForm.max_order_retries"
              label="最大下单重试次数"
              type="number"
              :min="0"
              :max="10"
              help-text="下单失败时的最大重试次数（建议 3-5 次）"
            >
              <template #prefix>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </template>
            </FormField>
          </div>

          <!-- 功能开关 -->
          <div class="space-y-4">
            <div class="flex items-center space-x-2 mb-4">
              <div class="w-1 h-6 bg-purple-500 rounded-full"></div>
              <h4 class="text-lg font-semibold text-gray-900 dark:text-white">功能开关</h4>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </div>
                  <div>
                    <h5 class="font-medium text-gray-900 dark:text-white">启用监控</h5>
                    <p class="text-sm text-gray-500 dark:text-gray-400">监控产品库存状态</p>
                  </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input v-model="productForm.enabled" type="checkbox" class="sr-only peer">
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                </label>
              </div>
              
              <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                    <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h5 class="font-medium text-gray-900 dark:text-white">自动下单</h5>
                    <p class="text-sm text-gray-500 dark:text-gray-400">有货时自动购买</p>
                  </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input v-model="productForm.auto_order_enabled" type="checkbox" class="sr-only peer">
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                </label>
              </div>
            </div>
          </div>
          
          <div class="flex justify-end space-x-3 pt-4">
            <button 
              type="button"
              @click="closeProductModal"
              class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-lg transition-colors"
            >
              取消
            </button>
            <button 
              type="submit"
              :disabled="isLoadingSave"
              class="px-4 py-2 bg-indigo-500 hover:bg-indigo-600 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center space-x-2"
            >
              <svg v-if="isLoadingSave" class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span>{{ editingProductId ? '更新' : '添加' }}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useMainStore } from '../stores/main'
import { api } from '../utils/api'
import { formatTime, getBillingCycleText, toast, showConfirm } from '../utils/helpers'
import FormField from '../components/FormField.vue'
import FormSelect from '../components/FormSelect.vue'

export default {
  name: 'Products',
  components: {
    FormField,
    FormSelect
  },
  props: {
    siteId: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const route = useRoute()
    const store = useMainStore()
    
    // 使用 props 或 route 参数
    const siteId = computed(() => props.siteId || route.params.siteId)
    
    // 使用store中的products数据
    const products = computed(() => {
      // 将store中的products数组转换为对象格式以兼容现有逻辑
      const productsObj = {}
      store.products.forEach(product => {
        productsObj[product.product_id] = product
      })
      return productsObj
    })
    
    const isLoading = computed(() => store.isLoading.products)
    const isLoadingSave = ref(false)
    const showProductModal = ref(false)
    const editingProductId = ref(null)
    
    const productForm = reactive({
      productId: '',
      name: '',
      productType: 'traditional',
      // 传统产品字段
      url: '',
      promo_code: '',
      billing_cycle: 'monthly',
      // LET智能产品字段
      naturalLanguage: '',
      keywords: '',
      minPrice: null,
      maxPrice: null,
      minCpuCores: null,
      minRamGb: null,
      minStorageGb: null,
      preferredLocations: '',
      excludedLocations: '',
      // 通用字段
      enabled: true,
      auto_order_enabled: true,
      max_order_retries: 3
    })
    
    // 计费周期选项
    const billingCycleOptions = [
      { value: 'monthly', label: '月付' },
      { value: 'quarterly', label: '季付' },
      { value: 'semiannually', label: '半年付' },
      { value: 'annually', label: '年付' },
      { value: 'biennially', label: '两年付' },
      { value: 'triennially', label: '三年付' }
    ]
    
    // 计算属性
    const productList = computed(() => {
      return Object.entries(products.value).map(([productId, productData]) => ({
        product_id: productId,
        ...productData
      }))
    })
    
    const enabledProducts = computed(() => {
      return productList.value.filter(p => p.enabled).length
    })
    
    const autoOrderProducts = computed(() => {
      return productList.value.filter(p => p.auto_order_enabled).length
    })
    
    const inStockProducts = computed(() => {
      return productList.value.filter(p => p.status).length
    })
    
    // 移除loadProducts函数，直接使用store方法
    
    // 打开产品模态框
    const openProductModal = (productId = null, productData = null) => {
      editingProductId.value = productId
      
      if (productData) {
        // 编辑模式
        Object.assign(productForm, {
          productId: productId,
          name: productData.name,
          productType: productData.product_type || 'traditional',
          // 传统产品字段
          url: productData.url || '',
          promo_code: productData.promo_code || '',
          billing_cycle: productData.billing_cycle || 'monthly',
          // LET智能产品字段
          naturalLanguage: productData.let_match_criteria?.natural_language || '',
          keywords: productData.let_match_criteria?.keywords?.join(', ') || '',
          minPrice: productData.let_match_criteria?.min_price || null,
          maxPrice: productData.let_match_criteria?.max_price || null,
          minCpuCores: productData.let_match_criteria?.min_cpu_cores || null,
          minRamGb: productData.let_match_criteria?.min_ram_gb || null,
          minStorageGb: productData.let_match_criteria?.min_storage_gb || null,
          preferredLocations: productData.let_match_criteria?.preferred_locations?.join(', ') || '',
          excludedLocations: productData.let_match_criteria?.excluded_locations?.join(', ') || '',
          // 通用字段
          enabled: productData.enabled,
          auto_order_enabled: productData.auto_order_enabled,
          max_order_retries: productData.max_order_retries || 3
        })
      } else {
        // 新增模式
        Object.assign(productForm, {
          productId: '',
          name: '',
          productType: 'traditional',
          // 传统产品字段
          url: '',
          promo_code: '',
          billing_cycle: 'monthly',
          // LET智能产品字段
          naturalLanguage: '',
          keywords: '',
          minPrice: null,
          maxPrice: null,
          minCpuCores: null,
          minRamGb: null,
          minStorageGb: null,
          preferredLocations: '',
          excludedLocations: '',
          // 通用字段
          enabled: true,
          auto_order_enabled: true,
          max_order_retries: 3
        })
      }
      
      showProductModal.value = true
    }
    
    // 关闭产品模态框
    const closeProductModal = () => {
      showProductModal.value = false
      editingProductId.value = null
    }
    
    // 保存产品
    const saveProduct = async () => {
      isLoadingSave.value = true
      try {
        const payload = {
          name: productForm.name,
          product_type: productForm.productType,
          enabled: productForm.enabled,
          auto_order_enabled: productForm.auto_order_enabled,
          max_order_retries: productForm.max_order_retries
        }

        if (productForm.productType === 'traditional') {
          // 传统产品字段
          payload.url = productForm.url
          payload.promo_code = productForm.promo_code
          payload.billing_cycle = productForm.billing_cycle
        } else if (productForm.productType === 'let_smart') {
          // LET智能产品字段
          payload.let_match_criteria = {
            natural_language: productForm.naturalLanguage || '',
            keywords: productForm.keywords ? productForm.keywords.split(',').map(k => k.trim()).filter(k => k) : [],
            min_price: productForm.minPrice || null,
            max_price: productForm.maxPrice || null,
            min_cpu_cores: productForm.minCpuCores || null,
            min_ram_gb: productForm.minRamGb || null,
            min_storage_gb: productForm.minStorageGb || null,
            preferred_locations: productForm.preferredLocations ? productForm.preferredLocations.split(',').map(l => l.trim()).filter(l => l) : [],
            excluded_locations: productForm.excludedLocations ? productForm.excludedLocations.split(',').map(l => l.trim()).filter(l => l) : []
          }
        }
        
        const data = await api.post(`/api/sites/${siteId.value}/products/${productForm.productId}`, payload)
        toast('success', data.message)
        closeProductModal()
        // 使用store方法刷新数据
        await store.getProducts(siteId.value)
      } catch (error) {
        console.error('保存产品失败:', error)
        toast('error', '保存产品失败')
      } finally {
        isLoadingSave.value = false
      }
    }
    
    // 切换产品启用状态
    const toggleProductEnabled = async (productId, enabled) => {
      try {
        const success = await store.toggleProduct(siteId.value, productId, enabled)
        if (success) {
          toast('success', '产品状态更新成功')
        }
      } catch (error) {
        console.error('切换产品状态失败:', error)
        toast('error', '操作失败')
      }
    }
    
    // 切换自动下单状态
    const toggleAutoOrder = async (productId) => {
      try {
        const success = await store.toggleAutoOrder(siteId.value, productId)
        if (success) {
          toast('success', '自动下单状态更新成功')
        }
      } catch (error) {
        console.error('切换自动下单状态失败:', error)
        toast('error', '操作失败')
      }
    }
    
    // 重置失败次数
    const resetFailures = async (productId) => {
      try {
        const success = await store.resetFailures(siteId.value, productId)
        if (success) {
          toast('success', '失败次数重置成功')
        }
      } catch (error) {
        console.error('重置失败次数失败:', error)
        toast('error', '操作失败')
      }
    }
    
    // 删除产品确认
    const confirmDeleteProduct = async (productId) => {
      const confirmed = await showConfirm(
        '确认删除产品',
        `确定要删除产品 "${productId}" 吗？此操作不可撤销。`
      )
      
      if (confirmed) {
        try {
          const success = await store.deleteProduct(siteId.value, productId)
          if (success) {
            toast('success', '产品删除成功')
          } else {
            toast('error', '删除产品失败')
          }
        } catch (error) {
          console.error('删除产品失败:', error)
          toast('error', '删除产品失败')
        }
      }
    }
    
    // 监听 siteId 变化，确保切换网站时重新加载产品数据
    watch(siteId, (newSiteId, oldSiteId) => {
      if (newSiteId && newSiteId !== oldSiteId) {
        store.getProducts(newSiteId).catch(console.error)
      }
    }, { immediate: false })
    
    // 生命周期 - 异步加载数据但不阻塞渲染
    onMounted(async () => {
      // 如果store中没有当前网站的产品数据，或者切换了网站，则重新加载
      if (siteId.value && (store.currentSiteId !== siteId.value || !store.products || store.products.length === 0)) {
        store.getProducts(siteId.value).catch(console.error)
      }
    })
    
    return {
      siteId,
      products,
      productList,
      isLoading,
      isLoadingSave,
      showProductModal,
      editingProductId,
      productForm,
      enabledProducts,
      autoOrderProducts,
      inStockProducts,
      openProductModal,
      closeProductModal,
      saveProduct,
      toggleProductEnabled,
      toggleAutoOrder,
      resetFailures,
      confirmDeleteProduct,
      formatTime,
      getBillingCycleText,
      billingCycleOptions
    }
  }
}
</script>

<style scoped>
.spinner {
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 新增动画效果 */
@keyframes pulse-custom {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-pulse-custom {
  animation: pulse-custom 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

@keyframes pulse-slow {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.8; 
    transform: scale(1.05);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 2s ease-in-out infinite;
}

/* 悬停放大效果 */
.hover\:scale-110:hover {
  transform: scale(1.1);
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

/* 旋转动画 */
.group:hover .group-hover\:rotate-180 {
  transform: rotate(180deg);
}

.group:hover .group-hover\:rotate-12 {
  transform: rotate(12deg);
}

.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

/* 自定义尺寸 */
.w-18 {
  width: 4.5rem;
}

.h-18 {
  height: 4.5rem;
}

/* 装饰性效果 */
.dark\:via-gray-750 {
  --tw-gradient-stops: var(--tw-gradient-from), #374151 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.border-3 {
  border-width: 3px;
}
</style> 