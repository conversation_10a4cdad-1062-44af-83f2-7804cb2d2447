<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white">通知管理</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">管理通知渠道和发送自定义消息</p>
      </div>
      <div class="flex gap-3">
        <button
          @click="showAddModal = true"
          class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200 flex items-center space-x-2"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          <span>添加渠道</span>
        </button>
        <button
          @click="showSendModal = true"
          class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200 flex items-center space-x-2"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
          <span>发送通知</span>
        </button>
      </div>
    </div>

    <!-- 通知渠道列表 -->
    <div v-if="isLoading" class="text-center p-12">
      <div class="spinner border-4 border-blue-500 rounded-full w-8 h-8 mx-auto mb-4"></div>
      <p class="text-gray-500">加载通知渠道中...</p>
    </div>

    <div v-else-if="channels.length === 0" class="text-center p-12 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
      <svg class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h7a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无通知渠道</h3>
      <p class="text-gray-500 mb-4">开始添加您的第一个通知渠道</p>
      <button
        @click="showAddModal = true"
        class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200"
      >
        添加第一个渠道
      </button>
    </div>
    
    <!-- 通知渠道卡片列表 -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
      <div
        v-for="channel in channels"
        :key="channel.id"
        class="group bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-300 transform hover:-translate-y-0.5 hover:scale-[1.01] flex flex-col"
      >
        <!-- 渠道信息头部 -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-start justify-between mb-3">
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-2">
                <!-- 渠道状态指示器 -->
                <div class="relative">
                  <div :class="['w-2.5 h-2.5 rounded-full', channel.enabled ? 'bg-blue-500 animate-pulse' : 'bg-red-500']"></div>
                  <div v-if="channel.enabled" class="absolute inset-0 w-2.5 h-2.5 bg-blue-400 rounded-full animate-ping opacity-75"></div>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate">
                  {{ channel.name }}
                </h3>
              </div>
              <div class="flex items-center space-x-2 mb-2">
                <div class="p-1.5 rounded-lg" :class="getChannelIconBg(channel.channel_type)">
                  <svg v-if="channel.channel_type === 'telegram'" class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                  </svg>
                  <svg v-else-if="channel.channel_type === 'discord'" class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                  </svg>
                  <svg v-else-if="channel.channel_type === 'wechat'" class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.53-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.53-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.34 2.867c-1.797-.052-3.746.512-5.28 1.786-1.72 1.428-2.687 3.72-1.78 6.22.942 2.453 3.666 4.229 6.884 4.229.826 0 1.622-.12 2.361-.336a.722.722 0 0 1 .598.082l1.584.926a.272.272 0 0 0 .14.04c.134 0 .24-.111.24-.247 0-.06-.023-.12-.038-.177l-.327-1.233a.582.582 0 0 1-.023-.156.49.49 0 0 1 .201-.398C23.024 18.48 24 16.82 24 14.98c0-3.21-2.931-5.837-6.656-6.088V8.858zm-3.288 1.87c.54 0 .97.424.97.949 0 .525-.43.949-.97.949-.54 0-.97-.424-.97-.949 0-.525.43-.949.97-.949zm4.714 0c.54 0 .97.424.97.949 0 .525-.43.949-.97.949-.54 0-.97-.424-.97-.949 0-.525.43-.949.97-.949z"/>
                  </svg>
                  <svg v-else class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <span class="text-sm text-gray-600 dark:text-gray-300 capitalize">{{ getChannelTypeName(channel.channel_type) }}</span>
              </div>
              <!-- 状态徽章 -->
              <span
                :class="[
                  'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full',
                  channel.enabled 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' 
                    : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                ]"
              >
                <svg :class="['w-2 h-2 mr-1', channel.enabled ? 'text-green-500' : 'text-red-500']" fill="currentColor" viewBox="0 0 8 8">
                  <circle cx="4" cy="4" r="3" />
                </svg>
                {{ channel.enabled ? '运行中' : '已停用' }}
              </span>
            </div>
          </div>
        </div>
          
          <!-- 渠道内容 -->
          <div class="p-4 flex-1 flex flex-col">
            <div class="space-y-3 mb-6 flex-1">
               <div class="text-sm">
                 <span class="text-gray-500 dark:text-gray-400">通知类型:</span>
                 <div class="mt-1 flex flex-wrap gap-1">
                   <template v-if="channel.notification_types && channel.notification_types.length > 0">
                     <span
                       v-for="type in channel.notification_types"
                       :key="type"
                       class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full"
                     >
                       {{ notificationTypeNames[type] || type }}
                     </span>
                   </template>
                   <span v-else class="text-gray-400 text-xs">全部类型</span>
                 </div>
               </div>
               <div class="flex items-center justify-between text-sm">
                 <span class="text-gray-500 dark:text-gray-400">最低优先级</span>
                 <span
                   :class="[
                     'px-2 py-1 text-xs rounded-full',
                     channel.min_priority === 'critical' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' :
                     channel.min_priority === 'high' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' :
                     channel.min_priority === 'normal' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' :
                     'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                   ]"
                 >
                   {{ getPriorityName(channel.min_priority) }}
                 </span>
               </div>
               <div class="flex items-center justify-between text-sm">
                 <span class="text-gray-500 dark:text-gray-400">重试次数</span>
                 <span class="text-gray-900 dark:text-white">{{ channel.retry_count || 3 }}</span>
               </div>
             </div>
            
            <!-- 操作按钮 - 固定在卡片底部 -->
            <div class="mt-auto space-y-3">
              <div class="flex flex-wrap gap-2">
                <button
                  @click="testChannel(channel.id)"
                  :disabled="testingChannels[channel.id]"
                  class="flex-1 px-3 py-2 text-sm bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center gap-1"
                >
                  <svg v-if="testingChannels[channel.id]" class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  测试
                </button>
                <button
                  @click="editChannel(channel)"
                  class="flex-1 px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center justify-center gap-1"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  编辑
                </button>
              </div>
              
              <div class="flex gap-2">
                <button
                  @click="toggleChannel(channel.id)"
                  :class="[
                    'flex-1 px-3 py-2 text-sm rounded-lg transition-all duration-200 font-medium',
                    channel.enabled
                      ? 'bg-orange-50 dark:bg-orange-900 text-orange-600 dark:text-orange-300 hover:bg-orange-100 dark:hover:bg-orange-800'
                      : 'bg-green-50 dark:bg-green-900 text-green-600 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-800'
                  ]"
                >
                  {{ channel.enabled ? '禁用' : '启用' }}
                </button>
                <button
                  @click="deleteChannel(channel.id)"
                  class="flex-1 px-3 py-2 text-sm bg-red-50 dark:bg-red-900 text-red-600 dark:text-red-300 hover:bg-red-100 dark:hover:bg-red-800 rounded-lg transition-colors"
                >
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑渠道模态框 -->
    <div v-if="showAddModal || showEditModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto border border-gray-200/50 dark:border-gray-700/50">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-blue-500 rounded-lg">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h7a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 dark:text-white">
              {{ editingChannel ? '编辑通知渠道' : '添加通知渠道' }}
            </h3>
          </div>
          <button 
            @click="closeChannelModal" 
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <form @submit.prevent="saveChannel" class="p-8 space-y-8">
          <!-- 基本信息 -->
          <div class="space-y-6">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">基本信息</h4>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">渠道名称</label>
                <input
                  v-model="channelForm.name"
                  type="text"
                  placeholder="输入渠道名称"
                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                  required
                />
              </div>
              
              <div>
                <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">渠道类型</label>
                <select
                  v-model="channelForm.channel_type"
                  @change="resetConfig"
                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                  required
                >
                  <option value="">请选择渠道类型</option>
                  <option value="telegram">📱 Telegram</option>
                  <option value="discord">🎮 Discord</option>
                  <option value="wechat">💬 微信</option>
                  <option value="webhook">🔗 Webhook</option>
                </select>
              </div>
            </div>
            
            <div class="flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
              <input
                v-model="channelForm.enabled"
                type="checkbox"
                class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              />
              <div>
                <span class="text-sm font-medium text-gray-900 dark:text-white">启用此渠道</span>
                <p class="text-xs text-gray-500 dark:text-gray-400">开启后将开始接收通知消息</p>
              </div>
            </div>
          </div>

          <!-- 通知配置 -->
          <div class="space-y-6">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">通知配置</h4>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- 通知类型 -->
              <div>
                <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                  通知类型
                </label>
                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4 space-y-3 max-h-48 overflow-y-auto">
                  <label class="flex items-center p-2 bg-white dark:bg-gray-700 rounded-lg shadow-sm">
                    <input
                      v-model="selectAllNotificationTypes"
                      type="checkbox"
                      @change="toggleAllNotificationTypes"
                      class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span class="ml-3 text-sm font-medium text-gray-900 dark:text-white">全选</span>
                  </label>
                  <div class="border-t border-gray-200 dark:border-gray-600 pt-2">
                    <label
                      v-for="(name, type) in notificationTypeNames"
                      :key="type"
                      class="flex items-center p-2 hover:bg-white dark:hover:bg-gray-700 rounded-lg transition-colors cursor-pointer"
                    >
                      <input
                        v-model="channelForm.notification_types"
                        :value="type"
                        type="checkbox"
                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span class="ml-3 text-sm text-gray-700 dark:text-gray-300">{{ name }}</span>
                    </label>
                  </div>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  💡 不选择任何类型将接收所有类型的通知
                </p>
              </div>

              <!-- 优先级配置 -->
              <div>
                <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                  最低优先级
                </label>
                <select
                  v-model="channelForm.min_priority"
                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                >
                  <option value="low">🔵 低优先级</option>
                  <option value="normal">🟡 普通优先级</option>
                  <option value="high">🟠 高优先级</option>
                  <option value="critical">🔴 紧急优先级</option>
                </select>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  🎯 只接收此优先级及以上的通知
                </p>
                
                <!-- 重试配置 -->
                <div class="mt-4">
                  <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    重试次数
                  </label>
                  <input
                    v-model.number="channelForm.retry_count"
                    type="number"
                    min="0"
                    max="10"
                    placeholder="3"
                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                  />
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    🔄 发送失败时的重试次数 (0-10)
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 渠道特定配置 -->
          <div v-if="channelForm.channel_type" class="space-y-6">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
              {{ getChannelTypeName(channelForm.channel_type) }} 配置
            </h4>
            
            <!-- Telegram 配置 -->
            <template v-if="channelForm.channel_type === 'telegram'">
              <div class="grid grid-cols-1 gap-6">
                <div>
                  <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    🤖 Bot Token
                  </label>
                  <input
                    v-model="channelForm.config.bot_token"
                    type="text"
                    placeholder="输入 Telegram Bot Token"
                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                    required
                  />
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    从 @BotFather 获取的机器人令牌
                  </p>
                </div>
                <div>
                  <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    💬 Chat ID
                  </label>
                  <input
                    v-model="channelForm.config.chat_id"
                    type="text"
                    placeholder="输入 Chat ID"
                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                    required
                  />
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    接收消息的聊天 ID (个人或群组)
                  </p>
                </div>
              </div>
            </template>

            <!-- Discord 配置 -->
            <template v-if="channelForm.channel_type === 'discord'">
              <div class="grid grid-cols-1 gap-6">
                <div>
                  <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    🔗 Webhook URL
                  </label>
                  <input
                    v-model="channelForm.config.webhook_url"
                    type="url"
                    placeholder="https://discord.com/api/webhooks/..."
                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                    required
                  />
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    Discord 频道的 Webhook 地址
                  </p>
                </div>
                <div>
                  <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    👤 用户名 (可选)
                  </label>
                  <input
                    v-model="channelForm.config.username"
                    type="text"
                    placeholder="机器人显示名称"
                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                  />
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    消息发送者的显示名称
                  </p>
                </div>
              </div>
            </template>

            <!-- 微信配置 -->
            <template v-if="channelForm.channel_type === 'wechat'">
              <div>
                <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                  🔑 Webhook Key
                </label>
                <input
                  v-model="channelForm.config.webhook_key"
                  type="text"
                  placeholder="输入企业微信 Webhook Key"
                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                  required
                />
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  企业微信群机器人的 Webhook 密钥
                </p>
              </div>
            </template>

            <!-- Webhook 配置 -->
            <template v-if="channelForm.channel_type === 'webhook'">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="md:col-span-2">
                  <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    🌐 Webhook URL
                  </label>
                  <input
                    v-model="channelForm.config.url"
                    type="url"
                    placeholder="https://example.com/webhook"
                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                    required
                  />
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    接收通知的 Webhook 地址
                  </p>
                </div>
                <div>
                  <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    📡 请求方法
                  </label>
                  <select
                    v-model="channelForm.config.method"
                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                  >
                    <option value="POST">POST</option>
                    <option value="GET">GET</option>
                    <option value="PUT">PUT</option>
                  </select>
                </div>
              </div>
            </template>
          </div>
          
          <!-- 操作按钮 -->
          <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              @click="closeChannelModal"
              class="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-xl font-medium transition-all duration-200"
            >
              取消
            </button>
            <button
              type="submit"
              class="px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5"
            >
              {{ editingChannel ? '更新渠道' : '创建渠道' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 发送通知模态框 -->
    <SendNotificationModal
      v-if="showSendModal"
      :visible="showSendModal"
      :channels="channels"
      :notification-types="notificationTypes"
      @close="showSendModal = false"
      @send="sendNotification"
    />
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import SendNotificationModal from '@/components/SendNotificationModal.vue'
import { toast, showConfirm } from '@/utils/helpers'

// 添加组件名称以修复ESLint警告
defineOptions({
  name: 'NotificationManager'
})

// 响应式数据
const channels = ref([])
const notificationTypes = ref([])
const showAddModal = ref(false)
const showEditModal = ref(false)
const showSendModal = ref(false)
const editingChannel = ref(null)
const isLoading = ref(false)
const testingChannels = ref({})

const channelForm = reactive({
  name: '',
  channel_type: '',
  enabled: true,
  notification_types: [],
  min_priority: 'normal',
  config: {}
})

// 通知类型选择相关
const selectAllNotificationTypes = ref(false)

// 通知类型名称映射
const notificationTypeNames = {
  'whmcs_stock': 'WHMCS库存通知',
  'whmcs_order': 'WHMCS订单通知', 
  'let_monitor': 'LET监控通知',
  'let_match': 'LET智能匹配',
  'system_status': '系统状态通知',
  'error_alert': '错误警报',
  'custom': '自定义通知'
}

// 监听渠道类型变化，重置配置
const resetConfig = () => {
  channelForm.config = {}
}

// 通知类型全选功能
const toggleAllNotificationTypes = () => {
  if (selectAllNotificationTypes.value) {
    channelForm.notification_types = Object.keys(notificationTypeNames)
  } else {
    channelForm.notification_types = []
  }
}

// 获取优先级名称
const getPriorityName = (priority) => {
  const priorityNames = {
    'low': '低',
    'normal': '普通',
    'high': '高',
    'critical': '紧急'
  }
  return priorityNames[priority] || priority
}

// 获取渠道类型名称
const getChannelTypeName = (type) => {
  const typeNames = {
    'telegram': 'Telegram',
    'discord': 'Discord',
    'wechat': '微信',
    'webhook': 'Webhook'
  }
  return typeNames[type] || type
}

// 获取渠道图标背景颜色
const getChannelIconBg = (type) => {
  const bgClasses = {
    'telegram': 'bg-blue-500',
    'discord': 'bg-indigo-500',
    'wechat': 'bg-green-500',
    'webhook': 'bg-gray-500'
  }
  return bgClasses[type] || 'bg-gray-500'
}

// 方法
const loadChannels = async () => {
  try {
    isLoading.value = true
    const response = await fetch('/api/notifications/channels')
    const data = await response.json()
    channels.value = data.channels || []
    notificationTypes.value = data.notification_types || []
  } catch (error) {
    console.error('加载通知渠道失败:', error)
  } finally {
    isLoading.value = false
  }
}

const testChannel = async (channelId) => {
  try {
    testingChannels.value[channelId] = true
    const response = await fetch(`/api/notifications/channels/${channelId}/test`, {
      method: 'POST'
    })
    const result = await response.json()
    
    if (result.success) {
      toast('success', '测试通知发送成功！')
    } else {
      toast('error', `测试失败: ${result.message}`)
    }
  } catch (error) {
    console.error('测试渠道失败:', error)
    toast('error', '测试失败，请检查网络连接')
  } finally {
    testingChannels.value[channelId] = false
  }
}

const editChannel = (channel) => {
  editingChannel.value = channel
  Object.assign(channelForm, {
    name: channel.name,
    channel_type: channel.channel_type,
    enabled: channel.enabled,
    notification_types: channel.notification_types || [],
    min_priority: channel.min_priority || 'normal',
    config: { ...channel.config }
  })
  
  // 更新全选状态
  selectAllNotificationTypes.value = channelForm.notification_types.length === Object.keys(notificationTypeNames).length
  
  showEditModal.value = true
}

const toggleChannel = async (channelId) => {
  try {
    const channel = channels.value.find(c => c.id === channelId)
    const response = await fetch(`/api/notifications/channels/${channelId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ enabled: !channel.enabled })
    })
    
    if (response.ok) {
      channel.enabled = !channel.enabled
      toast('success', `渠道已${channel.enabled ? '启用' : '禁用'}`)
    } else {
      toast('error', '切换渠道状态失败')
    }
  } catch (error) {
    console.error('切换渠道状态失败:', error)
    toast('error', '操作失败，请重试')
  }
}

const deleteChannel = async (channelId) => {
  const confirmed = await showConfirm('确认删除', '确定要删除这个通知渠道吗？')
  if (!confirmed) return
  
  try {
    const response = await fetch(`/api/notifications/channels/${channelId}`, {
      method: 'DELETE'
    })
    
    if (response.ok) {
      channels.value = channels.value.filter(c => c.id !== channelId)
      toast('success', '渠道删除成功')
    } else {
      toast('error', '删除失败')
    }
  } catch (error) {
    console.error('删除渠道失败:', error)
    toast('error', '删除失败，请重试')
  }
}

const saveChannel = async () => {
  try {
    const url = editingChannel.value 
      ? `/api/notifications/channels/${editingChannel.value.id}`
      : '/api/notifications/channels'
    
    const method = editingChannel.value ? 'PUT' : 'POST'
    
    const payload = {
      name: channelForm.name,
      channel_type: channelForm.channel_type,
      enabled: channelForm.enabled,
      notification_types: channelForm.notification_types,
      min_priority: channelForm.min_priority,
      config: channelForm.config
    }
    
    const response = await fetch(url, {
      method,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    })
    
    if (response.ok) {
      await loadChannels()
      closeChannelModal()
      toast('success', editingChannel.value ? '渠道更新成功' : '渠道创建成功')
    } else {
      const error = await response.json()
      toast('error', `保存失败: ${error.detail}`)
    }
  } catch (error) {
    console.error('保存渠道失败:', error)
    toast('error', '保存失败，请重试')
  }
}

const closeChannelModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  editingChannel.value = null
  Object.assign(channelForm, {
    name: '',
    channel_type: '',
    enabled: true,
    notification_types: [],
    min_priority: 'normal',
    config: {}
  })
  selectAllNotificationTypes.value = false
}

const sendNotification = async (notificationData) => {
  try {
    const response = await fetch('/api/notifications/send', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(notificationData)
    })
    
    const result = await response.json()
    
    if (result.success) {
      showSendModal.value = false
      toast('success', `通知发送成功！成功: ${result.successful_channels.length}, 失败: ${result.failed_channels.length}`)
    } else {
      toast('error', `发送失败: ${result.message}`)
    }
  } catch (error) {
    console.error('发送通知失败:', error)
    toast('error', '发送失败，请重试')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadChannels()
})
</script>

<style scoped>
.spinner {
  border-top-color: transparent;
  border-left-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background-color: #f3f4f6;
  border-radius: 9999px;
}

:global(.dark) .overflow-y-auto::-webkit-scrollbar-track {
  background-color: #374151;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 9999px;
}

:global(.dark) .overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #6b7280;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

:global(.dark) .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* 表单输入框焦点效果 */
.form-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 按钮悬停效果 */
.btn-hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
</style>