# WHMCS 库存监控管理系统 - 前端

这是使用 Vue 3 + Vite + Tailwind CSS 重构的现代化前端项目。

## 🚀 技术栈

- **Vue 3** - 采用 Composition API
- **Vite** - 快速的构建工具
- **Vue Router** - 路由管理
- **Pinia** - 状态管理
- **Tailwind CSS** - 样式框架
- **Axios** - HTTP 客户端
- **SweetAlert2** - 美观的弹窗组件

## 📁 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/        # 公共组件
│   │   ├── Sidebar.vue   # 侧边栏组件
│   │   └── TopNavbar.vue # 顶部导航栏
│   ├── views/            # 页面组件
│   │   ├── Dashboard.vue # 仪表板
│   │   ├── Sites.vue     # 网站管理
│   │   ├── Products.vue  # 产品管理
│   │   └── Settings.vue  # 设置页面
│   ├── stores/           # Pinia 状态管理
│   │   └── main.js       # 主要状态
│   ├── utils/            # 工具函数
│   │   ├── api.js        # API 封装
│   │   └── helpers.js    # 辅助函数
│   ├── router/           # 路由配置
│   │   └── index.js
│   ├── App.vue           # 主应用组件
│   ├── LoginApp.vue      # 登录应用组件
│   ├── main.js           # 主应用入口
│   ├── login.js          # 登录应用入口
│   └── style.css         # 全局样式
├── index.html            # 主页面入口
├── login.html           # 登录页面入口
├── package.json         # 项目配置
├── vite.config.js       # Vite 配置
├── tailwind.config.js   # Tailwind 配置
└── postcss.config.js    # PostCSS 配置
```

## 🛠️ 开发环境设置

### 1. 安装依赖

```bash
cd frontend
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

开发服务器将在 http://localhost:5173 启动

### 3. 构建生产版本

```bash
npm run build
```

构建完成后，文件将输出到 `../static` 目录

## 🔧 配置说明

### Vite 配置

- 构建输出目录设置为 `../static`
- 多页面应用配置（主页面和登录页面）
- API 代理设置，开发时代理到后端服务器

### 样式系统

- 使用 Tailwind CSS 作为主要样式框架
- 支持暗色/亮色主题切换
- 响应式设计，支持移动端

### 状态管理

- 使用 Pinia 进行状态管理
- 模块化设计，易于维护
- 响应式数据绑定

## 🎨 功能特性

### 登录页面

- 密码输入框
- 显示/隐藏密码功能
- 错误提示和动画效果
- 服务器连接状态显示

### 主应用

- 响应式侧边栏
- 暗色/亮色主题切换
- 实时状态监控
- 多页面路由导航

### 组件化设计

- 高度组件化的架构
- 可复用的 UI 组件
- 统一的设计语言

## 📱 响应式支持

- 桌面端：侧边栏固定显示
- 移动端：可折叠侧边栏
- 平板端：自适应布局

## 🔄 与后端集成

前端通过以下 API 与后端通信：

- `/api/login` - 用户登录
- `/api/status` - 获取系统状态
- `/api/sites` - 网站管理
- `/api/sites/{id}/products` - 产品管理
- `/api/config` - 配置管理

## 🚀 部署

构建完成后，生成的静态文件会自动放到 `../static` 目录中，可以直接被后端服务器提供服务。

## 💡 开发建议

1. 组件开发时遵循 Vue 3 Composition API 最佳实践
2. 使用 Tailwind CSS 类名进行样式设计
3. 状态管理使用 Pinia store
4. API 调用统一使用 `@/utils/api.js` 中的封装函数
5. 遵循响应式设计原则，确保移动端体验
