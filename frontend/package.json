{"name": "integrated-monitor-frontend", "version": "1.0.0", "description": "集成库存监控系统前端 - 支持WHMCS和LET多平台管理", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --fix"}, "dependencies": {"axios": "^1.6.0", "pinia": "^2.1.0", "sweetalert2": "^11.10.0", "vue": "^3.4.0", "vue-router": "^4.2.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.0", "globals": "^16.2.0", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "vite": "^5.0.0"}}