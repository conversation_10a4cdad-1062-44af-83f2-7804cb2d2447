services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: integrated-monitor
    restart: always
    ports:
      - "8000:8000"
    env_file:
      - .env
    volumes:
      - ./data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    depends_on:
      - cfbypass
  cfbypass:
    image: ghcr.io/sarperavci/cloudflarebypassforscraping:latest
    container_name: cfbypass
    restart: always
