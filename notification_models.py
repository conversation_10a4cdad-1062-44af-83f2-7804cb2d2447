#!/usr/bin/env python3
"""
高级通知系统数据模型
支持多通道、灵活配置的通知系统
"""

from enum import Enum
from uuid import uuid4

from pydantic import BaseModel, Field, validator


class NotificationType(str, Enum):
    """通知类型枚举"""

    WHMCS_STOCK = "whmcs_stock"  # WHMCS库存监控
    WHMCS_ORDER = "whmcs_order"  # WHMCS订单相关
    LET_MONITOR = "let_monitor"  # LET RSS监控
    LET_MATCH = "let_match"  # LET智能匹配
    SYSTEM_STATUS = "system_status"  # 系统状态
    ERROR_ALERT = "error_alert"  # 错误警报
    CUSTOM = "custom"  # 自定义通知


class NotificationChannelType(str, Enum):
    """通知渠道类型枚举"""

    TELEGRAM = "telegram"
    DISCORD = "discord"
    WECHAT = "wechat"
    EMAIL = "email"
    WEBHOOK = "webhook"
    DINGTALK = "dingtalk"
    SLACK = "slack"


class NotificationPriority(str, Enum):
    """通知优先级"""

    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class NotificationChannelConfig(BaseModel):
    """通知渠道配置"""

    # 基础信息
    id: str = Field(default_factory=lambda: str(uuid4()), description="通道唯一ID")
    name: str = Field(..., description="通道名称，用于标识")
    channel_type: NotificationChannelType = Field(..., description="通道类型")
    enabled: bool = Field(True, description="是否启用")

    # 通知类型过滤
    notification_types: set[NotificationType] = Field(
        default_factory=set, description="允许的通知类型，空集合表示允许所有类型"
    )

    # 优先级过滤
    min_priority: NotificationPriority = Field(NotificationPriority.LOW, description="最低通知优先级")

    # 渠道特定配置
    config: dict[str, str | int | bool | dict | list | None] = Field(
        default_factory=dict, description="渠道特定的配置参数"
    )

    # 高级设置
    rate_limit: int | None = Field(None, description="速率限制（每分钟最大消息数），None表示无限制")
    quiet_hours: dict[str, str] | None = Field(None, description="静默时间段，格式: {'start': 'HH:MM', 'end': 'HH:MM'}")
    retry_count: int = Field(3, description="失败重试次数")
    timeout: int = Field(30, description="超时时间（秒）")

    # 消息格式化
    message_template: str | None = Field(None, description="自定义消息模板")

    @validator("notification_types", pre=True)
    def validate_notification_types(cls, v):
        if isinstance(v, list):
            return set(v)
        elif isinstance(v, str):
            # 处理从JSON加载时的字符串格式："{<NotificationType.WHMCS_STOCK: 'whmcs_stock'>}"
            import re

            if v.startswith("{") and v.endswith("}"):
                # 提取枚举值
                matches = re.findall(r"'([^']*)'", v)
                return {NotificationType(match) for match in matches if match in [t.value for t in NotificationType]}
            return set()
        elif isinstance(v, set):
            # 确保set中的元素都是NotificationType
            return {NotificationType(item) if isinstance(item, str) else item for item in v}
        return v

    @validator("config", pre=True)
    def validate_config(cls, v):
        """验证配置字段，确保类型正确"""
        if not isinstance(v, dict):
            return {}

        # 确保headers字段是字典类型
        if "headers" in v and not isinstance(v["headers"], dict):
            v["headers"] = {}

        return v

    def should_send_notification(self, notification_type: NotificationType, priority: NotificationPriority) -> bool:
        """判断是否应该发送通知"""
        if not self.enabled:
            return False

        # 检查通知类型过滤
        if self.notification_types and notification_type not in self.notification_types:
            return False

        # 检查优先级过滤
        priority_order = [
            NotificationPriority.LOW,
            NotificationPriority.NORMAL,
            NotificationPriority.HIGH,
            NotificationPriority.CRITICAL,
        ]

        if priority_order.index(priority) < priority_order.index(self.min_priority):
            return False

        return True


class TelegramChannelConfig(NotificationChannelConfig):
    """Telegram通道配置"""

    channel_type: NotificationChannelType = NotificationChannelType.TELEGRAM

    def __init__(self, **data):
        super().__init__(**data)
        # 确保必要的配置存在
        if "bot_token" not in self.config:
            self.config["bot_token"] = ""
        if "chat_id" not in self.config:
            self.config["chat_id"] = ""

    @property
    def bot_token(self) -> str:
        return self.config.get("bot_token", "")

    @property
    def chat_id(self) -> str:
        return self.config.get("chat_id", "")


class DiscordChannelConfig(NotificationChannelConfig):
    """Discord通道配置"""

    channel_type: NotificationChannelType = NotificationChannelType.DISCORD

    def __init__(self, **data):
        super().__init__(**data)
        if "webhook_url" not in self.config:
            self.config["webhook_url"] = ""
        if "username" not in self.config:
            self.config["username"] = "监控机器人"

    @property
    def webhook_url(self) -> str:
        return self.config.get("webhook_url", "")

    @property
    def username(self) -> str:
        return self.config.get("username", "监控机器人")


class WeChatChannelConfig(NotificationChannelConfig):
    """微信通道配置"""

    channel_type: NotificationChannelType = NotificationChannelType.WECHAT

    def __init__(self, **data):
        super().__init__(**data)
        if "webhook_key" not in self.config:
            self.config["webhook_key"] = ""

    @property
    def webhook_key(self) -> str:
        return self.config.get("webhook_key", "")


class WebhookChannelConfig(NotificationChannelConfig):
    """自定义Webhook通道配置"""

    channel_type: NotificationChannelType = NotificationChannelType.WEBHOOK

    def __init__(self, **data):
        super().__init__(**data)
        if "url" not in self.config:
            self.config["url"] = ""
        if "method" not in self.config:
            self.config["method"] = "POST"
        if "headers" not in self.config:
            self.config["headers"] = {}

    @property
    def url(self) -> str:
        return self.config.get("url", "")

    @property
    def method(self) -> str:
        return self.config.get("method", "POST")

    @property
    def headers(self) -> dict[str, str]:
        return self.config.get("headers", {})


class EmailChannelConfig(NotificationChannelConfig):
    """邮件通道配置"""

    channel_type: NotificationChannelType = NotificationChannelType.EMAIL

    def __init__(self, **data):
        super().__init__(**data)
        default_config = {
            "smtp_server": "",
            "smtp_port": 587,
            "username": "",
            "password": "",
            "from_email": "",
            "to_emails": [],
            "use_tls": True,
        }
        for key, value in default_config.items():
            if key not in self.config:
                self.config[key] = value


class NotificationMessage(BaseModel):
    """通知消息模型"""

    # 基础信息
    title: str = Field(..., description="消息标题")
    content: str = Field(..., description="消息内容")
    notification_type: NotificationType = Field(..., description="通知类型")
    priority: NotificationPriority = Field(NotificationPriority.NORMAL, description="优先级")

    # 元数据
    source: str = Field("system", description="消息来源")
    timestamp: str | None = Field(None, description="时间戳")
    tags: list[str] = Field(default_factory=list, description="标签")

    # 附加数据
    extra_data: dict[str, str | int | bool | None] = Field(default_factory=dict, description="附加数据")

    # 格式化选项
    format_markdown: bool = Field(True, description="是否支持Markdown格式")

    def format_for_channel(self, channel_config: NotificationChannelConfig) -> str:
        """根据渠道配置格式化消息"""
        if channel_config.message_template:
            # 使用自定义模板
            return channel_config.message_template.format(
                title=self.title,
                content=self.content,
                source=self.source,
                priority=self.priority.value,
                **self.extra_data,
            )
        else:
            # 使用默认格式
            formatted = f"**{self.title}**\n\n{self.content}"

            if self.source != "system":
                formatted += f"\n\n📍 来源: {self.source}"

            if self.priority != NotificationPriority.NORMAL:
                priority_icons = {
                    NotificationPriority.LOW: "🔵",
                    NotificationPriority.HIGH: "🟡",
                    NotificationPriority.CRITICAL: "🔴",
                }
                icon = priority_icons.get(self.priority, "⚪")
                formatted += f"\n{icon} 优先级: {self.priority.value}"

            if self.tags:
                formatted += f"\n🏷️ 标签: {', '.join(self.tags)}"

            return formatted


class NotificationChannelManager(BaseModel):
    """通知渠道管理器"""

    channels: list[NotificationChannelConfig] = Field(default_factory=list, description="通知渠道列表")

    def add_channel(self, channel: NotificationChannelConfig) -> str:
        """添加通知渠道"""
        self.channels.append(channel)
        return channel.id

    def remove_channel(self, channel_id: str) -> bool:
        """移除通知渠道"""
        for i, channel in enumerate(self.channels):
            if channel.id == channel_id:
                del self.channels[i]
                return True
        return False

    def update_channel(self, channel_id: str, updates: dict) -> bool:
        """更新通知渠道配置"""
        for channel in self.channels:
            if channel.id == channel_id:
                for key, value in updates.items():
                    if hasattr(channel, key):
                        setattr(channel, key, value)
                return True
        return False

    def get_channel(self, channel_id: str) -> NotificationChannelConfig | None:
        """获取指定通知渠道"""
        for channel in self.channels:
            if channel.id == channel_id:
                return channel
        return None

    def get_channels_for_notification(
        self, notification_type: NotificationType, priority: NotificationPriority = NotificationPriority.NORMAL
    ) -> list[NotificationChannelConfig]:
        """获取适用于指定通知的渠道列表"""
        return [channel for channel in self.channels if channel.should_send_notification(notification_type, priority)]

    def get_enabled_channels(self) -> list[NotificationChannelConfig]:
        """获取所有启用的通知渠道"""
        return [channel for channel in self.channels if channel.enabled]

    def get_channels_by_type(self, channel_type: NotificationChannelType) -> list[NotificationChannelConfig]:
        """按类型获取通知渠道"""
        return [channel for channel in self.channels if channel.channel_type == channel_type]


# 渠道工厂函数
def create_channel_config(channel_type: NotificationChannelType, **kwargs) -> NotificationChannelConfig:
    """创建通知渠道配置的工厂函数"""
    channel_classes = {
        NotificationChannelType.TELEGRAM: TelegramChannelConfig,
        NotificationChannelType.DISCORD: DiscordChannelConfig,
        NotificationChannelType.WECHAT: WeChatChannelConfig,
        NotificationChannelType.WEBHOOK: WebhookChannelConfig,
        NotificationChannelType.EMAIL: EmailChannelConfig,
    }

    channel_class = channel_classes.get(channel_type, NotificationChannelConfig)
    return channel_class(channel_type=channel_type, **kwargs)


# 预定义的通知类型组合
NOTIFICATION_TYPE_GROUPS = {
    "all": set(NotificationType),
    "monitoring": {NotificationType.WHMCS_STOCK, NotificationType.LET_MONITOR, NotificationType.LET_MATCH},
    "system": {NotificationType.SYSTEM_STATUS, NotificationType.ERROR_ALERT},
    "whmcs_only": {NotificationType.WHMCS_STOCK, NotificationType.WHMCS_ORDER},
    "let_only": {NotificationType.LET_MONITOR, NotificationType.LET_MATCH},
}
