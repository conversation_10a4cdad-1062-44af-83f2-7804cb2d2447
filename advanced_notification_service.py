#!/usr/bin/env python3
"""
高级通知服务
支持多渠道、灵活配置的通知系统
"""

import asyncio
from datetime import datetime, time
import json
import logging
import os
from pathlib import Path
import urllib.parse as parse

import aiohttp

from notification_models import (
    NotificationChannelConfig,
    NotificationChannelManager,
    NotificationChannelType,
    NotificationMessage,
    NotificationPriority,
    NotificationType,
)


class ChannelSender:
    """通知渠道发送器基类"""

    def __init__(self, session: aiohttp.ClientSession):
        self.session = session
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    async def send(self, channel: NotificationChannelConfig, message: NotificationMessage) -> bool:
        """发送通知消息"""
        raise NotImplementedError


class TelegramSender(ChannelSender):
    """Telegram发送器"""

    async def send(self, channel: NotificationChannelConfig, message: NotificationMessage) -> bool:
        """发送Telegram消息"""
        bot_token = channel.config.get("bot_token", "")
        chat_id = channel.config.get("chat_id", "")
        if not bot_token or not chat_id:
            self.logger.warning(f"Telegram渠道 {channel.name} 配置不完整")
            return False

        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"

        # 格式化消息
        formatted_message = message.format_for_channel(channel)

        def escape_telegrambot_underscore(txt: str) -> str:
            lines = txt.split("\n")
            for i, line in enumerate(lines):
                # 一个_，不被``包裹，才进行转义，这里的意思是一个完整的``匹配
                if "_" not in line:
                    continue

                def stack_match(line: str) -> str:
                    protected_ranges = []
                    chars = list(line)
                    i = 0
                    while i < len(chars):
                        if chars[i] == "`":
                            j = i + 1
                            while j < len(chars):
                                if chars[j] == "`":
                                    protected_ranges.append((i, j))
                                    i = j
                                    break
                                j += 1
                        i += 1

                    result = []
                    for i, char in enumerate(chars):
                        if char == "_":
                            is_protected = False
                            for start, end in protected_ranges:
                                if start < i < end:
                                    is_protected = True
                                    break

                            if is_protected:
                                result.append("_")
                            else:
                                result.append("\\_")
                        else:
                            result.append(char)

                    return "".join(result)

                lines[i] = stack_match(line)

            return "\n".join(lines)

        formatted_message = escape_telegrambot_underscore(formatted_message)

        logging.info(f"Telegram formatted_message: {formatted_message}")
        logging.info(f"Chat_id: {chat_id}")
        logging.info(f"Bot_token: {bot_token}")
        logging.info(f"Url: {url}")
        logging.info(f"Message: {message}")
        logging.info(f"Channel: {channel}")
        payload = {
            "chat_id": chat_id,
            "text": formatted_message,
            "parse_mode": "Markdown" if message.format_markdown else None,
            "disable_web_page_preview": True,
        }
        logging.info(f"Telegram payload: {payload}")

        try:
            async with self.session.post(
                url,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=channel.timeout),
                proxy=os.getenv("HTTP_PROXY") or None,
            ) as response:
                if response.status == 200:
                    self.logger.debug(f"Telegram消息发送成功: {channel.name}")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"Telegram发送失败 {channel.name}: {response.status} - {error_text}")
                    return False
        except Exception as e:
            self.logger.error(f"Telegram发送异常 {channel.name}: {e}")
            return False


class DiscordSender(ChannelSender):
    """Discord发送器"""

    async def send(self, channel: NotificationChannelConfig, message: NotificationMessage) -> bool:
        """发送Discord消息"""
        webhook_url = channel.config.get("webhook_url", "")
        if not webhook_url:
            self.logger.warning(f"Discord渠道 {channel.name} webhook URL未配置")
            return False

        # 格式化消息
        formatted_message = message.format_for_channel(channel)

        # 构建Discord embed
        embed = {
            "title": message.title,
            "description": message.content,
            "color": self._get_priority_color(message.priority),
            "timestamp": datetime.now().isoformat(),
            "footer": {"text": f"来源: {message.source}"},
        }

        # 添加字段
        if message.tags:
            embed["fields"] = [{"name": "标签", "value": ", ".join(message.tags), "inline": True}]

        username = channel.config.get("username", "监控机器人")
        payload = {"username": username, "embeds": [embed]}

        try:
            async with self.session.post(
                webhook_url, json=payload, timeout=aiohttp.ClientTimeout(total=channel.timeout)
            ) as response:
                if response.status in (200, 204):
                    self.logger.debug(f"Discord消息发送成功: {channel.name}")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"Discord发送失败 {channel.name}: {response.status} - {error_text}")
                    return False
        except Exception as e:
            self.logger.error(f"Discord发送异常 {channel.name}: {e}")
            return False

    def _get_priority_color(self, priority: NotificationPriority) -> int:
        """获取优先级对应的颜色"""
        colors = {
            NotificationPriority.LOW: 0x0099FF,  # 蓝色
            NotificationPriority.NORMAL: 0x00FF99,  # 绿色
            NotificationPriority.HIGH: 0xFFAA00,  # 橙色
            NotificationPriority.CRITICAL: 0xFF0000,  # 红色
        }
        return colors.get(priority, 0x00FF99)


class WeChatSender(ChannelSender):
    """微信发送器"""

    async def send(self, channel: NotificationChannelConfig, message: NotificationMessage) -> bool:
        """发送微信消息"""
        webhook_key = channel.config.get("webhook_key", "")
        if not webhook_key:
            self.logger.warning(f"微信渠道 {channel.name} webhook key未配置")
            return False

        url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={webhook_key}"

        # 格式化消息
        formatted_message = message.format_for_channel(channel)

        payload = {"msgtype": "markdown", "markdown": {"content": formatted_message}}

        try:
            async with self.session.post(
                url, json=payload, timeout=aiohttp.ClientTimeout(total=channel.timeout)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("errcode") == 0:
                        self.logger.debug(f"微信消息发送成功: {channel.name}")
                        return True
                    else:
                        self.logger.error(f"微信发送失败 {channel.name}: {result}")
                        return False
                else:
                    error_text = await response.text()
                    self.logger.error(f"微信发送失败 {channel.name}: {response.status} - {error_text}")
                    return False
        except Exception as e:
            self.logger.error(f"微信发送异常 {channel.name}: {e}")
            return False


class WebhookSender(ChannelSender):
    """自定义Webhook发送器"""

    async def send(self, channel: NotificationChannelConfig, message: NotificationMessage) -> bool:
        """发送Webhook消息"""
        # 确保渠道类型正确
        if channel.channel_type != NotificationChannelType.WEBHOOK:
            self.logger.error(f"渠道类型错误: 期望webhook，实际为 {channel.channel_type}")
            return False

        webhook_url = channel.config.get("url", "")
        if not webhook_url:
            self.logger.warning(f"Webhook渠道 {channel.name} URL未配置")
            return False

        # 构建请求载荷
        # payload = {
        #    "title": message.title,
        #    "content": message.content,
        #    "notification_type": message.notification_type.value,
        #    "priority": message.priority.value,
        #    "source": message.source,
        #    "timestamp": datetime.now().isoformat(),
        #    "tags": message.tags,
        #    "extra_data": message.extra_data,
        #    "formatted_message": message.format_for_channel(channel),
        # }
        #
        # 构建请求头
        method = channel.config.get("method", "POST")
        custom_headers = channel.config.get("headers", {})
        headers = {"Content-Type": "application/json", "User-Agent": "WHMCS-Monitor/1.0", **custom_headers}
        message_ser = message.format_for_channel(channel)
        message_ser = parse.quote(message_ser)
        try:
            method = method.upper()
            if method == "GET":
                async with self.session.get(
                    webhook_url.replace(r"{message}", message_ser),
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=channel.timeout),
                ) as response:
                    success = response.status < 400
            else:
                async with self.session.request(
                    method,
                    webhook_url.replace(r"{message}", message_ser),
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=channel.timeout),
                ) as response:
                    success = response.status < 400

            if success:
                self.logger.debug(f"Webhook消息发送成功: {channel.name}")
                return True
            else:
                error_text = await response.text()
                self.logger.error(f"Webhook发送失败 {channel.name}: {response.status} - {error_text}")
                return False

        except Exception as e:
            self.logger.error(f"Webhook发送异常 {channel.name}: {e}")
            return False


class AdvancedNotificationService:
    """高级通知服务"""

    def __init__(self, config_file: str = "data/notification_channels.json"):
        self.config_file = Path(config_file)
        self.channel_manager = NotificationChannelManager()
        self.session: aiohttp.ClientSession | None = None
        self.setup_logging()
        self.logger = logging.getLogger(__name__)

        # 渠道发送器映射
        self.senders: dict[NotificationChannelType, ChannelSender] = {}

        # 速率限制追踪
        self.rate_limit_tracker: dict[str, list[datetime]] = {}

        # 加载配置
        self.load_config()

    def setup_logging(self):
        """设置日志配置"""
        log_dir = Path("data/logs")
        log_dir.mkdir(parents=True, exist_ok=True)

        # 为文件和控制台定义不同的格式
        file_log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        console_log_format = "\033[36m%(asctime)s\033[0m - %(name)s - \033[1;33m%(levelname)s\033[0m - %(message)s"

        logger = logging.getLogger(__name__)

        # 防止重复添加handler
        if not logger.handlers:
            logger.setLevel(logging.INFO)
            logger.propagate = False  # 防止日志向上传播到root logger

            # 创建文件handler (无颜色)
            file_handler = logging.FileHandler(log_dir / "notification_service.log", encoding="utf-8")
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(logging.Formatter(file_log_format))

            # 创建控制台handler (有颜色)
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(logging.Formatter(console_log_format))

            logger.addHandler(file_handler)
            logger.addHandler(console_handler)

    async def __aenter__(self):
        """异步上下文管理器入口"""
        # 创建HTTP会话
        headers = {"User-Agent": "WHMCS-Monitor-Advanced-Notification/1.0"}
        connector = aiohttp.TCPConnector(limit=20)
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(headers=headers, connector=connector, timeout=timeout)

        # 初始化发送器
        self.senders = {
            NotificationChannelType.TELEGRAM: TelegramSender(self.session),
            NotificationChannelType.DISCORD: DiscordSender(self.session),
            NotificationChannelType.WECHAT: WeChatSender(self.session),
            NotificationChannelType.WEBHOOK: WebhookSender(self.session),
        }

        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    def load_config(self):
        """加载配置文件"""
        self.config_file.parent.mkdir(parents=True, exist_ok=True)

        if self.config_file.exists():
            try:
                with open(self.config_file, encoding="utf-8") as f:
                    data = json.load(f)
                    self.channel_manager = NotificationChannelManager(**data)
                self.logger.info(f"已加载 {len(self.channel_manager.channels)} 个通知渠道")
            except Exception as e:
                self.logger.error(f"加载通知配置失败: {e}")
                self._create_default_config()
        else:
            self._create_default_config()

    def save_config(self):
        """保存配置文件"""
        try:
            # 自定义序列化函数处理set和enum
            def custom_serializer(obj):
                if isinstance(obj, set):
                    return list(obj)
                elif hasattr(obj, "value"):  # 处理枚举类型
                    return obj.value
                elif hasattr(obj, "__dict__"):
                    return obj.__dict__
                return str(obj)

            data = self.channel_manager.dict()
            # 确保notification_types序列化为列表
            for channel in data.get("channels", []):
                if "notification_types" in channel and isinstance(channel["notification_types"], set):
                    channel["notification_types"] = list(channel["notification_types"])

            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=custom_serializer)
            self.logger.debug("通知配置已保存")
        except Exception as e:
            self.logger.error(f"保存通知配置失败: {e}")

    def _create_default_config(self):
        """创建默认配置"""
        self.logger.info("创建默认通知配置")
        self.channel_manager = NotificationChannelManager()
        self.save_config()

    def _check_rate_limit(self, channel: NotificationChannelConfig) -> bool:
        """检查速率限制"""
        if not channel.rate_limit:
            return True

        now = datetime.now()
        channel_id = channel.id

        # 初始化追踪记录
        if channel_id not in self.rate_limit_tracker:
            self.rate_limit_tracker[channel_id] = []

        # 清理过期记录（超过1分钟）
        cutoff = (
            now.replace(second=now.second - 60)
            if now.second >= 60
            else now.replace(minute=now.minute - 1, second=now.second + 60 - 60)
        )
        self.rate_limit_tracker[channel_id] = [
            timestamp for timestamp in self.rate_limit_tracker[channel_id] if timestamp > cutoff
        ]

        # 检查是否超出限制
        if len(self.rate_limit_tracker[channel_id]) >= channel.rate_limit:
            return False

        # 记录当前发送
        self.rate_limit_tracker[channel_id].append(now)
        return True

    def _check_quiet_hours(self, channel: NotificationChannelConfig) -> bool:
        """检查是否在静默时间"""
        if not channel.quiet_hours:
            return True

        try:
            now_time = datetime.now().time()
            start_time = time.fromisoformat(channel.quiet_hours["start"])
            end_time = time.fromisoformat(channel.quiet_hours["end"])

            if start_time <= end_time:
                # 同一天内的时间段
                return not (start_time <= now_time <= end_time)
            else:
                # 跨天的时间段
                return not (now_time >= start_time or now_time <= end_time)
        except (KeyError, ValueError) as e:
            self.logger.warning(f"静默时间配置错误 {channel.name}: {e}")
            return True

    async def send_notification(
        self, message: NotificationMessage, target_channels: list[str] | None = None
    ) -> dict[str, bool]:
        """发送通知"""
        results = {}

        # 确定目标渠道
        if target_channels:
            channels = [self.channel_manager.get_channel(channel_id) for channel_id in target_channels]
            channels = [ch for ch in channels if ch is not None]
        else:
            channels = self.channel_manager.get_channels_for_notification(message.notification_type, message.priority)

        if not channels:
            self.logger.warning(f"没有找到适用的通知渠道: {message.notification_type}, 优先级: {message.priority}")
            return results

        # 并发发送通知
        send_tasks = []
        for channel in channels:
            if not channel.enabled:
                continue

            # 检查速率限制
            if not self._check_rate_limit(channel):
                self.logger.warning(f"渠道 {channel.name} 触发速率限制")
                results[channel.id] = False
                continue

            # 检查静默时间
            if not self._check_quiet_hours(channel):
                self.logger.debug(f"渠道 {channel.name} 在静默时间内")
                results[channel.id] = False
                continue

            # 创建发送任务
            task = asyncio.create_task(self._send_to_channel_with_retry(channel, message))
            send_tasks.append((channel.id, task))

        # 等待所有发送任务完成
        for channel_id, task in send_tasks:
            try:
                success = await task
                results[channel_id] = success
            except Exception as e:
                self.logger.error(f"发送任务异常 {channel_id}: {e}")
                results[channel_id] = False

        return results

    async def _send_to_channel_with_retry(
        self, channel: NotificationChannelConfig, message: NotificationMessage
    ) -> bool:
        """带重试的渠道发送"""
        sender = self.senders.get(channel.channel_type)
        if not sender:
            self.logger.warning(f"不支持的渠道类型: {channel.channel_type}")
            return False

        for attempt in range(channel.retry_count + 1):
            try:
                success = await sender.send(channel, message)
                if success:
                    return True

                if attempt < channel.retry_count:
                    # 指数退避重试
                    delay = min(2**attempt, 60)
                    await asyncio.sleep(delay)

            except Exception as e:
                self.logger.error(f"发送失败 {channel.name} (尝试 {attempt + 1}): {e}")
                if attempt < channel.retry_count:
                    delay = min(2**attempt, 60)
                    await asyncio.sleep(delay)

        return False

    # 渠道管理方法
    def add_channel(self, channel: NotificationChannelConfig) -> str:
        """添加通知渠道"""
        channel_id = self.channel_manager.add_channel(channel)
        self.save_config()
        self.logger.info(f"添加通知渠道: {channel.name} ({channel.channel_type})")
        return channel_id

    def remove_channel(self, channel_id: str) -> bool:
        """移除通知渠道"""
        success = self.channel_manager.remove_channel(channel_id)
        if success:
            self.save_config()
            self.logger.info(f"移除通知渠道: {channel_id}")
        return success

    def update_channel(self, channel_id: str, updates: dict) -> bool:
        """更新通知渠道"""
        success = self.channel_manager.update_channel(channel_id, updates)
        if success:
            self.save_config()
            self.logger.info(f"更新通知渠道: {channel_id}")
        return success

    def get_channels(self) -> list[NotificationChannelConfig]:
        """获取所有通知渠道"""
        return self.channel_manager.channels

    def get_channel(self, channel_id: str) -> NotificationChannelConfig | None:
        """获取指定通知渠道"""
        return self.channel_manager.get_channel(channel_id)

    async def test_channel(self, channel_id: str) -> bool:
        """测试通知渠道"""
        channel = self.get_channel(channel_id)
        if not channel:
            return False

        test_message = NotificationMessage(
            title="🔔 通知渠道测试",
            content="这是一条测试消息，用于验证通知渠道配置是否正确。",
            notification_type=NotificationType.SYSTEM_STATUS,
            priority=NotificationPriority.LOW,
            source="notification_test",
        )

        results = await self.send_notification(test_message, [channel_id])
        return results.get(channel_id, False)
