# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a WHMCS Stock Monitor - an automated tool for monitoring WHMCS website inventory and automatically placing orders when products become available. The system consists of a Python FastAPI backend with async monitoring capabilities and a Vue.js frontend for management.

## Development Commands

### Backend (Python with uv)

- `uv sync` - Install Python dependencies
- `uv run uvicorn api_server:app --host 0.0.0.0 --port 8000` - Start API server
- `uv run playwright install chromium` - Install Playwright browser
- `uv run playwright install-deps` - Install Playwright system dependencies
- `uv run python stock_monitor.py` - Run stock monitor standalone
- `uv run python control.py` - Run Playwright debugging tool

### Frontend (Vue.js)

- `cd frontend && npm install` - Install frontend dependencies
- `cd frontend && npm run dev` - Start development server
- `cd frontend && npm run build` - Build for production
- `cd frontend && npm run lint` - Lint Vue.js code

### Docker Development

- `docker compose up -d` - Start all services
- `docker compose logs -f api` - View API logs
- `docker compose build --no-cache` - Rebuild images
- `docker compose ps` - Check service status

## Architecture Overview

### Core Components

1. **API Server** (`api_server.py`):

   - FastAPI application providing REST API
   - Authentication via cookie-based sessions
   - Static file serving for frontend
   - Background task management for monitoring

2. **Stock Monitor** (`stock_monitor.py`):

   - Async monitoring system using aiohttp
   - Multi-site and multi-product support
   - Intelligent retry mechanisms for failed orders
   - Notification system integration

3. **Core Logic** (`core.py`):

   - Playwright-based web automation
   - WHMCS login and order placement
   - Cookie management for session persistence
   - Site-specific authentication handling

4. **Data Models** (`models.py`):
   - Pydantic models for configuration validation
   - API request/response schemas
   - Type safety for all data structures

### Data Flow

1. **Configuration**: Sites and products are configured via Web UI or API
2. **Monitoring**: AsyncStockMonitor periodically checks product availability
3. **Detection**: When stock is available, automatic order placement is triggered
4. **Automation**: Playwright handles the complete order workflow
5. **Notifications**: Status updates are sent via configured notification channels

### Frontend Architecture

- **Vue 3** with Composition API
- **Pinia** for state management
- **Vue Router** for navigation
- **Tailwind CSS** for styling
- **Vite** for build tooling

Key Vue components:

- `Dashboard.vue` - Main monitoring status view
- `Sites.vue` - WHMCS site management
- `Products.vue` - Product configuration
- `Settings.vue` - Application settings

## Configuration Management

### Environment Variables

- `APP_PASSWORD` - Web interface authentication password
- `LOG_LEVEL` - Logging verbosity (DEBUG, INFO, WARNING, ERROR)
- `DATA_DIR` - Data storage directory path

### Data Storage

- `data/stock_config.json` - Main configuration file
- `data/cookies/` - Session cookies for each site
- `data/logs/` - Application and monitoring logs

### Configuration Structure

Sites contain products, each with:

- Monitoring intervals and thresholds
- Auto-order settings and retry limits
- Payment method preferences
- Custom notification URLs

## Key Features

### Intelligent Retry System

- Configurable retry limits (0-10 attempts)
- Progressive delays between retries
- Automatic hostname generation to avoid conflicts
- Failure counting and auto-disable protection

### Multi-Site Support

- Independent monitoring for multiple WHMCS sites
- Site-specific authentication and cookies
- Per-site configuration isolation

### Security Features

- Password-protected web interface
- Secure cookie handling
- Environment-based configuration
- Proper error handling and logging

## Development Notes

### Testing

No formal test suite exists. Manual testing is done through:

- Web interface functionality
- Direct API endpoint testing
- Docker container health checks

### Debugging

- Use `control.py` for Playwright debugging
- Enable DEBUG log level for detailed output
- Check `data/logs/` for monitoring history
- Use browser developer tools for frontend issues

### Adding New Features

When extending the system:

1. Update data models in `models.py` first
2. Implement backend logic in appropriate modules
3. Add API endpoints in `api_server.py`
4. Update frontend components and stores
5. Test with Docker compose setup

### Browser Automation

The system uses Playwright with Chromium for reliable web automation. Key considerations:

- Headless mode for production
- Cookie persistence between sessions
- Error handling for network issues
- Dynamic wait strategies for page loads

### 如何与用户交流

使用中文与用户交流
