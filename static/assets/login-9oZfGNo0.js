import{r,C as j,q as l,D as i,v as e,y as k,x as u,A as B,N as L,z,P as E,_ as V,$ as T,E as m,a0 as f,Z as I}from"./style-BZB2Cyno.js";const S={class:"flex items-center justify-center min-h-screen"},A={class:"w-full max-w-md"},D={class:"flex justify-end mb-4"},H=["title"],N={class:"text-xl"},P={class:"relative"},U={class:"relative"},q=["type","disabled"],K=["disabled"],O={key:0,class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},$={key:1,class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},F={key:0,class:"mt-2 text-sm text-red-500 dark:text-red-400 flex items-center"},R=["disabled"],W={key:0,class:"flex items-center"},Z={key:1,class:"flex items-center"},G={class:"mt-4 text-center"},J={class:"inline-flex items-center text-sm text-gray-600 dark:text-gray-400"},Q={__name:"LoginApp",setup(X){const n=r(""),o=r(!1),c=r(!1),v=r(!1),p=r(""),h=r(!1),d=r(!0),a=r(localStorage.getItem("theme")||"light"),b=async()=>{try{await fetch("/api/status",{method:"HEAD"}),d.value=!0}catch{d.value=!1}},M=()=>{c.value=!c.value},y=()=>{v.value=!1,p.value=""},x=s=>{v.value=!0,p.value=s,h.value=!0,setTimeout(()=>{h.value=!1},500)},w=async()=>{if(!(o.value||!n.value.trim())){y(),o.value=!0;try{const s=await fetch("/api/login",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:`password=${encodeURIComponent(n.value)}`}),t=await s.json();if(s.ok&&t.success)f("success","登录成功","正在跳转到管理面板..."),setTimeout(()=>{window.location.href="/"},1e3);else{const g=t.detail||t.message||"登录失败";x(g),f("error","登录失败",g),n.value=""}}catch(s){console.error("Login error:",s);const t="网络连接异常，请检查网络后重试";x(t),f("error","连接失败",t),d.value=!1}finally{o.value=!1}}},C=()=>{a.value=a.value==="light"?"dark":"light",localStorage.setItem("theme",a.value),_()},_=()=>{a.value==="dark"?(document.documentElement.classList.add("dark"),document.body.classList.add("dark")):(document.documentElement.classList.remove("dark"),document.body.classList.remove("dark"))};return j(()=>{_(),b(),setInterval(b,3e4),setTimeout(()=>{var s;(s=document.getElementById("password"))==null||s.focus()},300)}),(s,t)=>(i(),l("div",{class:u(["min-h-screen p-4 transition-colors duration-300",a.value==="dark"?"bg-animated-dark":"bg-animated-light"])},[e("div",S,[e("div",A,[e("div",D,[e("button",{onClick:C,class:"p-2 rounded-lg bg-white/10 dark:bg-gray-800/50 backdrop-blur-sm hover:bg-white/20 dark:hover:bg-gray-700/50 transition-all duration-200",title:a.value==="dark"?"切换到亮色主题":"切换到暗色主题"},[e("span",N,k(a.value==="dark"?"☀️":"🌙"),1)],8,H)]),e("div",{class:u(["bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-2xl p-8 space-y-8 animate-fadeInUp border border-gray-200/50 dark:border-gray-700/50",{"animate-shake":h.value}])},[t[8]||(t[8]=B('<div class="text-center"><div class="mx-auto h-16 w-16 bg-indigo-600 dark:bg-indigo-500 rounded-full flex items-center justify-center mb-4 shadow-lg"><svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg></div><h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">欢迎回来</h2><p class="text-gray-600 dark:text-gray-400">请输入密码访问监控面板</p></div>',1)),e("form",{onSubmit:L(w,["prevent"]),class:"space-y-6"},[e("div",P,[t[5]||(t[5]=e("label",{for:"password",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"密码",-1)),e("div",U,[E(e("input",{id:"password",type:c.value?"text":"password","onUpdate:modelValue":t[0]||(t[0]=g=>n.value=g),required:"",disabled:o.value,class:u(["input-focus w-full px-4 py-3 pl-12 pr-12 bg-gray-50 dark:bg-gray-900/50 border rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-colors",v.value?"border-red-500 focus:ring-red-500":"border-gray-300 dark:border-gray-600"]),placeholder:"请输入您的密码",onInput:y,onKeydown:T(w,["enter"])},null,42,q),[[V,n.value]]),t[3]||(t[3]=e("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[e("svg",{class:"h-5 w-5 text-gray-400 dark:text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),e("button",{type:"button",onClick:M,disabled:o.value,class:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 transition-colors"},[c.value?(i(),l("svg",O,t[1]||(t[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)]))):(i(),l("svg",$,t[2]||(t[2]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)])))],8,K)]),v.value?(i(),l("div",F,[t[4]||(t[4]=e("svg",{class:"h-4 w-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),m(" "+k(p.value),1)])):z("",!0)]),e("button",{type:"submit",disabled:o.value||!n.value.trim(),class:u(["btn-interactive w-full flex justify-center items-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 focus:ring-offset-white dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",o.value?"bg-indigo-600 dark:bg-indigo-500":"bg-indigo-600 dark:bg-indigo-500 hover:bg-indigo-700 dark:hover:bg-indigo-600"])},[o.value?(i(),l("div",W,t[6]||(t[6]=[e("div",{class:"spinner border-2 border-white border-t-transparent rounded-full mr-2"},null,-1),m(" 正在登录... ")]))):(i(),l("div",Z,t[7]||(t[7]=[e("svg",{class:"h-5 w-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"})],-1),m(" 登录面板 ")])))],10,R)],32),t[9]||(t[9]=e("div",{class:"text-center"},[e("p",{class:"text-xs text-gray-500 dark:text-gray-400"},"WHMCS库存监控系统 v1.0")],-1))],2),e("div",G,[e("div",J,[e("div",{class:u(["w-2 h-2 rounded-full mr-2",d.value?"bg-green-400 animate-pulse-custom":"bg-red-400"])},null,2),m(" "+k(d.value?"服务器连接正常":"服务器连接异常"),1)])])])])],2))}};I(Q).mount("#app");
