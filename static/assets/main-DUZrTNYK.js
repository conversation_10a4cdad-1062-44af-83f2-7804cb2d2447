import{e as $t,r as O,m as At,a as ie,i as Ne,b as Ht,t as ir,g as dr,o as ur,w as Le,n as dt,c as cr,d as A,f as ke,h as gr,s as mr,j as It,u as fe,k as fr,p as Ze,l as Ot,S as Ue,q as g,v as t,x as P,y as x,z as M,A as Y,F as ne,B as de,C as we,D as u,E as $,G as vr,H as De,I as q,J as K,T as qe,K as Be,L as We,M as Ft,N as ve,O as ye,P as F,Q as X,R as re,U as pe,V as Ve,W as pr,X as br,Y as xr,Z as hr}from"./style-CglD5PP2.js";/*!
 * pinia v2.3.1
 * (c) 2025 <PERSON>
 * @license MIT
 */let Ut;const Ge=o=>Ut=o,Nt=Symbol();function st(o){return o&&typeof o=="object"&&Object.prototype.toString.call(o)==="[object Object]"&&typeof o.toJSON!="function"}var Te;(function(o){o.direct="direct",o.patchObject="patch object",o.patchFunction="patch function"})(Te||(Te={}));function yr(){const o=$t(!0),e=o.run(()=>O({}));let a=[],r=[];const l=At({install(d){Ge(l),l._a=d,d.provide(Nt,l),d.config.globalProperties.$pinia=l,r.forEach(n=>a.push(n)),r=[]},use(d){return this._a?a.push(d):r.push(d),this},_p:a,_a:null,_e:o,_s:new Map,state:e});return l}const Dt=()=>{};function pt(o,e,a,r=Dt){o.push(e);const l=()=>{const d=o.indexOf(e);d>-1&&(o.splice(d,1),r())};return!a&&dr()&&ur(l),l}function Se(o,...e){o.slice().forEach(a=>{a(...e)})}const kr=o=>o(),bt=Symbol(),et=Symbol();function at(o,e){o instanceof Map&&e instanceof Map?e.forEach((a,r)=>o.set(r,a)):o instanceof Set&&e instanceof Set&&e.forEach(o.add,o);for(const a in e){if(!e.hasOwnProperty(a))continue;const r=e[a],l=o[a];st(l)&&st(r)&&o.hasOwnProperty(a)&&!Ne(r)&&!Ht(r)?o[a]=at(l,r):o[a]=r}return o}const wr=Symbol();function _r(o){return!st(o)||!o.hasOwnProperty(wr)}const{assign:xe}=Object;function Cr(o){return!!(Ne(o)&&o.effect)}function Mr(o,e,a,r){const{state:l,actions:d,getters:n}=e,i=a.state.value[o];let p;function s(){i||(a.state.value[o]=l?l():{});const m=cr(a.state.value[o]);return xe(m,d,Object.keys(n||{}).reduce((c,b)=>(c[b]=At(A(()=>{Ge(a);const h=a._s.get(o);return n[b].call(h,h)})),c),{}))}return p=qt(o,s,e,a,r,!0),p}function qt(o,e,a={},r,l,d){let n;const i=xe({actions:{}},a),p={deep:!0};let s,m,c=[],b=[],h;const B=r.state.value[o];!d&&!B&&(r.state.value[o]={}),O({});let C;function v(z){let S;s=m=!1,typeof z=="function"?(z(r.state.value[o]),S={type:Te.patchFunction,storeId:o,events:h}):(at(r.state.value[o],z),S={type:Te.patchObject,payload:z,storeId:o,events:h});const f=C=Symbol();dt().then(()=>{C===f&&(s=!0)}),m=!0,Se(c,S,r.state.value[o])}const k=d?function(){const{state:S}=a,f=S?S():{};this.$patch(_=>{xe(_,f)})}:Dt;function w(){n.stop(),c=[],b=[],r._s.delete(o)}const E=(z,S="")=>{if(bt in z)return z[et]=S,z;const f=function(){Ge(r);const _=Array.from(arguments),W=[],ae=[];function I(te){W.push(te)}function Q(te){ae.push(te)}Se(b,{args:_,name:f[et],store:Z,after:I,onError:Q});let ee;try{ee=z.apply(this&&this.$id===o?this:Z,_)}catch(te){throw Se(ae,te),te}return ee instanceof Promise?ee.then(te=>(Se(W,te),te)).catch(te=>(Se(ae,te),Promise.reject(te))):(Se(W,ee),ee)};return f[bt]=!0,f[et]=S,f},N={_p:r,$id:o,$onAction:pt.bind(null,b),$patch:v,$reset:k,$subscribe(z,S={}){const f=pt(c,z,S.detached,()=>_()),_=n.run(()=>Le(()=>r.state.value[o],W=>{(S.flush==="sync"?m:s)&&z({storeId:o,type:Te.direct,events:h},W)},xe({},p,S)));return f},$dispose:w},Z=ie(N);r._s.set(o,Z);const V=(r._a&&r._a.runWithContext||kr)(()=>r._e.run(()=>(n=$t()).run(()=>e({action:E}))));for(const z in V){const S=V[z];if(Ne(S)&&!Cr(S)||Ht(S))d||(B&&_r(S)&&(Ne(S)?S.value=B[z]:at(S,B[z])),r.state.value[o][z]=S);else if(typeof S=="function"){const f=E(S,z);V[z]=f,i.actions[z]=S}}return xe(Z,V),xe(ir(Z),V),Object.defineProperty(Z,"$state",{get:()=>r.state.value[o],set:z=>{v(S=>{xe(S,z)})}}),r._p.forEach(z=>{xe(Z,n.run(()=>z({store:Z,app:r._a,pinia:r,options:i})))}),B&&d&&a.hydrate&&a.hydrate(Z.$state,B),s=!0,m=!0,Z}/*! #__NO_SIDE_EFFECTS__ */function Sr(o,e,a){let r,l;const d=typeof e=="function";r=o,l=d?a:e;function n(i,p){const s=gr();return i=i||(s?ke(Nt,null):null),i&&Ge(i),i=Ut,i._s.has(r)||(d?qt(r,e,l,i):Mr(r,l,i)),i._s.get(r)}return n.$id=r,n}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const je=typeof document<"u";function Wt(o){return typeof o=="object"||"displayName"in o||"props"in o||"__vccOpts"in o}function jr(o){return o.__esModule||o[Symbol.toStringTag]==="Module"||o.default&&Wt(o.default)}const G=Object.assign;function tt(o,e){const a={};for(const r in e){const l=e[r];a[r]=ge(l)?l.map(o):o(l)}return a}const $e=()=>{},ge=Array.isArray,Gt=/#/g,Lr=/&/g,Vr=/\//g,Br=/=/g,Er=/\?/g,Kt=/\+/g,Pr=/%5B/g,Rr=/%5D/g,Qt=/%5E/g,zr=/%60/g,Jt=/%7B/g,Tr=/%7C/g,Yt=/%7D/g,$r=/%20/g;function ut(o){return encodeURI(""+o).replace(Tr,"|").replace(Pr,"[").replace(Rr,"]")}function Ar(o){return ut(o).replace(Jt,"{").replace(Yt,"}").replace(Qt,"^")}function nt(o){return ut(o).replace(Kt,"%2B").replace($r,"+").replace(Gt,"%23").replace(Lr,"%26").replace(zr,"`").replace(Jt,"{").replace(Yt,"}").replace(Qt,"^")}function Hr(o){return nt(o).replace(Br,"%3D")}function Ir(o){return ut(o).replace(Gt,"%23").replace(Er,"%3F")}function Or(o){return o==null?"":Ir(o).replace(Vr,"%2F")}function He(o){try{return decodeURIComponent(""+o)}catch{}return""+o}const Fr=/\/$/,Ur=o=>o.replace(Fr,"");function rt(o,e,a="/"){let r,l={},d="",n="";const i=e.indexOf("#");let p=e.indexOf("?");return i<p&&i>=0&&(p=-1),p>-1&&(r=e.slice(0,p),d=e.slice(p+1,i>-1?i:e.length),l=o(d)),i>-1&&(r=r||e.slice(0,i),n=e.slice(i,e.length)),r=Wr(r??e,a),{fullPath:r+(d&&"?")+d+n,path:r,query:l,hash:He(n)}}function Nr(o,e){const a=e.query?o(e.query):"";return e.path+(a&&"?")+a+(e.hash||"")}function xt(o,e){return!e||!o.toLowerCase().startsWith(e.toLowerCase())?o:o.slice(e.length)||"/"}function Dr(o,e,a){const r=e.matched.length-1,l=a.matched.length-1;return r>-1&&r===l&&Ee(e.matched[r],a.matched[l])&&Xt(e.params,a.params)&&o(e.query)===o(a.query)&&e.hash===a.hash}function Ee(o,e){return(o.aliasOf||o)===(e.aliasOf||e)}function Xt(o,e){if(Object.keys(o).length!==Object.keys(e).length)return!1;for(const a in o)if(!qr(o[a],e[a]))return!1;return!0}function qr(o,e){return ge(o)?ht(o,e):ge(e)?ht(e,o):o===e}function ht(o,e){return ge(e)?o.length===e.length&&o.every((a,r)=>a===e[r]):o.length===1&&o[0]===e}function Wr(o,e){if(o.startsWith("/"))return o;if(!o)return e;const a=e.split("/"),r=o.split("/"),l=r[r.length-1];(l===".."||l===".")&&r.push("");let d=a.length-1,n,i;for(n=0;n<r.length;n++)if(i=r[n],i!==".")if(i==="..")d>1&&d--;else break;return a.slice(0,d).join("/")+"/"+r.slice(n).join("/")}const be={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ie;(function(o){o.pop="pop",o.push="push"})(Ie||(Ie={}));var Ae;(function(o){o.back="back",o.forward="forward",o.unknown=""})(Ae||(Ae={}));function Gr(o){if(!o)if(je){const e=document.querySelector("base");o=e&&e.getAttribute("href")||"/",o=o.replace(/^\w+:\/\/[^\/]+/,"")}else o="/";return o[0]!=="/"&&o[0]!=="#"&&(o="/"+o),Ur(o)}const Kr=/^[^#]+#/;function Qr(o,e){return o.replace(Kr,"#")+e}function Jr(o,e){const a=document.documentElement.getBoundingClientRect(),r=o.getBoundingClientRect();return{behavior:e.behavior,left:r.left-a.left-(e.left||0),top:r.top-a.top-(e.top||0)}}const Ke=()=>({left:window.scrollX,top:window.scrollY});function Yr(o){let e;if("el"in o){const a=o.el,r=typeof a=="string"&&a.startsWith("#"),l=typeof a=="string"?r?document.getElementById(a.slice(1)):document.querySelector(a):a;if(!l)return;e=Jr(l,o)}else e=o;"scrollBehavior"in document.documentElement.style?window.scrollTo(e):window.scrollTo(e.left!=null?e.left:window.scrollX,e.top!=null?e.top:window.scrollY)}function yt(o,e){return(history.state?history.state.position-e:-1)+o}const lt=new Map;function Xr(o,e){lt.set(o,e)}function Zr(o){const e=lt.get(o);return lt.delete(o),e}let eo=()=>location.protocol+"//"+location.host;function Zt(o,e){const{pathname:a,search:r,hash:l}=e,d=o.indexOf("#");if(d>-1){let i=l.includes(o.slice(d))?o.slice(d).length:1,p=l.slice(i);return p[0]!=="/"&&(p="/"+p),xt(p,"")}return xt(a,o)+r+l}function to(o,e,a,r){let l=[],d=[],n=null;const i=({state:b})=>{const h=Zt(o,location),B=a.value,C=e.value;let v=0;if(b){if(a.value=h,e.value=b,n&&n===B){n=null;return}v=C?b.position-C.position:0}else r(h);l.forEach(k=>{k(a.value,B,{delta:v,type:Ie.pop,direction:v?v>0?Ae.forward:Ae.back:Ae.unknown})})};function p(){n=a.value}function s(b){l.push(b);const h=()=>{const B=l.indexOf(b);B>-1&&l.splice(B,1)};return d.push(h),h}function m(){const{history:b}=window;b.state&&b.replaceState(G({},b.state,{scroll:Ke()}),"")}function c(){for(const b of d)b();d=[],window.removeEventListener("popstate",i),window.removeEventListener("beforeunload",m)}return window.addEventListener("popstate",i),window.addEventListener("beforeunload",m,{passive:!0}),{pauseListeners:p,listen:s,destroy:c}}function kt(o,e,a,r=!1,l=!1){return{back:o,current:e,forward:a,replaced:r,position:window.history.length,scroll:l?Ke():null}}function ro(o){const{history:e,location:a}=window,r={value:Zt(o,a)},l={value:e.state};l.value||d(r.value,{back:null,current:r.value,forward:null,position:e.length-1,replaced:!0,scroll:null},!0);function d(p,s,m){const c=o.indexOf("#"),b=c>-1?(a.host&&document.querySelector("base")?o:o.slice(c))+p:eo()+o+p;try{e[m?"replaceState":"pushState"](s,"",b),l.value=s}catch(h){console.error(h),a[m?"replace":"assign"](b)}}function n(p,s){const m=G({},e.state,kt(l.value.back,p,l.value.forward,!0),s,{position:l.value.position});d(p,m,!0),r.value=p}function i(p,s){const m=G({},l.value,e.state,{forward:p,scroll:Ke()});d(m.current,m,!0);const c=G({},kt(r.value,p,null),{position:m.position+1},s);d(p,c,!1),r.value=p}return{location:r,state:l,push:i,replace:n}}function oo(o){o=Gr(o);const e=ro(o),a=to(o,e.state,e.location,e.replace);function r(d,n=!0){n||a.pauseListeners(),history.go(d)}const l=G({location:"",base:o,go:r,createHref:Qr.bind(null,o)},e,a);return Object.defineProperty(l,"location",{enumerable:!0,get:()=>e.location.value}),Object.defineProperty(l,"state",{enumerable:!0,get:()=>e.state.value}),l}function so(o){return typeof o=="string"||o&&typeof o=="object"}function er(o){return typeof o=="string"||typeof o=="symbol"}const tr=Symbol("");var wt;(function(o){o[o.aborted=4]="aborted",o[o.cancelled=8]="cancelled",o[o.duplicated=16]="duplicated"})(wt||(wt={}));function Pe(o,e){return G(new Error,{type:o,[tr]:!0},e)}function me(o,e){return o instanceof Error&&tr in o&&(e==null||!!(o.type&e))}const _t="[^/]+?",ao={sensitive:!1,strict:!1,start:!0,end:!0},no=/[.+*?^${}()[\]/\\]/g;function lo(o,e){const a=G({},ao,e),r=[];let l=a.start?"^":"";const d=[];for(const s of o){const m=s.length?[]:[90];a.strict&&!s.length&&(l+="/");for(let c=0;c<s.length;c++){const b=s[c];let h=40+(a.sensitive?.25:0);if(b.type===0)c||(l+="/"),l+=b.value.replace(no,"\\$&"),h+=40;else if(b.type===1){const{value:B,repeatable:C,optional:v,regexp:k}=b;d.push({name:B,repeatable:C,optional:v});const w=k||_t;if(w!==_t){h+=10;try{new RegExp(`(${w})`)}catch(N){throw new Error(`Invalid custom RegExp for param "${B}" (${w}): `+N.message)}}let E=C?`((?:${w})(?:/(?:${w}))*)`:`(${w})`;c||(E=v&&s.length<2?`(?:/${E})`:"/"+E),v&&(E+="?"),l+=E,h+=20,v&&(h+=-8),C&&(h+=-20),w===".*"&&(h+=-50)}m.push(h)}r.push(m)}if(a.strict&&a.end){const s=r.length-1;r[s][r[s].length-1]+=.7000000000000001}a.strict||(l+="/?"),a.end?l+="$":a.strict&&!l.endsWith("/")&&(l+="(?:/|$)");const n=new RegExp(l,a.sensitive?"":"i");function i(s){const m=s.match(n),c={};if(!m)return null;for(let b=1;b<m.length;b++){const h=m[b]||"",B=d[b-1];c[B.name]=h&&B.repeatable?h.split("/"):h}return c}function p(s){let m="",c=!1;for(const b of o){(!c||!m.endsWith("/"))&&(m+="/"),c=!1;for(const h of b)if(h.type===0)m+=h.value;else if(h.type===1){const{value:B,repeatable:C,optional:v}=h,k=B in s?s[B]:"";if(ge(k)&&!C)throw new Error(`Provided param "${B}" is an array but it is not repeatable (* or + modifiers)`);const w=ge(k)?k.join("/"):k;if(!w)if(v)b.length<2&&(m.endsWith("/")?m=m.slice(0,-1):c=!0);else throw new Error(`Missing required param "${B}"`);m+=w}}return m||"/"}return{re:n,score:r,keys:d,parse:i,stringify:p}}function io(o,e){let a=0;for(;a<o.length&&a<e.length;){const r=e[a]-o[a];if(r)return r;a++}return o.length<e.length?o.length===1&&o[0]===80?-1:1:o.length>e.length?e.length===1&&e[0]===80?1:-1:0}function rr(o,e){let a=0;const r=o.score,l=e.score;for(;a<r.length&&a<l.length;){const d=io(r[a],l[a]);if(d)return d;a++}if(Math.abs(l.length-r.length)===1){if(Ct(r))return 1;if(Ct(l))return-1}return l.length-r.length}function Ct(o){const e=o[o.length-1];return o.length>0&&e[e.length-1]<0}const uo={type:0,value:""},co=/[a-zA-Z0-9_]/;function go(o){if(!o)return[[]];if(o==="/")return[[uo]];if(!o.startsWith("/"))throw new Error(`Invalid path "${o}"`);function e(h){throw new Error(`ERR (${a})/"${s}": ${h}`)}let a=0,r=a;const l=[];let d;function n(){d&&l.push(d),d=[]}let i=0,p,s="",m="";function c(){s&&(a===0?d.push({type:0,value:s}):a===1||a===2||a===3?(d.length>1&&(p==="*"||p==="+")&&e(`A repeatable param (${s}) must be alone in its segment. eg: '/:ids+.`),d.push({type:1,value:s,regexp:m,repeatable:p==="*"||p==="+",optional:p==="*"||p==="?"})):e("Invalid state to consume buffer"),s="")}function b(){s+=p}for(;i<o.length;){if(p=o[i++],p==="\\"&&a!==2){r=a,a=4;continue}switch(a){case 0:p==="/"?(s&&c(),n()):p===":"?(c(),a=1):b();break;case 4:b(),a=r;break;case 1:p==="("?a=2:co.test(p)?b():(c(),a=0,p!=="*"&&p!=="?"&&p!=="+"&&i--);break;case 2:p===")"?m[m.length-1]=="\\"?m=m.slice(0,-1)+p:a=3:m+=p;break;case 3:c(),a=0,p!=="*"&&p!=="?"&&p!=="+"&&i--,m="";break;default:e("Unknown state");break}}return a===2&&e(`Unfinished custom RegExp for param "${s}"`),c(),n(),l}function mo(o,e,a){const r=lo(go(o.path),a),l=G(r,{record:o,parent:e,children:[],alias:[]});return e&&!l.record.aliasOf==!e.record.aliasOf&&e.children.push(l),l}function fo(o,e){const a=[],r=new Map;e=Lt({strict:!1,end:!0,sensitive:!1},e);function l(c){return r.get(c)}function d(c,b,h){const B=!h,C=St(c);C.aliasOf=h&&h.record;const v=Lt(e,c),k=[C];if("alias"in c){const N=typeof c.alias=="string"?[c.alias]:c.alias;for(const Z of N)k.push(St(G({},C,{components:h?h.record.components:C.components,path:Z,aliasOf:h?h.record:C})))}let w,E;for(const N of k){const{path:Z}=N;if(b&&Z[0]!=="/"){const H=b.record.path,V=H[H.length-1]==="/"?"":"/";N.path=b.record.path+(Z&&V+Z)}if(w=mo(N,b,v),h?h.alias.push(w):(E=E||w,E!==w&&E.alias.push(w),B&&c.name&&!jt(w)&&n(c.name)),or(w)&&p(w),C.children){const H=C.children;for(let V=0;V<H.length;V++)d(H[V],w,h&&h.children[V])}h=h||w}return E?()=>{n(E)}:$e}function n(c){if(er(c)){const b=r.get(c);b&&(r.delete(c),a.splice(a.indexOf(b),1),b.children.forEach(n),b.alias.forEach(n))}else{const b=a.indexOf(c);b>-1&&(a.splice(b,1),c.record.name&&r.delete(c.record.name),c.children.forEach(n),c.alias.forEach(n))}}function i(){return a}function p(c){const b=bo(c,a);a.splice(b,0,c),c.record.name&&!jt(c)&&r.set(c.record.name,c)}function s(c,b){let h,B={},C,v;if("name"in c&&c.name){if(h=r.get(c.name),!h)throw Pe(1,{location:c});v=h.record.name,B=G(Mt(b.params,h.keys.filter(E=>!E.optional).concat(h.parent?h.parent.keys.filter(E=>E.optional):[]).map(E=>E.name)),c.params&&Mt(c.params,h.keys.map(E=>E.name))),C=h.stringify(B)}else if(c.path!=null)C=c.path,h=a.find(E=>E.re.test(C)),h&&(B=h.parse(C),v=h.record.name);else{if(h=b.name?r.get(b.name):a.find(E=>E.re.test(b.path)),!h)throw Pe(1,{location:c,currentLocation:b});v=h.record.name,B=G({},b.params,c.params),C=h.stringify(B)}const k=[];let w=h;for(;w;)k.unshift(w.record),w=w.parent;return{name:v,path:C,params:B,matched:k,meta:po(k)}}o.forEach(c=>d(c));function m(){a.length=0,r.clear()}return{addRoute:d,resolve:s,removeRoute:n,clearRoutes:m,getRoutes:i,getRecordMatcher:l}}function Mt(o,e){const a={};for(const r of e)r in o&&(a[r]=o[r]);return a}function St(o){const e={path:o.path,redirect:o.redirect,name:o.name,meta:o.meta||{},aliasOf:o.aliasOf,beforeEnter:o.beforeEnter,props:vo(o),children:o.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in o?o.components||null:o.component&&{default:o.component}};return Object.defineProperty(e,"mods",{value:{}}),e}function vo(o){const e={},a=o.props||!1;if("component"in o)e.default=a;else for(const r in o.components)e[r]=typeof a=="object"?a[r]:a;return e}function jt(o){for(;o;){if(o.record.aliasOf)return!0;o=o.parent}return!1}function po(o){return o.reduce((e,a)=>G(e,a.meta),{})}function Lt(o,e){const a={};for(const r in o)a[r]=r in e?e[r]:o[r];return a}function bo(o,e){let a=0,r=e.length;for(;a!==r;){const d=a+r>>1;rr(o,e[d])<0?r=d:a=d+1}const l=xo(o);return l&&(r=e.lastIndexOf(l,r-1)),r}function xo(o){let e=o;for(;e=e.parent;)if(or(e)&&rr(o,e)===0)return e}function or({record:o}){return!!(o.name||o.components&&Object.keys(o.components).length||o.redirect)}function ho(o){const e={};if(o===""||o==="?")return e;const r=(o[0]==="?"?o.slice(1):o).split("&");for(let l=0;l<r.length;++l){const d=r[l].replace(Kt," "),n=d.indexOf("="),i=He(n<0?d:d.slice(0,n)),p=n<0?null:He(d.slice(n+1));if(i in e){let s=e[i];ge(s)||(s=e[i]=[s]),s.push(p)}else e[i]=p}return e}function Vt(o){let e="";for(let a in o){const r=o[a];if(a=Hr(a),r==null){r!==void 0&&(e+=(e.length?"&":"")+a);continue}(ge(r)?r.map(d=>d&&nt(d)):[r&&nt(r)]).forEach(d=>{d!==void 0&&(e+=(e.length?"&":"")+a,d!=null&&(e+="="+d))})}return e}function yo(o){const e={};for(const a in o){const r=o[a];r!==void 0&&(e[a]=ge(r)?r.map(l=>l==null?null:""+l):r==null?r:""+r)}return e}const ko=Symbol(""),Bt=Symbol(""),Qe=Symbol(""),ct=Symbol(""),it=Symbol("");function ze(){let o=[];function e(r){return o.push(r),()=>{const l=o.indexOf(r);l>-1&&o.splice(l,1)}}function a(){o=[]}return{add:e,list:()=>o.slice(),reset:a}}function he(o,e,a,r,l,d=n=>n()){const n=r&&(r.enterCallbacks[l]=r.enterCallbacks[l]||[]);return()=>new Promise((i,p)=>{const s=b=>{b===!1?p(Pe(4,{from:a,to:e})):b instanceof Error?p(b):so(b)?p(Pe(2,{from:e,to:b})):(n&&r.enterCallbacks[l]===n&&typeof b=="function"&&n.push(b),i())},m=d(()=>o.call(r&&r.instances[l],e,a,s));let c=Promise.resolve(m);o.length<3&&(c=c.then(s)),c.catch(b=>p(b))})}function ot(o,e,a,r,l=d=>d()){const d=[];for(const n of o)for(const i in n.components){let p=n.components[i];if(!(e!=="beforeRouteEnter"&&!n.instances[i]))if(Wt(p)){const m=(p.__vccOpts||p)[e];m&&d.push(he(m,a,r,n,i,l))}else{let s=p();d.push(()=>s.then(m=>{if(!m)throw new Error(`Couldn't resolve component "${i}" at "${n.path}"`);const c=jr(m)?m.default:m;n.mods[i]=m,n.components[i]=c;const h=(c.__vccOpts||c)[e];return h&&he(h,a,r,n,i,l)()}))}}return d}function Et(o){const e=ke(Qe),a=ke(ct),r=A(()=>{const p=fe(o.to);return e.resolve(p)}),l=A(()=>{const{matched:p}=r.value,{length:s}=p,m=p[s-1],c=a.matched;if(!m||!c.length)return-1;const b=c.findIndex(Ee.bind(null,m));if(b>-1)return b;const h=Pt(p[s-2]);return s>1&&Pt(m)===h&&c[c.length-1].path!==h?c.findIndex(Ee.bind(null,p[s-2])):b}),d=A(()=>l.value>-1&&So(a.params,r.value.params)),n=A(()=>l.value>-1&&l.value===a.matched.length-1&&Xt(a.params,r.value.params));function i(p={}){if(Mo(p)){const s=e[fe(o.replace)?"replace":"push"](fe(o.to)).catch($e);return o.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>s),s}return Promise.resolve()}return{route:r,href:A(()=>r.value.href),isActive:d,isExactActive:n,navigate:i}}function wo(o){return o.length===1?o[0]:o}const _o=It({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Et,setup(o,{slots:e}){const a=ie(Et(o)),{options:r}=ke(Qe),l=A(()=>({[Rt(o.activeClass,r.linkActiveClass,"router-link-active")]:a.isActive,[Rt(o.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:a.isExactActive}));return()=>{const d=e.default&&wo(e.default(a));return o.custom?d:Ot("a",{"aria-current":a.isExactActive?o.ariaCurrentValue:null,href:a.href,onClick:a.navigate,class:l.value},d)}}}),Co=_o;function Mo(o){if(!(o.metaKey||o.altKey||o.ctrlKey||o.shiftKey)&&!o.defaultPrevented&&!(o.button!==void 0&&o.button!==0)){if(o.currentTarget&&o.currentTarget.getAttribute){const e=o.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(e))return}return o.preventDefault&&o.preventDefault(),!0}}function So(o,e){for(const a in e){const r=e[a],l=o[a];if(typeof r=="string"){if(r!==l)return!1}else if(!ge(l)||l.length!==r.length||r.some((d,n)=>d!==l[n]))return!1}return!0}function Pt(o){return o?o.aliasOf?o.aliasOf.path:o.path:""}const Rt=(o,e,a)=>o??e??a,jo=It({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(o,{attrs:e,slots:a}){const r=ke(it),l=A(()=>o.route||r.value),d=ke(Bt,0),n=A(()=>{let s=fe(d);const{matched:m}=l.value;let c;for(;(c=m[s])&&!c.components;)s++;return s}),i=A(()=>l.value.matched[n.value]);Ze(Bt,A(()=>n.value+1)),Ze(ko,i),Ze(it,l);const p=O();return Le(()=>[p.value,i.value,o.name],([s,m,c],[b,h,B])=>{m&&(m.instances[c]=s,h&&h!==m&&s&&s===b&&(m.leaveGuards.size||(m.leaveGuards=h.leaveGuards),m.updateGuards.size||(m.updateGuards=h.updateGuards))),s&&m&&(!h||!Ee(m,h)||!b)&&(m.enterCallbacks[c]||[]).forEach(C=>C(s))},{flush:"post"}),()=>{const s=l.value,m=o.name,c=i.value,b=c&&c.components[m];if(!b)return zt(a.default,{Component:b,route:s});const h=c.props[m],B=h?h===!0?s.params:typeof h=="function"?h(s):h:null,v=Ot(b,G({},B,e,{onVnodeUnmounted:k=>{k.component.isUnmounted&&(c.instances[m]=null)},ref:p}));return zt(a.default,{Component:v,route:s})||v}}});function zt(o,e){if(!o)return null;const a=o(e);return a.length===1?a[0]:a}const Lo=jo;function Vo(o){const e=fo(o.routes,o),a=o.parseQuery||ho,r=o.stringifyQuery||Vt,l=o.history,d=ze(),n=ze(),i=ze(),p=mr(be);let s=be;je&&o.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const m=tt.bind(null,y=>""+y),c=tt.bind(null,Or),b=tt.bind(null,He);function h(y,L){let j,T;return er(y)?(j=e.getRecordMatcher(y),T=L):T=y,e.addRoute(T,j)}function B(y){const L=e.getRecordMatcher(y);L&&e.removeRoute(L)}function C(){return e.getRoutes().map(y=>y.record)}function v(y){return!!e.getRecordMatcher(y)}function k(y,L){if(L=G({},L||p.value),typeof y=="string"){const U=rt(a,y,L.path),se=e.resolve({path:U.path},L),Re=l.createHref(U.fullPath);return G(U,se,{params:b(se.params),hash:He(U.hash),redirectedFrom:void 0,href:Re})}let j;if(y.path!=null)j=G({},y,{path:rt(a,y.path,L.path).path});else{const U=G({},y.params);for(const se in U)U[se]==null&&delete U[se];j=G({},y,{params:c(U)}),L.params=c(L.params)}const T=e.resolve(j,L),J=y.hash||"";T.params=m(b(T.params));const oe=Nr(r,G({},y,{hash:Ar(J),path:T.path})),D=l.createHref(oe);return G({fullPath:oe,hash:J,query:r===Vt?yo(y.query):y.query||{}},T,{redirectedFrom:void 0,href:D})}function w(y){return typeof y=="string"?rt(a,y,p.value.path):G({},y)}function E(y,L){if(s!==y)return Pe(8,{from:L,to:y})}function N(y){return V(y)}function Z(y){return N(G(w(y),{replace:!0}))}function H(y){const L=y.matched[y.matched.length-1];if(L&&L.redirect){const{redirect:j}=L;let T=typeof j=="function"?j(y):j;return typeof T=="string"&&(T=T.includes("?")||T.includes("#")?T=w(T):{path:T},T.params={}),G({query:y.query,hash:y.hash,params:T.path!=null?{}:y.params},T)}}function V(y,L){const j=s=k(y),T=p.value,J=y.state,oe=y.force,D=y.replace===!0,U=H(j);if(U)return V(G(w(U),{state:typeof U=="object"?G({},J,U.state):J,force:oe,replace:D}),L||j);const se=j;se.redirectedFrom=L;let Re;return!oe&&Dr(r,T,j)&&(Re=Pe(16,{to:se,from:T}),ft(T,T,!0,!1)),(Re?Promise.resolve(Re):f(se,T)).catch(le=>me(le)?me(le,2)?le:Je(le):Ce(le,se,T)).then(le=>{if(le){if(me(le,2))return V(G({replace:D},w(le.to),{state:typeof le.to=="object"?G({},J,le.to.state):J,force:oe}),L||se)}else le=W(se,T,!0,D,J);return _(se,T,le),le})}function z(y,L){const j=E(y,L);return j?Promise.reject(j):Promise.resolve()}function S(y){const L=Fe.values().next().value;return L&&typeof L.runWithContext=="function"?L.runWithContext(y):y()}function f(y,L){let j;const[T,J,oe]=Bo(y,L);j=ot(T.reverse(),"beforeRouteLeave",y,L);for(const U of T)U.leaveGuards.forEach(se=>{j.push(he(se,y,L))});const D=z.bind(null,y,L);return j.push(D),Me(j).then(()=>{j=[];for(const U of d.list())j.push(he(U,y,L));return j.push(D),Me(j)}).then(()=>{j=ot(J,"beforeRouteUpdate",y,L);for(const U of J)U.updateGuards.forEach(se=>{j.push(he(se,y,L))});return j.push(D),Me(j)}).then(()=>{j=[];for(const U of oe)if(U.beforeEnter)if(ge(U.beforeEnter))for(const se of U.beforeEnter)j.push(he(se,y,L));else j.push(he(U.beforeEnter,y,L));return j.push(D),Me(j)}).then(()=>(y.matched.forEach(U=>U.enterCallbacks={}),j=ot(oe,"beforeRouteEnter",y,L,S),j.push(D),Me(j))).then(()=>{j=[];for(const U of n.list())j.push(he(U,y,L));return j.push(D),Me(j)}).catch(U=>me(U,8)?U:Promise.reject(U))}function _(y,L,j){i.list().forEach(T=>S(()=>T(y,L,j)))}function W(y,L,j,T,J){const oe=E(y,L);if(oe)return oe;const D=L===be,U=je?history.state:{};j&&(T||D?l.replace(y.fullPath,G({scroll:D&&U&&U.scroll},J)):l.push(y.fullPath,J)),p.value=y,ft(y,L,j,D),Je()}let ae;function I(){ae||(ae=l.listen((y,L,j)=>{if(!vt.listening)return;const T=k(y),J=H(T);if(J){V(G(J,{replace:!0,force:!0}),T).catch($e);return}s=T;const oe=p.value;je&&Xr(yt(oe.fullPath,j.delta),Ke()),f(T,oe).catch(D=>me(D,12)?D:me(D,2)?(V(G(w(D.to),{force:!0}),T).then(U=>{me(U,20)&&!j.delta&&j.type===Ie.pop&&l.go(-1,!1)}).catch($e),Promise.reject()):(j.delta&&l.go(-j.delta,!1),Ce(D,T,oe))).then(D=>{D=D||W(T,oe,!1),D&&(j.delta&&!me(D,8)?l.go(-j.delta,!1):j.type===Ie.pop&&me(D,20)&&l.go(-1,!1)),_(T,oe,D)}).catch($e)}))}let Q=ze(),ee=ze(),te;function Ce(y,L,j){Je(y);const T=ee.list();return T.length?T.forEach(J=>J(y,L,j)):console.error(y),Promise.reject(y)}function Oe(){return te&&p.value!==be?Promise.resolve():new Promise((y,L)=>{Q.add([y,L])})}function Je(y){return te||(te=!y,I(),Q.list().forEach(([L,j])=>y?j(y):L()),Q.reset()),y}function ft(y,L,j,T){const{scrollBehavior:J}=o;if(!je||!J)return Promise.resolve();const oe=!j&&Zr(yt(y.fullPath,0))||(T||!j)&&history.state&&history.state.scroll||null;return dt().then(()=>J(y,L,oe)).then(D=>D&&Yr(D)).catch(D=>Ce(D,y,L))}const Ye=y=>l.go(y);let Xe;const Fe=new Set,vt={currentRoute:p,listening:!0,addRoute:h,removeRoute:B,clearRoutes:e.clearRoutes,hasRoute:v,getRoutes:C,resolve:k,options:o,push:N,replace:Z,go:Ye,back:()=>Ye(-1),forward:()=>Ye(1),beforeEach:d.add,beforeResolve:n.add,afterEach:i.add,onError:ee.add,isReady:Oe,install(y){const L=this;y.component("RouterLink",Co),y.component("RouterView",Lo),y.config.globalProperties.$router=L,Object.defineProperty(y.config.globalProperties,"$route",{enumerable:!0,get:()=>fe(p)}),je&&!Xe&&p.value===be&&(Xe=!0,N(l.location).catch(J=>{}));const j={};for(const J in be)Object.defineProperty(j,J,{get:()=>p.value[J],enumerable:!0});y.provide(Qe,L),y.provide(ct,fr(j)),y.provide(it,p);const T=y.unmount;Fe.add(y),y.unmount=function(){Fe.delete(y),Fe.size<1&&(s=be,ae&&ae(),ae=null,p.value=be,Xe=!1,te=!1),T()}}};function Me(y){return y.reduce((L,j)=>L.then(()=>S(j)),Promise.resolve())}return vt}function Bo(o,e){const a=[],r=[],l=[],d=Math.max(e.matched.length,o.matched.length);for(let n=0;n<d;n++){const i=e.matched[n];i&&(o.matched.find(s=>Ee(s,i))?r.push(i):a.push(i));const p=o.matched[n];p&&(e.matched.find(s=>Ee(s,p))||l.push(p))}return[a,r,l]}function Eo(){return ke(Qe)}function sr(o){return ke(ct)}const Po=(o,e=!1)=>{if(!o)return"N/A";let a;try{if(typeof o=="string")if(o.includes(" - ")){const r=o.split(" - ")[0].replace(",",".");a=new Date(r)}else a=new Date(o);else a=new Date(o);if(isNaN(a.getTime()))return"N/A"}catch(r){return console.warn("Time format error:",r,"Input:",o),"N/A"}if(e){const l=Math.floor((new Date-a)/(1e3*60));if(l<1)return"刚刚";if(l<60)return`${l}分钟前`;const d=Math.floor(l/60);if(d<24)return`${d}小时前`;const n=Math.floor(d/24);return n>365?`${Math.floor(n/365)}年前`:n>30?`${Math.floor(n/30)}个月前`:`${n}天前`}return a.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},R=(o,e,a=3e3)=>window.showToast?window.showToast(e,o,a):Ue.fire({icon:o,title:e,toast:!0,position:"top-end",showConfirmButton:!1,timer:a,timerProgressBar:!0,didOpen:r=>{r.addEventListener("mouseenter",Ue.stopTimer),r.addEventListener("mouseleave",Ue.resumeTimer)}}),gt=async(o,e)=>{const a=document.documentElement.classList.contains("dark");return(await Ue.fire({title:o,text:e,icon:"warning",showCancelButton:!0,confirmButtonColor:"#d33",cancelButtonColor:"#3085d6",confirmButtonText:"确认",cancelButtonText:"取消",customClass:{container:a?"dark":"",popup:a?"dark":""}})).isConfirmed},Ro=o=>({monthly:"月付",quarterly:"季付",semiannually:"半年付",annually:"年付",biennially:"两年付",triennially:"三年付"})[o]||o,zo=()=>window.innerWidth>=1024,ue=(o,e)=>{const a=o.__vccOpts||o;for(const[r,l]of e)a[r]=l;return a},To={name:"Dashboard",setup(){const o=_e(),e=A(()=>o.status),a=A(()=>o.logs),r=A(()=>o.isLoading),l=A(()=>e.value.is_running||e.value.monitor_running||o.monitorStatus==="running"||!1),d=s=>{if(!s)return"N/A";let m=null;if(s.timestamp)m=s.timestamp;else if(s.time)m=s.time;else if(s.created_at)m=s.created_at;else if(s.datetime)m=s.datetime;else if(typeof s=="string"&&s.includes(":")){const c=s.match(/(\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2})/);c&&(m=c[1])}if(!m)return new Date().toLocaleTimeString("zh-CN");try{const c=new Date(m);if(isNaN(c.getTime()))return m.toString();const b=new Date;return b-c<864e5&&c.toDateString()===b.toDateString()?c.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}):c.toLocaleString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch{return m.toString()}},n=s=>s?typeof s=="string"?s.replace(/^\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2}[^\s]*\s*/,"")||s:s.message||s.msg||s.content||s.text||String(s):"",i=s=>{if(!s)return"INFO";const c=(typeof s=="string"?s:s.message||s.msg||String(s)).toUpperCase();return c.includes("ERROR")||c.includes("失败")||c.includes("错误")?"ERROR":c.includes("WARNING")||c.includes("WARN")||c.includes("警告")?"WARN":c.includes("SUCCESS")||c.includes("成功")||c.includes("完成")?"SUCCESS":c.includes("DEBUG")?"DEBUG":"INFO"},p=s=>{switch(i(s)){case"ERROR":return"bg-red-500/10 text-red-600 dark:text-red-400 border border-red-500/20";case"WARN":return"bg-yellow-500/10 text-yellow-600 dark:text-yellow-400 border border-yellow-500/20";case"SUCCESS":return"bg-green-500/10 text-green-600 dark:text-green-400 border border-green-500/20";case"DEBUG":return"bg-gray-500/10 text-gray-600 dark:text-gray-400 border border-gray-500/20";default:return"bg-blue-500/10 text-blue-600 dark:text-blue-400 border border-blue-500/20"}};return we(async()=>{(!e.value||Object.keys(e.value).length===0)&&o.getStatus().catch(console.error),(!a.value||a.value.length===0)&&o.getLogs().catch(console.error)}),{status:e,logs:a,isLoading:r,isRunning:l,formatLogTime:d,formatLogMessage:n,getLogLevel:i,getLogLevelBadge:p}}},$o={class:"space-y-4"},Ao={class:"relative overflow-hidden bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 rounded-2xl p-4 text-white"},Ho={class:"relative z-10"},Io={class:"flex items-center justify-between"},Oo={class:"flex items-center space-x-6"},Fo={class:"flex items-center space-x-2"},Uo={class:"text-sm font-medium"},No={class:"text-sm text-blue-100"},Do={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4"},qo={class:"group relative overflow-hidden bg-gradient-to-br from-white via-blue-50/30 to-indigo-100/20 dark:from-gray-800 dark:via-blue-900/10 dark:to-indigo-900/20 rounded-2xl shadow-xl border border-blue-200/30 dark:border-blue-700/30 transition-all duration-500 hover:shadow-2xl hover:scale-[1.05] hover:-translate-y-2"},Wo={class:"relative p-4"},Go={class:"flex items-center justify-between mb-3"},Ko={class:"relative"},Qo={class:"p-2.5 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg transform transition-transform group-hover:scale-110 group-hover:rotate-3"},Jo={key:0,class:"absolute inset-0 bg-blue-400 rounded-2xl animate-ping opacity-20"},Yo={class:"relative"},Xo={class:"space-y-3"},Zo={class:"flex items-baseline space-x-3"},es={class:"text-sm text-gray-600 dark:text-gray-400 leading-relaxed"},ts={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden"},rs={class:"group relative overflow-hidden bg-gradient-to-br from-white via-emerald-50/30 to-green-100/20 dark:from-gray-800 dark:via-emerald-900/10 dark:to-green-900/20 rounded-2xl shadow-xl border border-emerald-200/30 dark:border-emerald-700/30 transition-all duration-500 hover:shadow-2xl hover:scale-[1.05] hover:-translate-y-2"},os={class:"relative p-4"},ss={class:"space-y-3"},as={class:"flex items-baseline space-x-2"},ns={class:"text-3xl font-black text-emerald-600 dark:text-emerald-400 tracking-tight"},ls={class:"flex items-center justify-between pt-2"},is={class:"text-xs text-emerald-600 dark:text-emerald-400 font-semibold bg-emerald-100 dark:bg-emerald-900/30 px-2 py-1 rounded-full"},ds={class:"group relative overflow-hidden bg-gradient-to-br from-white via-purple-50/30 to-pink-100/20 dark:from-gray-800 dark:via-purple-900/10 dark:to-pink-900/20 rounded-2xl shadow-xl border border-purple-200/30 dark:border-purple-700/30 transition-all duration-500 hover:shadow-2xl hover:scale-[1.05] hover:-translate-y-2"},us={class:"relative p-4"},cs={class:"space-y-3"},gs={class:"flex items-baseline space-x-2"},ms={class:"text-3xl font-black text-purple-600 dark:text-purple-400 tracking-tight"},fs={class:"group relative overflow-hidden bg-gradient-to-br from-white via-orange-50/30 to-red-100/20 dark:from-gray-800 dark:via-orange-900/10 dark:to-red-900/20 rounded-2xl shadow-xl border border-orange-200/30 dark:border-orange-700/30 transition-all duration-500 hover:shadow-2xl hover:scale-[1.05] hover:-translate-y-2"},vs={class:"relative p-4"},ps={class:"space-y-3"},bs={class:"flex items-baseline space-x-2"},xs={class:"text-3xl font-black text-orange-600 dark:text-orange-400 tracking-tight"},hs={class:"group relative overflow-hidden bg-gradient-to-br from-white via-teal-50/30 to-cyan-100/20 dark:from-gray-800 dark:via-teal-900/10 dark:to-cyan-900/20 rounded-2xl shadow-xl border border-teal-200/30 dark:border-teal-700/30 transition-all duration-500 hover:shadow-2xl hover:scale-[1.05] hover:-translate-y-2"},ys={class:"relative p-4"},ks={class:"flex items-center justify-between mb-3"},ws={class:"flex items-center space-x-1"},_s={class:"space-y-3"},Cs={class:"flex items-baseline space-x-2"},Ms={class:"text-3xl font-black text-teal-600 dark:text-teal-400 tracking-tight"},Ss={class:"flex items-center justify-between pt-2"},js={class:"text-xs text-teal-600 dark:text-teal-400 font-semibold bg-teal-100 dark:bg-teal-900/30 px-2 py-1 rounded-full"},Ls={class:"bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden"},Vs={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50"},Bs={class:"flex items-center justify-between"},Es={class:"flex items-center space-x-3"},Ps={class:"text-xs text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded-full"},Rs={class:"logs-container relative"},zs={class:"p-4 max-h-48 overflow-y-auto bg-gray-50 dark:bg-gray-900"},Ts={key:0,class:"text-center py-8"},$s={key:1,class:"space-y-1"},As={class:"flex-1 min-w-0"},Hs={class:"flex items-center space-x-2 mb-1"},Is={class:"timestamp inline-flex items-center px-2 py-1 rounded-md text-xs font-mono bg-blue-500/10 dark:bg-blue-500/10 text-blue-600 dark:text-blue-400 border border-blue-500/20"},Os={class:"log-message text-sm text-gray-700 dark:text-gray-300 leading-relaxed group-hover:text-gray-900 dark:group-hover:text-gray-200 transition-colors"};function Fs(o,e,a,r,l,d){return u(),g("div",$o,[t("div",Ao,[e[3]||(e[3]=t("div",{class:"absolute inset-0 bg-black/20"},null,-1)),e[4]||(e[4]=t("div",{class:"absolute inset-0 bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-indigo-600/20 backdrop-blur-sm"},null,-1)),e[5]||(e[5]=t("div",{class:"absolute top-0 right-0 -translate-y-12 translate-x-12 w-64 h-64 bg-white/10 rounded-full blur-3xl"},null,-1)),e[6]||(e[6]=t("div",{class:"absolute bottom-0 left-0 translate-y-12 -translate-x-12 w-48 h-48 bg-white/5 rounded-full blur-2xl"},null,-1)),t("div",Ho,[t("div",Io,[t("div",null,[e[0]||(e[0]=t("h1",{class:"text-xl font-bold mb-1 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent"}," 集成监控管理系统 ",-1)),e[1]||(e[1]=t("p",{class:"text-blue-100 text-sm mb-2"},"WHMCS库存监控 + LET RSS监控，实时预警、智能管理",-1)),t("div",Oo,[t("div",Fo,[t("div",{class:P(["w-3 h-3 rounded-full",r.isRunning?"bg-green-400 animate-pulse shadow-lg shadow-green-400/50":"bg-red-400 animate-pulse shadow-lg shadow-red-400/50"])},null,2),t("span",Uo,x(r.isRunning?"系统运行中":"系统已停止"),1)]),t("div",No," 上次更新: "+x(new Date().toLocaleTimeString("zh-CN")),1)])]),e[2]||(e[2]=t("div",{class:"hidden md:flex items-center space-x-4"},[t("div",{class:"p-2 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20"},[t("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1))])])]),t("div",Do,[t("div",qo,[e[10]||(e[10]=t("div",{class:"absolute inset-0"},[t("div",{class:"absolute inset-0 bg-gradient-to-br from-blue-500/[0.03] via-indigo-500/[0.02] to-purple-500/[0.03] dark:from-blue-400/[0.05] dark:via-indigo-400/[0.03] dark:to-purple-400/[0.05]"}),t("div",{class:"absolute top-0 right-0 -translate-y-6 translate-x-6 w-24 h-24 bg-blue-400/10 rounded-full blur-xl animate-pulse"}),t("div",{class:"absolute bottom-0 left-0 translate-y-6 -translate-x-6 w-20 h-20 bg-indigo-400/10 rounded-full blur-lg animate-pulse",style:{"animation-delay":"1s"}})],-1)),t("div",Wo,[t("div",Go,[t("div",Ko,[t("div",Qo,[e[7]||(e[7]=t("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})],-1)),r.isRunning?(u(),g("div",Jo)):M("",!0)])]),t("div",Yo,[t("div",{class:P(["w-4 h-4 rounded-full transition-all duration-300 shadow-lg",r.isRunning?"bg-green-400 animate-pulse shadow-green-400/50":"bg-red-400 animate-pulse shadow-red-400/50"])},[t("div",{class:P(["absolute inset-0 rounded-full animate-ping",r.isRunning?"bg-green-400":"bg-red-400"]),style:{"animation-duration":"2s"}},null,2)],2)])]),t("div",Xo,[e[9]||(e[9]=t("h3",{class:"text-lg font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"},"监控状态",-1)),t("div",Zo,[t("p",{class:P(["text-3xl font-black tracking-tight",r.isRunning?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"])},x(r.isRunning?"运行中":"已停止"),3),e[8]||(e[8]=t("div",{class:"flex items-center space-x-1"},[t("div",{class:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"}),t("div",{class:"w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse",style:{"animation-delay":"0.5s"}}),t("div",{class:"w-1 h-1 bg-blue-300 rounded-full animate-pulse",style:{"animation-delay":"1s"}})],-1))]),t("p",es,x(r.isRunning?"系统正在实时监控库存状态，为您提供7x24小时服务":"监控服务已停止，请启动监控服务"),1),t("div",ts,[t("div",{class:P(["h-full rounded-full transition-all duration-1000 ease-out",r.isRunning?"bg-gradient-to-r from-green-400 to-emerald-500 w-full":"bg-gradient-to-r from-red-400 to-red-500 w-0"])},null,2)])])])]),t("div",rs,[e[16]||(e[16]=t("div",{class:"absolute inset-0"},[t("div",{class:"absolute inset-0 bg-gradient-to-br from-emerald-500/[0.03] via-green-500/[0.02] to-teal-500/[0.03] dark:from-emerald-400/[0.05] dark:via-green-400/[0.03] dark:to-teal-400/[0.05]"}),t("div",{class:"absolute top-0 right-0 -translate-y-8 translate-x-8 w-28 h-28 bg-emerald-400/10 rounded-full blur-xl animate-pulse",style:{"animation-delay":"0.5s"}})],-1)),t("div",os,[e[15]||(e[15]=Y('<div class="flex items-center justify-between mb-3" data-v-4dac293e><div class="relative" data-v-4dac293e><div class="p-2.5 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl shadow-lg transform transition-transform group-hover:scale-110 group-hover:rotate-3" data-v-4dac293e><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-4dac293e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-v-4dac293e></path></svg><div class="absolute inset-0 bg-emerald-400 rounded-2xl animate-ping opacity-20" data-v-4dac293e></div></div></div><div class="flex items-center space-x-2" data-v-4dac293e><div class="w-3 h-3 bg-emerald-400 rounded-full animate-bounce" data-v-4dac293e></div><div class="w-2 h-2 bg-green-400 rounded-full animate-bounce" style="animation-delay:0.2s;" data-v-4dac293e></div><div class="w-1.5 h-1.5 bg-teal-400 rounded-full animate-bounce" style="animation-delay:0.4s;" data-v-4dac293e></div></div></div>',1)),t("div",ss,[e[13]||(e[13]=t("h3",{class:"text-lg font-bold text-gray-900 dark:text-white group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors"},"有货产品",-1)),t("div",as,[t("p",ns,x(r.status.in_stock_products||0),1),e[11]||(e[11]=Y('<div class="flex flex-col" data-v-4dac293e><span class="text-xs text-emerald-600 dark:text-emerald-400 font-semibold" data-v-4dac293e>款</span><div class="flex items-center space-x-1 mt-1" data-v-4dac293e><svg class="w-3 h-3 text-emerald-500" fill="currentColor" viewBox="0 0 20 20" data-v-4dac293e><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" data-v-4dac293e></path></svg><span class="text-xs text-emerald-500 font-medium" data-v-4dac293e>可购买</span></div></div>',1))]),e[14]||(e[14]=t("p",{class:"text-sm text-gray-600 dark:text-gray-400 leading-relaxed"}," 当前库存充足且可立即购买的产品 ",-1)),t("div",ls,[e[12]||(e[12]=Y('<div class="flex space-x-1" data-v-4dac293e><div class="w-2 h-8 bg-emerald-300 rounded-full" data-v-4dac293e></div><div class="w-2 h-6 bg-emerald-400 rounded-full" data-v-4dac293e></div><div class="w-2 h-10 bg-emerald-500 rounded-full" data-v-4dac293e></div><div class="w-2 h-4 bg-emerald-300 rounded-full" data-v-4dac293e></div><div class="w-2 h-7 bg-emerald-400 rounded-full" data-v-4dac293e></div></div>',1)),t("div",is," +"+x(Math.floor(Math.random()*10))+"% ",1)])])])]),t("div",ds,[e[21]||(e[21]=t("div",{class:"absolute inset-0"},[t("div",{class:"absolute inset-0 bg-gradient-to-br from-purple-500/[0.03] via-pink-500/[0.02] to-rose-500/[0.03] dark:from-purple-400/[0.05] dark:via-pink-400/[0.03] dark:to-rose-400/[0.05]"}),t("div",{class:"absolute top-0 right-0 -translate-y-10 translate-x-10 w-32 h-32 bg-purple-400/10 rounded-full blur-2xl animate-pulse",style:{"animation-delay":"1s"}})],-1)),t("div",us,[e[20]||(e[20]=Y('<div class="flex items-center justify-between mb-3" data-v-4dac293e><div class="relative" data-v-4dac293e><div class="p-2.5 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl shadow-lg transform transition-transform group-hover:scale-110 group-hover:rotate-3" data-v-4dac293e><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-4dac293e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" data-v-4dac293e></path></svg><div class="absolute inset-0 bg-purple-400 rounded-2xl animate-ping opacity-20" data-v-4dac293e></div></div></div><div class="grid grid-cols-2 gap-1" data-v-4dac293e><div class="w-2 h-2 bg-purple-400 rounded-sm animate-pulse" data-v-4dac293e></div><div class="w-2 h-2 bg-pink-400 rounded-sm animate-pulse" style="animation-delay:0.3s;" data-v-4dac293e></div><div class="w-2 h-2 bg-purple-300 rounded-sm animate-pulse" style="animation-delay:0.6s;" data-v-4dac293e></div><div class="w-2 h-2 bg-pink-300 rounded-sm animate-pulse" style="animation-delay:0.9s;" data-v-4dac293e></div></div></div>',1)),t("div",cs,[e[18]||(e[18]=t("h3",{class:"text-lg font-bold text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors"},"网站数量",-1)),t("div",gs,[t("p",ms,x(r.status.total_sites||0),1),e[17]||(e[17]=t("div",{class:"flex flex-col items-center"},[t("span",{class:"text-xs text-purple-600 dark:text-purple-400 font-semibold"},"个"),t("svg",{class:"w-4 h-4 text-purple-500 mt-1",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"})])],-1))]),e[19]||(e[19]=Y('<p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed" data-v-4dac293e> 正在实时监控的WHMCS网站总数 </p><div class="flex items-center justify-between pt-2" data-v-4dac293e><div class="flex space-x-2" data-v-4dac293e><div class="flex items-center space-x-1" data-v-4dac293e><div class="w-2 h-2 bg-green-400 rounded-full" data-v-4dac293e></div><span class="text-xs text-gray-600 dark:text-gray-400" data-v-4dac293e>在线</span></div><div class="flex items-center space-x-1" data-v-4dac293e><div class="w-2 h-2 bg-yellow-400 rounded-full" data-v-4dac293e></div><span class="text-xs text-gray-600 dark:text-gray-400" data-v-4dac293e>等待</span></div></div><div class="text-xs text-purple-600 dark:text-purple-400 font-semibold bg-purple-100 dark:bg-purple-900/30 px-2 py-1 rounded-full" data-v-4dac293e> 全部正常 </div></div>',2))])])]),t("div",fs,[e[26]||(e[26]=t("div",{class:"absolute inset-0"},[t("div",{class:"absolute inset-0 bg-gradient-to-br from-orange-500/[0.03] via-red-500/[0.02] to-pink-500/[0.03] dark:from-orange-400/[0.05] dark:via-red-400/[0.03] dark:to-pink-400/[0.05]"}),t("div",{class:"absolute bottom-0 left-0 translate-y-8 -translate-x-8 w-24 h-24 bg-orange-400/10 rounded-full blur-xl animate-pulse",style:{"animation-delay":"1.5s"}}),t("div",{class:"absolute top-0 right-0 -translate-y-4 translate-x-4 w-16 h-16 bg-red-400/10 rounded-full blur-lg animate-pulse",style:{"animation-delay":"0.8s"}})],-1)),t("div",vs,[e[25]||(e[25]=Y('<div class="flex items-center justify-between mb-3" data-v-4dac293e><div class="relative" data-v-4dac293e><div class="p-2.5 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl shadow-lg transform transition-transform group-hover:scale-110 group-hover:rotate-3" data-v-4dac293e><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-4dac293e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" data-v-4dac293e></path></svg><div class="absolute inset-0 bg-orange-400 rounded-2xl animate-ping opacity-20" data-v-4dac293e></div></div></div><div class="flex flex-col space-y-1" data-v-4dac293e><div class="flex space-x-1" data-v-4dac293e><div class="w-1.5 h-1.5 bg-orange-400 rounded-full animate-pulse" data-v-4dac293e></div><div class="w-1.5 h-1.5 bg-red-400 rounded-full animate-pulse" style="animation-delay:0.2s;" data-v-4dac293e></div></div><div class="flex space-x-1" data-v-4dac293e><div class="w-1.5 h-1.5 bg-orange-300 rounded-full animate-pulse" style="animation-delay:0.4s;" data-v-4dac293e></div><div class="w-1.5 h-1.5 bg-red-300 rounded-full animate-pulse" style="animation-delay:0.6s;" data-v-4dac293e></div></div></div></div>',1)),t("div",ps,[e[23]||(e[23]=t("h3",{class:"text-lg font-bold text-gray-900 dark:text-white group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors"},"产品总数",-1)),t("div",bs,[t("p",xs,x(r.status.total_products||0),1),e[22]||(e[22]=t("div",{class:"flex flex-col items-center"},[t("span",{class:"text-xs text-orange-600 dark:text-orange-400 font-semibold"},"种"),t("svg",{class:"w-4 h-4 text-orange-500 mt-1",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 2L3 7v11a1 1 0 001 1h4v-6h4v6h4a1 1 0 001-1V7l-7-5z","clip-rule":"evenodd"})])],-1))]),e[24]||(e[24]=Y('<p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed" data-v-4dac293e> 跨所有网站的产品类目总数量 </p><div class="flex items-center justify-between pt-2" data-v-4dac293e><div class="flex items-center space-x-3" data-v-4dac293e><div class="flex items-center space-x-1" data-v-4dac293e><div class="w-2 h-2 bg-green-400 rounded-full" data-v-4dac293e></div><span class="text-xs text-gray-600 dark:text-gray-400" data-v-4dac293e>可用</span></div><div class="flex items-center space-x-1" data-v-4dac293e><div class="w-2 h-2 bg-orange-400 rounded-full" data-v-4dac293e></div><span class="text-xs text-gray-600 dark:text-gray-400" data-v-4dac293e>监控</span></div></div><div class="text-xs text-orange-600 dark:text-orange-400 font-semibold bg-orange-100 dark:bg-orange-900/30 px-2 py-1 rounded-full" data-v-4dac293e> 多样化 </div></div>',2))])])]),t("div",hs,[e[32]||(e[32]=t("div",{class:"absolute inset-0"},[t("div",{class:"absolute inset-0 bg-gradient-to-br from-teal-500/[0.03] via-cyan-500/[0.02] to-blue-500/[0.03] dark:from-teal-400/[0.05] dark:via-cyan-400/[0.03] dark:to-blue-400/[0.05]"}),t("div",{class:"absolute bottom-0 right-0 translate-y-6 translate-x-6 w-20 h-20 bg-teal-400/10 rounded-full blur-xl animate-pulse",style:{"animation-delay":"2s"}}),t("div",{class:"absolute top-0 left-0 -translate-y-4 -translate-x-4 w-16 h-16 bg-cyan-400/10 rounded-full blur-lg animate-pulse",style:{"animation-delay":"1.2s"}})],-1)),t("div",ys,[t("div",ks,[e[27]||(e[27]=Y('<div class="relative" data-v-4dac293e><div class="p-2.5 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-2xl shadow-lg transform transition-transform group-hover:scale-110 group-hover:rotate-3" data-v-4dac293e><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-4dac293e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" data-v-4dac293e></path></svg><div class="absolute inset-0 bg-teal-400 rounded-2xl animate-ping opacity-20" data-v-4dac293e></div></div></div>',1)),t("div",ws,[t("div",{class:P(["w-3 h-3 rounded-full transition-all duration-300",r.status.let_monitor_running||r.status.let_processed_posts>0?"bg-green-400 animate-pulse":"bg-gray-400"])},null,2),t("span",{class:P(["text-xs font-medium",r.status.let_monitor_running||r.status.let_processed_posts>0?"text-green-600 dark:text-green-400":"text-gray-500"])},x(r.status.let_monitor_running||r.status.let_processed_posts>0?"活跃":"待机"),3)])]),t("div",_s,[e[30]||(e[30]=t("h3",{class:"text-lg font-bold text-gray-900 dark:text-white group-hover:text-teal-600 dark:group-hover:text-teal-400 transition-colors"},"LET监控",-1)),t("div",Cs,[t("p",Ms,x(r.status.let_stored_posts||0),1),e[28]||(e[28]=Y('<div class="flex flex-col items-center" data-v-4dac293e><span class="text-xs text-teal-600 dark:text-teal-400 font-semibold" data-v-4dac293e>帖</span><svg class="w-4 h-4 text-teal-500 mt-1" fill="currentColor" viewBox="0 0 20 20" data-v-4dac293e><path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" data-v-4dac293e></path><path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z" data-v-4dac293e></path></svg></div>',1))]),e[31]||(e[31]=t("p",{class:"text-sm text-gray-600 dark:text-gray-400 leading-relaxed"}," 已监控并存储的LET优惠帖子 ",-1)),t("div",Ss,[e[29]||(e[29]=Y('<div class="flex items-center space-x-3" data-v-4dac293e><div class="flex items-center space-x-1" data-v-4dac293e><div class="w-2 h-2 bg-blue-400 rounded-full" data-v-4dac293e></div><span class="text-xs text-gray-600 dark:text-gray-400" data-v-4dac293e>RSS</span></div><div class="flex items-center space-x-1" data-v-4dac293e><div class="w-2 h-2 bg-teal-400 rounded-full" data-v-4dac293e></div><span class="text-xs text-gray-600 dark:text-gray-400" data-v-4dac293e>解析</span></div></div>',1)),t("div",js,x(r.status.let_monitor_running||r.status.let_processed_posts>0?"运行中":"离线"),1)])])])])]),t("div",Ls,[t("div",Vs,[t("div",Bs,[e[34]||(e[34]=Y('<div class="flex items-center space-x-3" data-v-4dac293e><div class="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg" data-v-4dac293e><svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-4dac293e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" data-v-4dac293e></path></svg></div><div data-v-4dac293e><h3 class="text-lg font-semibold text-gray-900 dark:text-white" data-v-4dac293e>实时日志</h3><p class="text-sm text-gray-500 dark:text-gray-400" data-v-4dac293e>系统运行状态和监控信息</p></div></div>',1)),t("div",Es,[t("span",Ps,x(r.logs.length)+" 条记录 ",1),e[33]||(e[33]=t("div",{class:"flex items-center space-x-2"},[t("div",{class:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),t("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"实时更新")],-1))])])]),t("div",Rs,[t("div",zs,[r.logs.length===0?(u(),g("div",Ts,e[35]||(e[35]=[Y('<div class="w-12 h-12 mx-auto mb-4 bg-gray-200 dark:bg-gray-800 rounded-full flex items-center justify-center" data-v-4dac293e><svg class="w-6 h-6 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-4dac293e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" data-v-4dac293e></path></svg></div><p class="text-gray-500 dark:text-gray-400 font-medium" data-v-4dac293e>暂无日志记录</p><p class="text-gray-400 dark:text-gray-500 text-sm mt-1" data-v-4dac293e>启动监控后将显示实时日志信息</p>',3)]))):(u(),g("div",$s,[(u(!0),g(ne,null,de(r.logs.slice(-50).reverse(),(n,i)=>(u(),g("div",{key:i,class:"log-item group flex items-start space-x-3 py-2 px-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800/30 transition-all duration-200 border-l-2 border-transparent hover:border-blue-500/50 dark:hover:border-green-500/50"},[e[37]||(e[37]=t("div",{class:"flex-shrink-0 flex flex-col items-center"},[t("div",{class:"w-2 h-2 bg-blue-500 dark:bg-green-400 rounded-full opacity-60 group-hover:opacity-100 transition-opacity"}),t("div",{class:"w-px h-4 bg-gray-300 dark:bg-gray-700 mt-1 opacity-40"})],-1)),t("div",As,[t("div",Hs,[t("span",Is,[e[36]||(e[36]=t("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),$(" "+x(r.formatLogTime(n)),1)]),t("span",{class:P([r.getLogLevelBadge(n),"text-xs font-medium px-2 py-1 rounded-md"])},x(r.getLogLevel(n)),3)]),t("div",Os,x(r.formatLogMessage(n)),1)])]))),128))]))]),e[38]||(e[38]=t("div",{class:"absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-gray-50 dark:from-gray-900 via-gray-50/80 dark:via-gray-900/80 to-transparent pointer-events-none"},null,-1)),e[39]||(e[39]=t("div",{class:"absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-gray-50 dark:from-gray-900 via-gray-50/80 dark:via-gray-900/80 to-transparent pointer-events-none"},null,-1))])])])}const Us=ue(To,[["render",Fs],["__scopeId","data-v-4dac293e"]]),Ns={name:"FormField",props:{modelValue:{type:[String,Number],default:""},label:{type:String,default:""},type:{type:String,default:"text"},placeholder:{type:String,default:""},disabled:{type:Boolean,default:!1},required:{type:Boolean,default:!1},errorMessage:{type:String,default:""},helpText:{type:String,default:""},icon:{type:[String,Object],default:null},prefixIcon:{type:[String,Object],default:null},suffixIcon:{type:[String,Object],default:null},rows:{type:Number,default:4},min:{type:[String,Number],default:null},max:{type:[String,Number],default:null},step:{type:[String,Number],default:null}},emits:["update:modelValue","focus","blur","input"],setup(o,{emit:e}){vr();const a=O(!1),r=A(()=>`field-${Math.random().toString(36).substr(2,9)}`),l=A(()=>!!o.errorMessage),d=A(()=>o.type);return{isFocused:a,fieldId:r,hasError:l,inputType:d,handleFocus:s=>{a.value=!0,e("focus",s)},handleBlur:s=>{a.value=!1,e("blur",s)},handleInput:s=>{e("update:modelValue",s.target.value),e("input",s)}}}},Ds=["for"],qs={class:"flex items-center"},Ws={key:0,class:"text-red-500 ml-1"},Gs={class:"form-input-container"},Ks=["id","value","placeholder","disabled","required","rows"],Qs=["id","value","type","placeholder","disabled","required","min","max","step"],Js={key:2,class:"form-suffix"},Ys={class:"form-message"},Xs={key:0,class:"form-error"},Zs={key:0,class:"form-help"};function ea(o,e,a,r,l,d){return u(),g("div",{class:P(["form-field",{"has-error":r.hasError,"is-focused":r.isFocused}])},[a.label?(u(),g("label",{key:0,for:r.fieldId,class:P(["form-label",a.required&&"required",r.hasError&&"text-red-600 dark:text-red-400",r.isFocused&&"text-indigo-600 dark:text-indigo-400"])},[t("span",qs,[De(o.$slots,"prefix",{},()=>[a.prefixIcon?(u(),Be(We(a.prefixIcon),{key:0,class:"w-4 h-4 mr-2"})):M("",!0)]),$(" "+x(a.label)+" ",1),a.required?(u(),g("span",Ws,"*")):M("",!0)])],10,Ds)):M("",!0),t("div",Gs,[a.type==="textarea"?(u(),g("textarea",{key:0,id:r.fieldId,value:a.modelValue,placeholder:a.placeholder,disabled:a.disabled,required:a.required,rows:a.rows,class:P(["form-input form-textarea",r.hasError&&"border-red-500 focus:border-red-500 focus:ring-red-500",!r.hasError&&"border-gray-300 dark:border-gray-600 focus:border-indigo-500 focus:ring-indigo-500"]),onFocus:e[0]||(e[0]=(...n)=>r.handleFocus&&r.handleFocus(...n)),onBlur:e[1]||(e[1]=(...n)=>r.handleBlur&&r.handleBlur(...n)),onInput:e[2]||(e[2]=(...n)=>r.handleInput&&r.handleInput(...n))},null,42,Ks)):(u(),g("input",{key:1,id:r.fieldId,value:a.modelValue,type:r.inputType,placeholder:a.placeholder,disabled:a.disabled,required:a.required,min:a.min,max:a.max,step:a.step,class:P(["form-input",r.hasError&&"border-red-500 focus:border-red-500 focus:ring-red-500",!r.hasError&&"border-gray-300 dark:border-gray-600 focus:border-indigo-500 focus:ring-indigo-500"]),onFocus:e[3]||(e[3]=(...n)=>r.handleFocus&&r.handleFocus(...n)),onBlur:e[4]||(e[4]=(...n)=>r.handleBlur&&r.handleBlur(...n)),onInput:e[5]||(e[5]=(...n)=>r.handleInput&&r.handleInput(...n))},null,42,Qs)),o.$slots.suffix||a.suffixIcon?(u(),g("div",Js,[De(o.$slots,"suffix",{},()=>[a.suffixIcon?(u(),Be(We(a.suffixIcon),{key:0,class:"w-5 h-5 text-gray-400"})):M("",!0)])])):M("",!0)]),t("div",Ys,[q(qe,{name:"slide-down"},{default:K(()=>[r.hasError?(u(),g("div",Xs,[e[6]||(e[6]=t("svg",{class:"w-4 h-4 mr-1 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),$(" "+x(a.errorMessage),1)])):M("",!0)]),_:1}),a.helpText&&!r.hasError?(u(),g("div",Zs,x(a.helpText),1)):M("",!0)])],2)}const ar=ue(Ns,[["render",ea],["__scopeId","data-v-e475a89a"]]),ta={name:"FormSelect",props:{modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"请选择..."},options:{type:Array,default:()=>[],validator:o=>o.every(e=>typeof e=="object"&&"value"in e&&"label"in e)},disabled:{type:Boolean,default:!1},required:{type:Boolean,default:!1},errorMessage:{type:String,default:""},helpText:{type:String,default:""},icon:{type:[String,Object],default:null},prefixIcon:{type:[String,Object],default:null}},emits:["update:modelValue","focus","blur","change"],setup(o,{emit:e}){const a=O(!1),r=A(()=>`select-${Math.random().toString(36).substr(2,9)}`),l=A(()=>!!o.errorMessage);return{isFocused:a,fieldId:r,hasError:l,handleFocus:p=>{a.value=!0,e("focus",p)},handleBlur:p=>{a.value=!1,e("blur",p)},handleChange:p=>{e("update:modelValue",p.target.value),e("change",p)}}}},ra=["for"],oa={class:"flex items-center"},sa={key:0,class:"text-red-500 ml-1"},aa={class:"form-select-container"},na=["id","value","disabled","required"],la={key:0,value:"",disabled:""},ia=["value","disabled"],da={class:"form-message"},ua={key:0,class:"form-error"},ca={key:0,class:"form-help"};function ga(o,e,a,r,l,d){return u(),g("div",{class:P(["form-select-field",{"has-error":r.hasError,"is-focused":r.isFocused}])},[a.label?(u(),g("label",{key:0,for:r.fieldId,class:P(["form-label",a.required&&"required",r.hasError&&"text-red-600 dark:text-red-400",r.isFocused&&"text-indigo-600 dark:text-indigo-400"])},[t("span",oa,[De(o.$slots,"prefix",{},()=>[a.prefixIcon?(u(),Be(We(a.prefixIcon),{key:0,class:"w-4 h-4 mr-2"})):M("",!0)]),$(" "+x(a.label)+" ",1),a.required?(u(),g("span",sa,"*")):M("",!0)])],10,ra)):M("",!0),t("div",aa,[t("select",{id:r.fieldId,value:a.modelValue,disabled:a.disabled,required:a.required,class:P(["form-select",r.hasError&&"border-red-500 focus:border-red-500 focus:ring-red-500",!r.hasError&&"border-gray-300 dark:border-gray-600 focus:border-indigo-500 focus:ring-indigo-500"]),onFocus:e[0]||(e[0]=(...n)=>r.handleFocus&&r.handleFocus(...n)),onBlur:e[1]||(e[1]=(...n)=>r.handleBlur&&r.handleBlur(...n)),onChange:e[2]||(e[2]=(...n)=>r.handleChange&&r.handleChange(...n))},[a.placeholder?(u(),g("option",la,x(a.placeholder),1)):M("",!0),(u(!0),g(ne,null,de(a.options,n=>(u(),g("option",{key:n.value,value:n.value,disabled:n.disabled},x(n.label),9,ia))),128))],42,na),e[3]||(e[3]=t("div",{class:"form-arrow"},[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),t("div",da,[q(qe,{name:"slide-down"},{default:K(()=>[r.hasError?(u(),g("div",ua,[e[4]||(e[4]=t("svg",{class:"w-4 h-4 mr-1 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),$(" "+x(a.errorMessage),1)])):M("",!0)]),_:1}),a.helpText&&!r.hasError?(u(),g("div",ca,x(a.helpText),1)):M("",!0)])],2)}const nr=ue(ta,[["render",ga],["__scopeId","data-v-1cbe23b6"]]),ma={name:"FormCombobox",props:{modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"请输入或选择..."},options:{type:Array,default:()=>[],validator:o=>o.every(e=>typeof e=="object"&&"value"in e&&"label"in e)},disabled:{type:Boolean,default:!1},required:{type:Boolean,default:!1},errorMessage:{type:String,default:""},helpText:{type:String,default:""},prefixIcon:{type:[String,Object],default:null},filterMatchMode:{type:String,default:"contains",validator:o=>["contains","startsWith","exact"].includes(o)}},emits:["update:modelValue","focus","blur","input","select"],setup(o,{emit:e}){const a=O(!1),r=O(!1),l=O(-1),d=A(()=>`combobox-${Math.random().toString(36).substr(2,9)}`),n=A(()=>!!o.errorMessage),i=A(()=>{if(!o.modelValue)return o.options;const v=o.modelValue.toString().toLowerCase();return o.options.filter(k=>{const w=k.label.toLowerCase(),E=k.value.toString().toLowerCase();switch(o.filterMatchMode){case"startsWith":return w.startsWith(v)||E.startsWith(v);case"exact":return w===v||E===v;case"contains":default:return w.includes(v)||E.includes(v)}})}),p=A(()=>o.options.some(v=>v.value===o.modelValue||v.label===o.modelValue)),s=v=>{a.value=!0,e("focus",v)},m=v=>{setTimeout(()=>{a.value=!1,r.value=!1,l.value=-1},150),e("blur",v)},c=v=>{const k=v.target.value;e("update:modelValue",k),e("input",v),r.value||(r.value=!0),l.value=-1},b=v=>{if(!r.value&&(v.key==="ArrowDown"||v.key==="ArrowUp")){r.value=!0,l.value=0,v.preventDefault();return}if(r.value)switch(v.key){case"ArrowDown":l.value=Math.min(l.value+1,i.value.length-1),v.preventDefault();break;case"ArrowUp":l.value=Math.max(l.value-1,0),v.preventDefault();break;case"Enter":l.value>=0&&i.value[l.value]&&B(i.value[l.value]),v.preventDefault();break;case"Escape":r.value=!1,l.value=-1,v.preventDefault();break}},h=()=>{o.disabled||(r.value=!r.value,r.value&&(l.value=-1))},B=v=>{e("update:modelValue",v.value),e("select",v),r.value=!1,l.value=-1},C=v=>{v.target.closest(".form-combobox-field")||(r.value=!1,l.value=-1)};return we(()=>{document.addEventListener("click",C)}),Ft(()=>{document.removeEventListener("click",C)}),{isFocused:a,isOpen:r,selectedIndex:l,fieldId:d,hasError:n,filteredOptions:i,exactMatch:p,handleFocus:s,handleBlur:m,handleInput:c,handleKeydown:b,toggleDropdown:h,selectOption:B}}},fa=["for"],va={class:"flex items-center"},pa={key:0,class:"text-red-500 ml-1"},ba={class:"form-combobox-container"},xa=["id","value","placeholder","disabled","required"],ha=["disabled"],ya={key:0,class:"form-combobox-dropdown"},ka={class:"max-h-60 overflow-y-auto"},wa=["onClick","onMouseenter"],_a={class:"flex items-center justify-between"},Ca={class:"font-medium text-gray-900 dark:text-white"},Ma={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Sa={key:0,class:"text-indigo-600 dark:text-indigo-400"},ja={key:0,class:"form-combobox-custom"},La={class:"flex items-center text-sm text-gray-600 dark:text-gray-400 px-3 py-2 border-t border-gray-200 dark:border-gray-600"},Va={class:"form-message"},Ba={key:0,class:"form-error"},Ea={key:0,class:"form-help"};function Pa(o,e,a,r,l,d){return u(),g("div",{class:P(["form-combobox-field",{"has-error":r.hasError,"is-focused":r.isFocused}])},[a.label?(u(),g("label",{key:0,for:r.fieldId,class:P(["form-label",a.required&&"required",r.hasError&&"text-red-600 dark:text-red-400",r.isFocused&&"text-indigo-600 dark:text-indigo-400"])},[t("span",va,[De(o.$slots,"prefix",{},()=>[a.prefixIcon?(u(),Be(We(a.prefixIcon),{key:0,class:"w-4 h-4 mr-2"})):M("",!0)]),$(" "+x(a.label)+" ",1),a.required?(u(),g("span",pa,"*")):M("",!0)])],10,fa)):M("",!0),t("div",ba,[t("input",{id:r.fieldId,value:a.modelValue,type:"text",placeholder:a.placeholder,disabled:a.disabled,required:a.required,class:P(["form-combobox-input",r.hasError&&"border-red-500 focus:border-red-500 focus:ring-red-500",!r.hasError&&"border-gray-300 dark:border-gray-600 focus:border-indigo-500 focus:ring-indigo-500"]),onFocus:e[0]||(e[0]=(...n)=>r.handleFocus&&r.handleFocus(...n)),onBlur:e[1]||(e[1]=(...n)=>r.handleBlur&&r.handleBlur(...n)),onInput:e[2]||(e[2]=(...n)=>r.handleInput&&r.handleInput(...n)),onKeydown:e[3]||(e[3]=(...n)=>r.handleKeydown&&r.handleKeydown(...n)),autocomplete:"off"},null,42,xa),t("button",{type:"button",class:"form-combobox-button",onClick:e[4]||(e[4]=(...n)=>r.toggleDropdown&&r.toggleDropdown(...n)),disabled:a.disabled},[(u(),g("svg",{class:P(["w-5 h-5 text-gray-400 transition-transform duration-200",{"rotate-180":r.isOpen}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},e[5]||(e[5]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2))],8,ha),q(qe,{name:"dropdown"},{default:K(()=>[r.isOpen&&r.filteredOptions.length>0?(u(),g("div",ya,[t("div",ka,[(u(!0),g(ne,null,de(r.filteredOptions,(n,i)=>(u(),g("div",{key:n.value,class:P(["form-combobox-option",i===r.selectedIndex&&"selected",n.value===a.modelValue&&"active"]),onClick:p=>r.selectOption(n),onMouseenter:p=>r.selectedIndex=i},[t("div",_a,[t("div",null,[t("div",Ca,x(n.label),1),n.description?(u(),g("div",Ma,x(n.description),1)):M("",!0)]),n.value===a.modelValue?(u(),g("div",Sa,e[6]||(e[6]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):M("",!0)])],42,wa))),128))]),a.modelValue&&!r.exactMatch?(u(),g("div",ja,[t("div",La,[e[7]||(e[7]=t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)),$(' 使用自定义值: "'+x(a.modelValue)+'" ',1)])])):M("",!0)])):M("",!0)]),_:1})]),t("div",Va,[q(qe,{name:"slide-down"},{default:K(()=>[r.hasError?(u(),g("div",Ba,[e[8]||(e[8]=t("svg",{class:"w-4 h-4 mr-1 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),$(" "+x(a.errorMessage),1)])):M("",!0)]),_:1}),a.helpText&&!r.hasError?(u(),g("div",Ea,x(a.helpText),1)):M("",!0)])],2)}const Ra=ue(ma,[["render",Pa],["__scopeId","data-v-8f9f7d89"]]),za={name:"Sites",components:{FormField:ar,FormSelect:nr,FormCombobox:Ra},setup(){Eo();const o=_e(),e=ie({sites:!1,testingLogin:null,savingSite:!1}),a=A(()=>{const v={};return o.sites.forEach(k=>{v[k.site_id]=k}),v}),r=O(!1),l=O(null),d=ie({siteId:"",base_url:"",username:"",password:"",hostname_base:"",rootpw:"",payment_method:"cryptomusgateway",cookies_string:""}),n=[{value:"cryptomusgateway",label:"CryptoMus Gateway",description:"加密货币支付网关"},{value:"coinbase",label:"Coinbase Commerce",description:"正规加密货币支付平台"},{value:"coinpayments",label:"CoinPayments",description:"多种加密货币支持"},{value:"btcpay",label:"BTCPay Server",description:"去中心化比特币支付"},{value:"paypal",label:"PayPal",description:"全球领先的在线支付平台"},{value:"stripe",label:"Stripe",description:"专业的在线支付解决方案"},{value:"square",label:"Square",description:"综合商业支付解决方案"},{value:"2checkout",label:"2Checkout (Verifone)",description:"全球支付处理平台"},{value:"alipay",label:"支付宝",description:"蚂蚁金服旗下支付平台"},{value:"wechatpay",label:"微信支付",description:"腾讯旗下移动支付平台"},{value:"unionpay",label:"中国银联",description:"中国银行卡统一支付平台"},{value:"mollie",label:"Mollie",description:"欧洲本土化支付平台"},{value:"razorpay",label:"Razorpay",description:"印度领先支付网关"},{value:"payu",label:"PayU",description:"新兴市场支付解决方案"},{value:"banktransfer",label:"Bank Transfer",description:"银行转账/电汇"},{value:"ach",label:"ACH Transfer",description:"美国自动结算系统"},{value:"sepa",label:"SEPA Direct Debit",description:"欧元区直接借记"},{value:"skrill",label:"Skrill",description:"数字钱包和支付服务"},{value:"neteller",label:"NETELLER",description:"在线资金转移服务"},{value:"perfectmoney",label:"Perfect Money",description:"数字货币系统"},{value:"authorize",label:"Authorize.Net",description:"老牌支付处理商"},{value:"worldpay",label:"Worldpay",description:"全球支付技术公司"},{value:"adyen",label:"Adyen",description:"全渠道支付平台"},{value:"braintree",label:"Braintree",description:"PayPal 旗下支付平台"},{value:"free",label:"Free Account",description:"免费账户，无需支付"},{value:"manual",label:"Manual Payment",description:"手动处理支付"},{value:"invoice",label:"Invoice Payment",description:"发票后付款"}],i=A(()=>Object.entries(a.value).map(([v,k])=>({site_id:v,...k}))),p=v=>{try{return new URL(v).hostname}catch{return v}},s=v=>{const k=a.value[v];return(k==null?void 0:k.enabled_products)||0},m=v=>{const k=a.value[v];return(k==null?void 0:k.auto_order_products)||0},c=(v=null,k=null)=>{l.value=v,k?Object.assign(d,{siteId:v,base_url:k.base_url,username:k.username,password:"",hostname_base:k.hostname_base,rootpw:"",payment_method:k.payment_method||"cryptomusgateway",cookies_string:k.cookies_string||""}):Object.assign(d,{siteId:"",base_url:"",username:"",password:"",hostname_base:"",rootpw:"",payment_method:"cryptomusgateway",cookies_string:""}),r.value=!0},b=()=>{r.value=!1,l.value=null},h=async()=>{e.savingSite=!0;try{const v={base_url:d.base_url,username:d.username,hostname_base:d.hostname_base,payment_method:d.payment_method,cookies_string:d.cookies_string};d.password&&(v.password=d.password),d.rootpw&&(v.rootpw=d.rootpw);const k=await re.post(`/api/sites/${d.siteId}`,v);R("success",k.message),b(),await o.getSites()}catch(v){console.error("保存网站失败:",v),R("error","保存网站失败")}finally{e.savingSite=!1}},B=async v=>{if(await gt("确认删除网站",`确定要删除网站 "${v}" 吗？此操作不可撤销。`))try{await o.deleteSite(v)?R("success","网站删除成功"):R("error","删除网站失败")}catch(w){console.error("删除网站失败:",w),R("error","删除网站失败")}},C=async v=>{e.testingLogin=v;try{const k=await o.testLogin(v);k.success?R("success","登录测试成功"):R("error",`登录测试失败: ${k.message}`)}catch(k){console.error("测试登录失败:",k),R("error","测试登录失败")}finally{e.testingLogin=null}};return we(async()=>{(!o.sites||o.sites.length===0)&&o.getSites().catch(console.error)}),{isLoading:A(()=>({...e,sites:o.isLoading.sites})),sites:a,siteList:i,showSiteModal:r,editingSiteId:l,siteForm:d,formatUrl:p,openSiteModal:c,closeSiteModal:b,saveSite:h,deleteSite:B,testLogin:C,getEnabledProductsCount:s,getAutoOrderProductsCount:m,paymentMethods:n}}},Ta={class:"space-y-6"},$a={class:"flex items-center justify-between"},Aa={key:0,class:"text-center p-12"},Ha={key:1,class:"text-center p-12 bg-white dark:bg-gray-800 rounded-xl shadow-sm"},Ia={key:2,class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},Oa={class:"p-4 border-b border-gray-200 dark:border-gray-700"},Fa={class:"flex items-start justify-between mb-3"},Ua={class:"flex-1"},Na={class:"flex items-center space-x-2 mb-2"},Da={class:"text-lg font-semibold text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors"},qa=["href"],Wa={class:"truncate"},Ga={class:"mt-2 flex items-center space-x-2"},Ka={class:"text-xs text-gray-600 dark:text-gray-300"},Qa={key:0,class:"inline-flex items-center px-1.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded text-xs font-medium"},Ja={key:1,class:"inline-flex items-center px-1.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded text-xs font-medium"},Ya={class:"flex space-x-2"},Xa=["onClick","disabled"],Za={key:0,class:"absolute inset-0 bg-gradient-to-r from-emerald-600 to-green-600 rounded-lg opacity-75 animate-pulse"},en={key:1,class:"animate-spin h-4 w-4 relative z-10",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},tn={key:2,xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},rn={key:3,class:"absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full animate-ping"},on=["onClick"],sn=["onClick"],an={class:"p-4"},nn={class:"grid grid-cols-3 gap-2 mb-3"},ln={class:"text-center p-2 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded border border-blue-200/50 dark:border-blue-700/50"},dn={class:"text-lg font-bold text-blue-600 dark:text-blue-400"},un={class:"text-center p-2 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded border border-green-200/50 dark:border-green-700/50"},cn={class:"text-lg font-bold text-green-600 dark:text-green-400"},gn={class:"text-center p-2 bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded border border-orange-200/50 dark:border-orange-700/50"},mn={class:"text-lg font-bold text-orange-600 dark:text-orange-400"},fn={key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},vn={class:"bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"},pn={class:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700"},bn={class:"text-xl font-semibold text-gray-900 dark:text-white"},xn={class:"space-y-4"},hn={class:"space-y-4"},yn={class:"space-y-4"},kn={class:"space-y-2"},wn={class:"flex justify-end space-x-3 pt-4"},_n=["disabled"],Cn={key:0,class:"animate-spin h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"};function Mn(o,e,a,r,l,d){const n=ye("router-link"),i=ye("FormField"),p=ye("FormCombobox");return u(),g("div",Ta,[t("div",$a,[e[14]||(e[14]=t("div",null,[t("h2",{class:"text-3xl font-bold text-gray-900 dark:text-white"},"网站管理"),t("p",{class:"text-gray-600 dark:text-gray-400 mt-1"},"管理 WHMCS 网站和产品配置")],-1)),t("button",{onClick:e[0]||(e[0]=s=>r.openSiteModal()),class:"bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200 flex items-center space-x-2"},e[13]||(e[13]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),t("span",null,"添加网站",-1)]))]),r.isLoading.sites?(u(),g("div",Aa,e[15]||(e[15]=[t("div",{class:"spinner border-4 border-indigo-500 rounded-full w-8 h-8 mx-auto mb-4"},null,-1),t("p",{class:"text-gray-500"},"加载网站数据中...",-1)]))):r.siteList.length===0?(u(),g("div",Ha,[e[16]||(e[16]=t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-16 w-16 text-gray-400 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),e[17]||(e[17]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"暂无网站",-1)),e[18]||(e[18]=t("p",{class:"text-gray-500 mb-4"},"开始添加您的第一个 WHMCS 网站",-1)),t("button",{onClick:e[1]||(e[1]=s=>r.openSiteModal()),class:"bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200"}," 添加第一个网站 ")])):(u(),g("div",Ia,[(u(!0),g(ne,null,de(r.siteList,s=>(u(),g("div",{key:s.site_id,class:"group bg-gradient-to-br from-white via-gray-50 to-gray-100 dark:from-gray-800 dark:via-gray-800 dark:to-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md hover:border-indigo-300 dark:hover:border-indigo-500 transition-all duration-300 transform hover:-translate-y-0.5 hover:scale-[1.01]"},[t("div",Oa,[t("div",Fa,[t("div",Ua,[t("div",Na,[e[19]||(e[19]=t("div",{class:"relative"},[t("div",{class:"w-2.5 h-2.5 bg-indigo-500 rounded-full animate-pulse"}),t("div",{class:"absolute inset-0 w-2.5 h-2.5 bg-indigo-400 rounded-full animate-ping opacity-75"})],-1)),t("h3",Da,x(s.site_id),1)]),t("a",{href:s.base_url,target:"_blank",class:"text-indigo-500 hover:text-indigo-600 dark:text-indigo-400 dark:hover:text-indigo-300 text-sm flex items-center space-x-1 group/link"},[t("span",Wa,x(r.formatUrl(s.base_url)),1),e[20]||(e[20]=t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3 w-3 transform group-hover/link:translate-x-0.5 transition-transform",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})],-1))],8,qa),t("div",Ga,[e[23]||(e[23]=t("svg",{class:"w-3 h-3 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1)),t("span",Ka,x(s.username),1),s.cookies_string?(u(),g("span",Qa,e[21]||(e[21]=[t("svg",{class:"w-2.5 h-2.5 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),$(" Cookies ")]))):(u(),g("span",Ja,e[22]||(e[22]=[t("svg",{class:"w-2.5 h-2.5 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})],-1),$(" 密码 ")])))])]),t("div",Ya,[t("button",{onClick:m=>r.testLogin(s.site_id),disabled:r.isLoading.testingLogin===s.site_id,class:"relative p-2 bg-gradient-to-r from-emerald-500 to-green-500 hover:from-emerald-600 hover:to-green-600 text-white rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 disabled:opacity-70 disabled:cursor-not-allowed",title:"测试登录"},[r.isLoading.testingLogin===s.site_id?(u(),g("div",Za)):M("",!0),r.isLoading.testingLogin===s.site_id?(u(),g("svg",en,e[24]||(e[24]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):(u(),g("svg",tn,e[25]||(e[25]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))),r.isLoading.testingLogin?M("",!0):(u(),g("div",rn))],8,Xa),t("button",{onClick:m=>r.openSiteModal(s.site_id,s),class:"p-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-indigo-100 dark:hover:bg-indigo-800 hover:text-indigo-600 dark:hover:text-indigo-400 rounded-lg shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-200",title:"编辑网站"},e[26]||(e[26]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,on),t("button",{onClick:m=>r.deleteSite(s.site_id),class:"p-2 bg-gray-100 dark:bg-gray-700 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-600 rounded-lg shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-200",title:"删除网站"},e[27]||(e[27]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,sn)])])]),t("div",an,[t("div",nn,[t("div",ln,[t("div",dn,x(s.product_count||0),1),e[28]||(e[28]=t("div",{class:"text-xs text-gray-600 dark:text-gray-400 font-medium"},"总产品",-1))]),t("div",un,[t("div",cn,x(r.getEnabledProductsCount(s.site_id)),1),e[29]||(e[29]=t("div",{class:"text-xs text-gray-600 dark:text-gray-400 font-medium"},"启用",-1))]),t("div",gn,[t("div",mn,x(r.getAutoOrderProductsCount(s.site_id)),1),e[30]||(e[30]=t("div",{class:"text-xs text-gray-600 dark:text-gray-400 font-medium"},"自动",-1))])]),q(n,{to:{name:"products",params:{siteId:s.site_id}},class:"w-full flex items-center justify-center p-2.5 bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 hover:from-indigo-100 hover:to-blue-100 dark:hover:from-indigo-900/30 dark:hover:to-blue-900/30 rounded border border-indigo-200/50 dark:border-indigo-700/50 hover:border-indigo-300 dark:hover:border-indigo-500 group/link transition-all duration-300 transform hover:scale-[1.02]"},{default:K(()=>e[31]||(e[31]=[t("div",{class:"flex items-center space-x-2"},[t("div",{class:"p-1.5 bg-indigo-100 dark:bg-indigo-800/50 rounded group-hover/link:bg-indigo-200 dark:group-hover/link:bg-indigo-700/50 transition-colors"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 text-indigo-600 dark:text-indigo-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})])]),t("span",{class:"text-gray-800 dark:text-gray-200 group-hover/link:text-indigo-700 dark:group-hover/link:text-indigo-300 font-medium text-sm"},"管理产品"),t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 text-gray-400 group-hover/link:text-indigo-500 transform group-hover/link:translate-x-0.5 transition-all duration-200",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})])],-1)])),_:2,__:[31]},1032,["to"])])]))),128))])),r.showSiteModal?(u(),g("div",fn,[t("div",vn,[t("div",pn,[t("h3",bn,x(r.editingSiteId?"编辑网站":"添加网站"),1),t("button",{onClick:e[2]||(e[2]=(...s)=>r.closeSiteModal&&r.closeSiteModal(...s)),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[32]||(e[32]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("form",{onSubmit:e[12]||(e[12]=ve((...s)=>r.saveSite&&r.saveSite(...s),["prevent"])),class:"p-6 space-y-6"},[t("div",xn,[e[36]||(e[36]=t("div",{class:"flex items-center space-x-2 mb-4"},[t("div",{class:"w-1 h-6 bg-indigo-500 rounded-full"}),t("h4",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"基本信息")],-1)),q(i,{modelValue:r.siteForm.siteId,"onUpdate:modelValue":e[3]||(e[3]=s=>r.siteForm.siteId=s),label:"网站ID",disabled:!!r.editingSiteId,required:"",placeholder:"例如: site1","help-text":"用于在系统中标识此网站的唯一ID"},{prefix:K(()=>e[33]||(e[33]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)])),_:1},8,["modelValue","disabled"]),q(i,{modelValue:r.siteForm.base_url,"onUpdate:modelValue":e[4]||(e[4]=s=>r.siteForm.base_url=s),label:"网站URL",type:"url",required:"",placeholder:"https://example.com","help-text":"WHMCS网站的完整URL地址"},{prefix:K(()=>e[34]||(e[34]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"})],-1)])),_:1},8,["modelValue"]),q(i,{modelValue:r.siteForm.hostname_base,"onUpdate:modelValue":e[5]||(e[5]=s=>r.siteForm.hostname_base=s),label:"主机名前缀",required:"",placeholder:"例如: vps","help-text":"用于生成主机名的前缀，如 vps001、vps002"},{prefix:K(()=>e[35]||(e[35]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h6a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h6a2 2 0 002-2v-4a2 2 0 00-2-2m8-8V6a2 2 0 012-2h4a2 2 0 012 2v2a2 2 0 01-2 2h-4a2 2 0 01-2-2z"})],-1)])),_:1},8,["modelValue"])]),t("div",hn,[e[40]||(e[40]=t("div",{class:"flex items-center space-x-2 mb-4"},[t("div",{class:"w-1 h-6 bg-green-500 rounded-full"}),t("h4",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"认证信息")],-1)),q(i,{modelValue:r.siteForm.username,"onUpdate:modelValue":e[6]||(e[6]=s=>r.siteForm.username=s),label:"用户名",required:"",placeholder:"WHMCS登录用户名","help-text":"用于登录WHMCS的账户用户名"},{prefix:K(()=>e[37]||(e[37]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1)])),_:1},8,["modelValue"]),q(i,{modelValue:r.siteForm.password,"onUpdate:modelValue":e[7]||(e[7]=s=>r.siteForm.password=s),label:"密码",type:"password",required:!r.editingSiteId,placeholder:r.editingSiteId?"留空保持不变":"请输入密码","help-text":r.editingSiteId?"如需修改密码请输入新密码，否则留空":"用于登录WHMCS的账户密码"},{prefix:K(()=>e[38]||(e[38]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})],-1)])),_:1},8,["modelValue","required","placeholder","help-text"]),q(i,{modelValue:r.siteForm.rootpw,"onUpdate:modelValue":e[8]||(e[8]=s=>r.siteForm.rootpw=s),label:"Root密码",type:"password",required:!r.editingSiteId,placeholder:r.editingSiteId?"留空保持不变":"请输入Root密码","help-text":r.editingSiteId?"如需修改Root密码请输入新密码，否则留空":"用于VPS的Root密码"},{prefix:K(()=>e[39]||(e[39]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"})],-1)])),_:1},8,["modelValue","required","placeholder","help-text"])]),t("div",yn,[e[44]||(e[44]=t("div",{class:"flex items-center space-x-2 mb-4"},[t("div",{class:"w-1 h-6 bg-purple-500 rounded-full"}),t("h4",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"配置选项")],-1)),q(p,{modelValue:r.siteForm.payment_method,"onUpdate:modelValue":e[9]||(e[9]=s=>r.siteForm.payment_method=s),label:"支付方式",options:r.paymentMethods,placeholder:"输入或选择支付网关","help-text":"可以从推荐列表中选择，或输入自定义的支付网关名称"},{prefix:K(()=>e[41]||(e[41]=[t("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})],-1)])),_:1},8,["modelValue","options"]),t("div",kn,[e[42]||(e[42]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},[t("span",{class:"flex items-center"},[t("svg",{class:"w-4 h-4 mr-2 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})]),$(" Cookies字符串 (可选) ")])],-1)),F(t("textarea",{"onUpdate:modelValue":e[10]||(e[10]=s=>r.siteForm.cookies_string=s),rows:"3",class:"w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400",placeholder:"如果提供，将使用Cookies进行认证，否则使用用户名密码"},null,512),[[X,r.siteForm.cookies_string]]),e[43]||(e[43]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400"},"可选择直接使用Cookies进行认证，跳过登录步骤",-1))])]),t("div",wn,[t("button",{type:"button",onClick:e[11]||(e[11]=(...s)=>r.closeSiteModal&&r.closeSiteModal(...s)),class:"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-lg transition-colors"}," 取消 "),t("button",{type:"submit",disabled:r.isLoading.savingSite,class:"px-4 py-2 bg-indigo-500 hover:bg-indigo-600 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center space-x-2"},[r.isLoading.savingSite?(u(),g("svg",Cn,e[45]||(e[45]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):M("",!0),t("span",null,x(r.editingSiteId?"更新":"添加"),1)],8,_n)])],32)])])):M("",!0)])}const Sn=ue(za,[["render",Mn],["__scopeId","data-v-15d4b3ed"]]),jn={name:"Products",components:{FormField:ar,FormSelect:nr},props:{siteId:{type:String,required:!0}},setup(o){const e=sr(),a=_e(),r=A(()=>o.siteId||e.params.siteId),l=A(()=>{const H={};return a.products.forEach(V=>{H[V.product_id]=V}),H}),d=A(()=>a.isLoading.products),n=O(!1),i=O(!1),p=O(null),s=ie({productId:"",name:"",productType:"traditional",url:"",promo_code:"",billing_cycle:"monthly",naturalLanguage:"",keywords:"",minPrice:null,maxPrice:null,minCpuCores:null,minRamGb:null,minStorageGb:null,preferredLocations:"",excludedLocations:"",enabled:!0,auto_order_enabled:!0,max_order_retries:3}),m=[{value:"monthly",label:"月付"},{value:"quarterly",label:"季付"},{value:"semiannually",label:"半年付"},{value:"annually",label:"年付"},{value:"biennially",label:"两年付"},{value:"triennially",label:"三年付"}],c=A(()=>Object.entries(l.value).map(([H,V])=>({product_id:H,...V}))),b=A(()=>c.value.filter(H=>H.enabled).length),h=A(()=>c.value.filter(H=>H.auto_order_enabled).length),B=A(()=>c.value.filter(H=>H.status).length),C=(H=null,V=null)=>{var z,S,f,_,W,ae,I,Q,ee,te,Ce,Oe;p.value=H,V?Object.assign(s,{productId:H,name:V.name,productType:V.product_type||"traditional",url:V.url||"",promo_code:V.promo_code||"",billing_cycle:V.billing_cycle||"monthly",naturalLanguage:((z=V.let_match_criteria)==null?void 0:z.natural_language)||"",keywords:((f=(S=V.let_match_criteria)==null?void 0:S.keywords)==null?void 0:f.join(", "))||"",minPrice:((_=V.let_match_criteria)==null?void 0:_.min_price)||null,maxPrice:((W=V.let_match_criteria)==null?void 0:W.max_price)||null,minCpuCores:((ae=V.let_match_criteria)==null?void 0:ae.min_cpu_cores)||null,minRamGb:((I=V.let_match_criteria)==null?void 0:I.min_ram_gb)||null,minStorageGb:((Q=V.let_match_criteria)==null?void 0:Q.min_storage_gb)||null,preferredLocations:((te=(ee=V.let_match_criteria)==null?void 0:ee.preferred_locations)==null?void 0:te.join(", "))||"",excludedLocations:((Oe=(Ce=V.let_match_criteria)==null?void 0:Ce.excluded_locations)==null?void 0:Oe.join(", "))||"",enabled:V.enabled,auto_order_enabled:V.auto_order_enabled,max_order_retries:V.max_order_retries||3}):Object.assign(s,{productId:"",name:"",productType:"traditional",url:"",promo_code:"",billing_cycle:"monthly",naturalLanguage:"",keywords:"",minPrice:null,maxPrice:null,minCpuCores:null,minRamGb:null,minStorageGb:null,preferredLocations:"",excludedLocations:"",enabled:!0,auto_order_enabled:!0,max_order_retries:3}),i.value=!0},v=()=>{i.value=!1,p.value=null},k=async()=>{n.value=!0;try{const H={name:s.name,product_type:s.productType,enabled:s.enabled,auto_order_enabled:s.auto_order_enabled,max_order_retries:s.max_order_retries};s.productType==="traditional"?(H.url=s.url,H.promo_code=s.promo_code,H.billing_cycle=s.billing_cycle):s.productType==="let_smart"&&(H.let_match_criteria={natural_language:s.naturalLanguage||"",keywords:s.keywords?s.keywords.split(",").map(z=>z.trim()).filter(z=>z):[],min_price:s.minPrice||null,max_price:s.maxPrice||null,min_cpu_cores:s.minCpuCores||null,min_ram_gb:s.minRamGb||null,min_storage_gb:s.minStorageGb||null,preferred_locations:s.preferredLocations?s.preferredLocations.split(",").map(z=>z.trim()).filter(z=>z):[],excluded_locations:s.excludedLocations?s.excludedLocations.split(",").map(z=>z.trim()).filter(z=>z):[]});const V=await re.post(`/api/sites/${r.value}/products/${s.productId}`,H);R("success",V.message),v(),await a.getProducts(r.value)}catch(H){console.error("保存产品失败:",H),R("error","保存产品失败")}finally{n.value=!1}},w=async(H,V)=>{try{await a.toggleProduct(r.value,H,V)&&R("success","产品状态更新成功")}catch(z){console.error("切换产品状态失败:",z),R("error","操作失败")}},E=async H=>{try{await a.toggleAutoOrder(r.value,H)&&R("success","自动下单状态更新成功")}catch(V){console.error("切换自动下单状态失败:",V),R("error","操作失败")}},N=async H=>{try{await a.resetFailures(r.value,H)&&R("success","失败次数重置成功")}catch(V){console.error("重置失败次数失败:",V),R("error","操作失败")}},Z=async H=>{if(await gt("确认删除产品",`确定要删除产品 "${H}" 吗？此操作不可撤销。`))try{await a.deleteProduct(r.value,H)?R("success","产品删除成功"):R("error","删除产品失败")}catch(z){console.error("删除产品失败:",z),R("error","删除产品失败")}};return Le(r,(H,V)=>{H&&H!==V&&a.getProducts(H).catch(console.error)},{immediate:!1}),we(async()=>{r.value&&(a.currentSiteId!==r.value||!a.products||a.products.length===0)&&a.getProducts(r.value).catch(console.error)}),{siteId:r,products:l,productList:c,isLoading:d,isLoadingSave:n,showProductModal:i,editingProductId:p,productForm:s,enabledProducts:b,autoOrderProducts:h,inStockProducts:B,openProductModal:C,closeProductModal:v,saveProduct:k,toggleProductEnabled:w,toggleAutoOrder:E,resetFailures:N,confirmDeleteProduct:Z,formatTime:Po,getBillingCycleText:Ro,billingCycleOptions:m}}},Ln={class:"space-y-6"},Vn={class:"flex items-center justify-between"},Bn={class:"flex items-center space-x-4"},En={class:"text-2xl font-bold text-gray-900 dark:text-white"},Pn={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Rn={class:"group bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-300 transform hover:-translate-y-1"},zn={class:"flex items-center"},Tn={class:"ml-4"},$n={class:"text-2xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"},An={class:"group bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-green-300 dark:hover:border-green-500 transition-all duration-300 transform hover:-translate-y-1"},Hn={class:"flex items-center"},In={class:"ml-4"},On={class:"text-2xl font-bold text-gray-900 dark:text-white group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors"},Fn={class:"group bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-orange-300 dark:hover:border-orange-500 transition-all duration-300 transform hover:-translate-y-1"},Un={class:"flex items-center"},Nn={class:"ml-4"},Dn={class:"text-2xl font-bold text-gray-900 dark:text-white group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors"},qn={class:"group bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-emerald-300 dark:hover:border-emerald-500 transition-all duration-300 transform hover:-translate-y-1"},Wn={class:"flex items-center"},Gn={class:"ml-4"},Kn={class:"text-2xl font-bold text-gray-900 dark:text-white group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors"},Qn={key:0,class:"text-center py-12"},Jn={key:1,class:"text-center py-12 bg-white dark:bg-gray-800 rounded-xl shadow-sm"},Yn={key:2,class:"space-y-6"},Xn={class:"relative z-10"},Zn={class:"flex items-start justify-between mb-4"},el={class:"flex-1 space-y-3"},tl={class:"flex items-center justify-between"},rl={class:"flex items-center space-x-3"},ol={class:"text-lg font-bold text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-300"},sl={class:"flex items-center gap-2"},al={key:0,class:"flex items-center px-3 py-1 rounded-lg text-xs font-semibold border bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-700/30 transition-all duration-200"},nl={class:"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400"},ll={key:0,class:"flex items-center space-x-2"},il={class:"truncate"},dl={key:0,class:"flex items-center space-x-1"},ul={class:"text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded"},cl={key:0},gl={key:1,class:"flex items-center space-x-2"},ml=["href"],fl={class:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400"},vl={class:"flex items-center space-x-4"},pl={key:0,class:"flex items-center space-x-4"},bl={key:0,class:"flex items-center"},xl={key:1,class:"flex items-center"},hl={key:2,class:"flex items-center"},yl={key:3,class:"text-purple-600 dark:text-purple-400 font-medium"},kl={key:1,class:"flex items-center space-x-4"},wl={key:2,class:"text-yellow-600 dark:text-yellow-400 font-medium"},_l={key:0,class:"flex items-center space-x-1"},Cl={class:"text-xs"},Ml={class:"flex items-center justify-between pt-4 border-t border-gray-200/50 dark:border-gray-700/50"},Sl={class:"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400"},jl={key:0},Ll={key:1},Vl={key:2,class:"text-green-600 dark:text-green-400"},Bl={class:"flex items-center space-x-6"},El={class:"flex flex-col items-center space-y-1"},Pl={class:"relative"},Rl=["onClick"],zl=["d"],Tl={key:0,class:"absolute inset-0 rounded-full border border-green-400 animate-ping opacity-20"},$l={class:"flex flex-col items-center space-y-1"},Al={class:"relative"},Hl=["onClick"],Il={key:0,class:"absolute inset-0 animate-spin-slow",style:{"clip-path":"polygon(50% 0%, 93.3% 25%, 93.3% 75%, 50% 100%, 6.7% 75%, 6.7% 25%)"}},Ol={class:"text-xs font-bold"},Fl={class:"flex items-center space-x-2"},Ul=["onClick"],Nl=["onClick"],Dl=["onClick"],ql={key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},Wl={class:"bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"},Gl={class:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700"},Kl={class:"text-xl font-semibold text-gray-900 dark:text-white"},Ql={class:"space-y-4"},Jl={class:"grid grid-cols-2 gap-4"},Yl={key:0,class:"absolute top-2 right-2 w-5 h-5 bg-indigo-500 rounded-full flex items-center justify-center"},Xl={key:0,class:"absolute top-2 right-2 w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center"},Zl={class:"space-y-4"},ei={key:0,class:"space-y-4"},ti={key:1,class:"space-y-4"},ri={class:"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700"},oi={class:"mb-4"},si={class:"border-t border-purple-200 dark:border-purple-700 pt-4"},ai={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ni={class:"space-y-2"},li={class:"grid grid-cols-2 gap-2"},ii={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4"},di={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4"},ui={class:"space-y-4"},ci={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},gi={class:"space-y-4"},mi={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},fi={class:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"},vi={class:"relative inline-flex items-center cursor-pointer"},pi={class:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"},bi={class:"relative inline-flex items-center cursor-pointer"},xi={class:"flex justify-end space-x-3 pt-4"},hi=["disabled"],yi={key:0,class:"animate-spin h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"};function ki(o,e,a,r,l,d){const n=ye("router-link"),i=ye("FormField"),p=ye("FormSelect");return u(),g("div",Ln,[t("div",Vn,[t("div",Bn,[q(n,{to:"/sites",class:"flex items-center px-3 py-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200"},{default:K(()=>e[24]||(e[24]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1),t("span",null,"返回网站列表",-1)])),_:1,__:[24]}),e[26]||(e[26]=t("div",{class:"h-6 w-px bg-gray-300 dark:bg-gray-600"},null,-1)),t("div",null,[t("h2",En,x(r.siteId)+" - 产品管理",1),e[25]||(e[25]=t("p",{class:"text-sm text-gray-600 dark:text-gray-400 mt-1"}," 管理网站的产品配置和监控设置 ",-1))])]),t("button",{onClick:e[0]||(e[0]=s=>r.openProductModal()),class:"flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"},e[27]||(e[27]=[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),$(" 添加产品 ")]))]),t("div",Pn,[t("div",Rn,[t("div",zn,[e[29]||(e[29]=t("div",{class:"p-3 rounded-xl bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 group-hover:from-blue-200 group-hover:to-indigo-200 dark:group-hover:from-blue-800/40 dark:group-hover:to-indigo-800/40 transition-all duration-300"},[t("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})])],-1)),t("div",Tn,[e[28]||(e[28]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors"},"总产品数",-1)),t("p",$n,x(r.productList.length),1)])])]),t("div",An,[t("div",Hn,[e[31]||(e[31]=t("div",{class:"p-3 rounded-xl bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 group-hover:from-green-200 group-hover:to-emerald-200 dark:group-hover:from-green-800/40 dark:group-hover:to-emerald-800/40 transition-all duration-300"},[t("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400 group-hover:scale-110 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1)),t("div",In,[e[30]||(e[30]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors"},"启用产品",-1)),t("p",On,x(r.enabledProducts),1)])])]),t("div",Fn,[t("div",Un,[e[33]||(e[33]=t("div",{class:"p-3 rounded-xl bg-gradient-to-br from-orange-100 to-amber-100 dark:from-orange-900/30 dark:to-amber-900/30 group-hover:from-orange-200 group-hover:to-amber-200 dark:group-hover:from-orange-800/40 dark:group-hover:to-amber-800/40 transition-all duration-300"},[t("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400 group-hover:scale-110 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),t("div",Nn,[e[32]||(e[32]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors"},"自动下单",-1)),t("p",Dn,x(r.autoOrderProducts),1)])])]),t("div",qn,[t("div",Wn,[e[35]||(e[35]=t("div",{class:"p-3 rounded-xl bg-gradient-to-br from-emerald-100 to-teal-100 dark:from-emerald-900/30 dark:to-teal-900/30 group-hover:from-emerald-200 group-hover:to-teal-200 dark:group-hover:from-emerald-800/40 dark:group-hover:to-teal-800/40 transition-all duration-300"},[t("svg",{class:"w-6 h-6 text-emerald-600 dark:text-emerald-400 group-hover:scale-110 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",Gn,[e[34]||(e[34]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors"},"有货产品",-1)),t("p",Kn,x(r.inStockProducts),1)])])])]),r.isLoading?(u(),g("div",Qn,e[36]||(e[36]=[t("div",{class:"spinner border-4 border-indigo-200 border-t-indigo-600 rounded-full w-8 h-8 mx-auto"},null,-1),t("p",{class:"mt-4 text-gray-500 dark:text-gray-400"},"加载产品数据中...",-1)]))):r.productList.length===0?(u(),g("div",Jn,[e[37]||(e[37]=t("svg",{class:"w-16 h-16 mx-auto text-gray-400 dark:text-gray-600 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7"})],-1)),e[38]||(e[38]=t("h3",{class:"text-xl font-medium text-gray-900 dark:text-white mb-2"},"暂无产品",-1)),e[39]||(e[39]=t("p",{class:"text-gray-500 dark:text-gray-400 mb-6"},"开始添加您的第一个产品进行监控",-1)),t("button",{onClick:e[1]||(e[1]=s=>r.openProductModal()),class:"bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200"}," 添加第一个产品 ")])):(u(),g("div",Yn,[(u(!0),g(ne,null,de(r.productList,s=>{var m,c,b,h,B,C,v,k,w,E;return u(),g("div",{key:s.product_id,class:"group relative bg-gradient-to-br from-white via-gray-50/50 to-gray-100/30 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800 rounded-xl p-6 shadow-lg border border-gray-200/60 dark:border-gray-700/60 hover:shadow-xl hover:border-indigo-300/50 dark:hover:border-indigo-500/50 transition-all duration-300 transform hover:-translate-y-1 overflow-hidden"},[e[53]||(e[53]=t("div",{class:"absolute inset-0 bg-gradient-to-br from-transparent via-indigo-50/20 to-blue-50/10 dark:via-indigo-900/10 dark:to-blue-900/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"},null,-1)),t("div",Xn,[t("div",Zn,[t("div",el,[t("div",tl,[t("div",rl,[e[40]||(e[40]=t("div",{class:"relative"},[t("div",{class:"w-2.5 h-2.5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full animate-pulse-custom"}),t("div",{class:"absolute inset-0 w-2.5 h-2.5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full animate-ping opacity-20"})],-1)),t("h3",ol,x(s.name||s.product_id),1)]),t("div",sl,[t("div",{class:P(["flex items-center px-3 py-1 rounded-lg text-xs font-semibold border transition-all duration-200",s.enabled?"bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-700/30":"bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600/50"])},[t("div",{class:P(["w-1.5 h-1.5 rounded-full mr-1.5",s.enabled?"bg-green-500 animate-pulse":"bg-gray-400"])},null,2),$(" "+x(s.enabled?"监控中":"已停止"),1)],2),s.auto_order_enabled?(u(),g("div",al,e[41]||(e[41]=[t("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})],-1),$(" 自动 ")]))):M("",!0),t("div",{class:P(["flex items-center px-3 py-1 rounded-lg text-xs font-semibold border transition-all duration-200",s.status?"bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-700/30":"bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-700/30"])},[t("div",{class:P(["w-1.5 h-1.5 rounded-full mr-1.5",s.status?"bg-emerald-500":"bg-red-500"])},null,2),$(" "+x(s.status?"有货":"缺货"),1)],2)])]),t("div",nl,[s.product_type==="let_smart"?(u(),g("div",ll,[e[42]||(e[42]=t("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1)),e[43]||(e[43]=t("span",{class:"text-purple-600 dark:text-purple-400 font-medium"},"🧠 LET智能匹配",-1)),e[44]||(e[44]=t("span",{class:"text-gray-500"},"|",-1)),t("span",il,x(s.let_match_criteria.natural_language||"智能匹配产品"),1),(c=(m=s.let_match_criteria)==null?void 0:m.keywords)!=null&&c.length?(u(),g("div",dl,[t("span",ul,[$(x(s.let_match_criteria.keywords.slice(0,2).join(", "))+" ",1),s.let_match_criteria.keywords.length>2?(u(),g("span",cl,"...")):M("",!0)])])):M("",!0)])):(u(),g("div",gl,[e[45]||(e[45]=t("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"})],-1)),t("a",{href:s.url,target:"_blank",class:"flex-1 text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300 truncate font-medium transition-colors"},x(s.url),9,ml)]))]),t("div",fl,[t("div",vl,[s.product_type==="let_smart"?(u(),g("div",pl,[(b=s.let_match_criteria)!=null&&b.min_price||(h=s.let_match_criteria)!=null&&h.max_price?(u(),g("span",bl," 💰 $"+x(((B=s.let_match_criteria)==null?void 0:B.min_price)||"0")+" - $"+x(((C=s.let_match_criteria)==null?void 0:C.max_price)||"∞"),1)):M("",!0),(v=s.let_match_criteria)!=null&&v.min_cpu_cores?(u(),g("span",xl," 🔧 "+x((k=s.let_match_criteria)==null?void 0:k.min_cpu_cores)+"+ 核心 ",1)):M("",!0),(w=s.let_match_criteria)!=null&&w.min_ram_gb?(u(),g("span",hl," 💾 "+x((E=s.let_match_criteria)==null?void 0:E.min_ram_gb)+"+ GB ",1)):M("",!0),s.total_let_matches?(u(),g("span",yl," 🎯 匹配 "+x(s.total_let_matches)+" 次 ",1)):M("",!0)])):(u(),g("div",kl,[t("span",null,x(s.promo_code||"无优惠码"),1),t("span",null,x(r.getBillingCycleText(s.billing_cycle)),1)])),s.order_failure_count>0?(u(),g("span",wl," 失败 "+x(s.order_failure_count)+"/"+x(s.max_order_retries),1)):M("",!0)]),s.last_check?(u(),g("div",_l,[e[46]||(e[46]=t("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),t("span",Cl,x(r.formatTime(s.last_check,!0)),1)])):M("",!0)])])]),t("div",Ml,[t("div",Sl,[s.product_type==="let_smart"?(u(),g("span",jl," LET匹配: "+x(s.last_let_match_count||0),1)):(u(),g("span",Ll," 有货计数: "+x(s.in_stock_count),1)),s.successful_let_orders?(u(),g("span",Vl," 成功订单: "+x(s.successful_let_orders),1)):M("",!0)]),t("div",Bl,[t("div",El,[t("div",Pl,[t("div",{class:P(["relative w-14 h-14 rounded-full border-3 transition-all duration-500 cursor-pointer group",s.enabled?"border-green-400 bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 shadow-lg shadow-green-200/30 dark:shadow-green-900/20":"border-gray-300 dark:border-gray-600 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 shadow-md"]),onClick:N=>r.toggleProductEnabled(s.product_id,!s.enabled)},[t("div",{class:P(["absolute inset-1 rounded-full flex items-center justify-center transition-all duration-500 transform",s.enabled?"bg-gradient-to-br from-green-400 to-emerald-500 scale-100 rotate-0":"bg-gradient-to-br from-gray-400 to-gray-500 scale-75 rotate-180"])},[(u(),g("svg",{class:P(["w-5 h-5 transition-all duration-500",s.enabled?"text-white animate-pulse":"text-gray-200"]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:s.enabled?"M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z":"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L17 17M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,8,zl)],2))],2),t("div",{class:P(["absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full border border-white transition-all duration-300",s.enabled?"bg-green-500 animate-ping":"bg-red-500"])},null,2),s.enabled?(u(),g("div",Tl)):M("",!0)],10,Rl)]),t("span",{class:P(["text-xs font-medium",s.enabled?"text-green-600 dark:text-green-400":"text-gray-500"])},x(s.enabled?"监控":"停止"),3)]),t("div",$l,[t("div",Al,[t("div",{class:P(["relative w-14 h-14 cursor-pointer group transition-all duration-500 transform hover:scale-105",s.auto_order_enabled?"animate-pulse-slow":""]),onClick:N=>r.toggleAutoOrder(s.product_id,!s.auto_order_enabled)},[t("div",{class:P(["w-full h-full transition-all duration-500 transform shadow-lg",s.auto_order_enabled?"bg-gradient-to-br from-orange-400 via-red-500 to-pink-500 rotate-0 shadow-orange-300/30 dark:shadow-orange-900/20":"bg-gradient-to-br from-gray-300 to-gray-400 dark:from-gray-600 dark:to-gray-700 rotate-45 shadow-gray-300/30"]),style:{"clip-path":"polygon(50% 0%, 93.3% 25%, 93.3% 75%, 50% 100%, 6.7% 75%, 6.7% 25%)"}},null,2),t("div",{class:P(["absolute inset-2 rounded-full flex items-center justify-center transition-all duration-500",s.auto_order_enabled?"bg-white/20 backdrop-blur-sm":"bg-white/30"])},[(u(),g("svg",{class:P(["w-6 h-6 transition-all duration-500 transform",s.auto_order_enabled?"text-white animate-bounce":"text-gray-500 scale-75"]),fill:"currentColor",viewBox:"0 0 24 24"},e[47]||(e[47]=[t("path",{d:"M13 10V3L4 14h7v7l9-11h-7z"},null,-1)]),2))],2),s.auto_order_enabled?(u(),g("div",Il,e[48]||(e[48]=[t("div",{class:"w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent"},null,-1)]))):M("",!0),t("div",{class:P(["absolute -top-1 -right-1 w-4 h-4 rounded-full border border-white flex items-center justify-center transition-all duration-300",s.auto_order_enabled?"bg-yellow-400 text-orange-600 animate-pulse":"bg-gray-400 text-gray-600"])},[t("span",Ol,x(s.auto_order_enabled?"⚡":"○"),1)],2)],10,Hl)]),t("span",{class:P(["text-xs font-medium",s.auto_order_enabled?"text-orange-600 dark:text-orange-400":"text-gray-500"])},x(s.auto_order_enabled?"自动":"手动"),3)]),e[52]||(e[52]=t("div",{class:"h-12 w-px bg-gradient-to-b from-transparent via-gray-300 dark:via-gray-600 to-transparent"},null,-1)),t("div",Fl,[s.order_failure_count>0?(u(),g("button",{key:0,onClick:N=>r.resetFailures(s.product_id),class:"group inline-flex items-center px-3 py-2 text-xs font-medium text-yellow-700 bg-gradient-to-r from-yellow-100 to-amber-100 hover:from-yellow-200 hover:to-amber-200 dark:from-yellow-900/20 dark:to-amber-900/20 dark:text-yellow-400 dark:hover:from-yellow-900/30 dark:hover:to-amber-900/30 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md border border-yellow-200 dark:border-yellow-800",title:"重置失败次数"},e[49]||(e[49]=[t("svg",{class:"w-3 h-3 group-hover:rotate-180 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)]),8,Ul)):M("",!0),t("button",{onClick:N=>r.openProductModal(s.product_id,s),class:"group inline-flex items-center px-3 py-2 text-xs font-medium text-gray-700 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 dark:from-gray-700 dark:to-gray-600 dark:text-gray-300 dark:hover:from-gray-600 dark:hover:to-gray-500 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md border border-gray-200 dark:border-gray-600",title:"编辑产品"},e[50]||(e[50]=[t("svg",{class:"w-3 h-3 group-hover:rotate-12 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Nl),t("button",{onClick:N=>r.confirmDeleteProduct(s.product_id),class:"group inline-flex items-center px-3 py-2 text-xs font-medium text-red-700 bg-gradient-to-r from-red-100 to-rose-100 hover:from-red-200 hover:to-rose-200 dark:from-red-900/20 dark:to-rose-900/20 dark:text-red-400 dark:hover:from-red-900/30 dark:hover:to-rose-900/30 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md border border-red-200 dark:border-red-800",title:"删除产品"},e[51]||(e[51]=[t("svg",{class:"w-3 h-3 group-hover:rotate-12 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1v3M4 7h16"})],-1)]),8,Dl)])])])])])}),128))])),r.showProductModal?(u(),g("div",ql,[t("div",Wl,[t("div",Gl,[t("h3",Kl,x(r.editingProductId?"编辑产品":"添加产品"),1),t("button",{onClick:e[2]||(e[2]=(...s)=>r.closeProductModal&&r.closeProductModal(...s)),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[54]||(e[54]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("form",{onSubmit:e[23]||(e[23]=ve((...s)=>r.saveProduct&&r.saveProduct(...s),["prevent"])),class:"p-6 space-y-6"},[t("div",Ql,[e[59]||(e[59]=t("div",{class:"flex items-center space-x-2 mb-4"},[t("div",{class:"w-1 h-6 bg-blue-500 rounded-full"}),t("h4",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"产品类型")],-1)),t("div",Jl,[t("div",{class:P(["relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200",r.productForm.productType==="traditional"?"border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"]),onClick:e[3]||(e[3]=s=>r.productForm.productType="traditional")},[e[56]||(e[56]=Y('<div class="flex items-center space-x-3" data-v-ba0d8e7f><div class="p-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg" data-v-ba0d8e7f><svg class="w-6 h-6 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-ba0d8e7f><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" data-v-ba0d8e7f></path></svg></div><div data-v-ba0d8e7f><h5 class="font-medium text-gray-900 dark:text-white" data-v-ba0d8e7f>传统监控</h5><p class="text-sm text-gray-500 dark:text-gray-400" data-v-ba0d8e7f>监控WHMCS产品页面库存状态</p></div></div>',1)),r.productForm.productType==="traditional"?(u(),g("div",Yl,e[55]||(e[55]=[t("svg",{class:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):M("",!0)],2),t("div",{class:P(["relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200",r.productForm.productType==="let_smart"?"border-purple-500 bg-purple-50 dark:bg-purple-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"]),onClick:e[4]||(e[4]=s=>r.productForm.productType="let_smart")},[e[58]||(e[58]=Y('<div class="flex items-center space-x-3" data-v-ba0d8e7f><div class="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg" data-v-ba0d8e7f><svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-ba0d8e7f><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" data-v-ba0d8e7f></path></svg></div><div data-v-ba0d8e7f><h5 class="font-medium text-gray-900 dark:text-white" data-v-ba0d8e7f>🧠 LET智能匹配</h5><p class="text-sm text-gray-500 dark:text-gray-400" data-v-ba0d8e7f>AI分析LET帖子并自动匹配</p></div></div>',1)),r.productForm.productType==="let_smart"?(u(),g("div",Xl,e[57]||(e[57]=[t("svg",{class:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):M("",!0)],2)])]),t("div",Zl,[e[73]||(e[73]=t("div",{class:"flex items-center space-x-2 mb-4"},[t("div",{class:"w-1 h-6 bg-indigo-500 rounded-full"}),t("h4",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"基本信息")],-1)),q(i,{modelValue:r.productForm.productId,"onUpdate:modelValue":e[5]||(e[5]=s=>r.productForm.productId=s),label:"产品ID",disabled:!!r.editingProductId,required:"",placeholder:"例如: product1","help-text":"用于在系统中标识此产品的唯一ID"},{prefix:K(()=>e[60]||(e[60]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})],-1)])),_:1},8,["modelValue","disabled"]),q(i,{modelValue:r.productForm.name,"onUpdate:modelValue":e[6]||(e[6]=s=>r.productForm.name=s),label:"产品名称",required:"",placeholder:"产品显示名称","help-text":"在管理界面中显示的产品名称"},{prefix:K(()=>e[61]||(e[61]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})],-1)])),_:1},8,["modelValue"]),r.productForm.productType==="traditional"?(u(),g("div",ei,[q(i,{modelValue:r.productForm.url,"onUpdate:modelValue":e[7]||(e[7]=s=>r.productForm.url=s),label:"产品URL",type:"url",required:"",placeholder:"https://example.com/cart.php?a=add&pid=123","help-text":"WHMCS产品页面的完整URL地址"},{prefix:K(()=>e[62]||(e[62]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"})],-1)])),_:1},8,["modelValue"])])):M("",!0),r.productForm.productType==="let_smart"?(u(),g("div",ti,[t("div",ri,[e[72]||(e[72]=t("h5",{class:"font-medium text-purple-900 dark:text-purple-200 mb-3 flex items-center"},[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})]),$(" 智能匹配条件 ")],-1)),t("div",oi,[q(i,{modelValue:r.productForm.naturalLanguage,"onUpdate:modelValue":e[8]||(e[8]=s=>r.productForm.naturalLanguage=s),label:"🤖 自然语言需求描述",type:"textarea",rows:3,placeholder:"请用自然语言描述您的需求，例如：我需要一台便宜的美国VPS，最好有大硬盘，适合做存储...","help-text":"AI将主要根据此描述进行智能匹配，这是最重要的匹配条件"},{prefix:K(()=>e[63]||(e[63]=[t("svg",{class:"w-5 h-5 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1)])),_:1},8,["modelValue"])]),t("div",si,[e[71]||(e[71]=t("h6",{class:"text-sm font-medium text-purple-800 dark:text-purple-300 mb-3"},"📋 补充技术条件（可选）",-1)),t("div",ai,[q(i,{modelValue:r.productForm.keywords,"onUpdate:modelValue":e[9]||(e[9]=s=>r.productForm.keywords=s),label:"关键词",placeholder:"VPS, 独立服务器, 美国, 等等","help-text":"用逗号分隔多个关键词"},{prefix:K(()=>e[64]||(e[64]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})],-1)])),_:1},8,["modelValue"]),t("div",ni,[e[65]||(e[65]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"价格范围 (USD)",-1)),t("div",li,[q(i,{modelValue:r.productForm.minPrice,"onUpdate:modelValue":e[10]||(e[10]=s=>r.productForm.minPrice=s),modelModifiers:{number:!0},label:"",type:"number",min:"0",step:"0.01",placeholder:"最低价格","help-text":""},null,8,["modelValue"]),q(i,{modelValue:r.productForm.maxPrice,"onUpdate:modelValue":e[11]||(e[11]=s=>r.productForm.maxPrice=s),modelModifiers:{number:!0},label:"",type:"number",min:"0",step:"0.01",placeholder:"最高价格","help-text":""},null,8,["modelValue"])])])]),t("div",ii,[q(i,{modelValue:r.productForm.minCpuCores,"onUpdate:modelValue":e[12]||(e[12]=s=>r.productForm.minCpuCores=s),modelModifiers:{number:!0},label:"最少CPU核心数",type:"number",min:"1",placeholder:"如: 2","help-text":""},{prefix:K(()=>e[66]||(e[66]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"})],-1)])),_:1},8,["modelValue"]),q(i,{modelValue:r.productForm.minRamGb,"onUpdate:modelValue":e[13]||(e[13]=s=>r.productForm.minRamGb=s),modelModifiers:{number:!0},label:"最少内存 (GB)",type:"number",min:"1",placeholder:"如: 4","help-text":""},{prefix:K(()=>e[67]||(e[67]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1)])),_:1},8,["modelValue"]),q(i,{modelValue:r.productForm.minStorageGb,"onUpdate:modelValue":e[14]||(e[14]=s=>r.productForm.minStorageGb=s),modelModifiers:{number:!0},label:"最少存储 (GB)",type:"number",min:"1",placeholder:"如: 100","help-text":""},{prefix:K(()=>e[68]||(e[68]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"})],-1)])),_:1},8,["modelValue"])]),t("div",di,[q(i,{modelValue:r.productForm.preferredLocations,"onUpdate:modelValue":e[15]||(e[15]=s=>r.productForm.preferredLocations=s),label:"偏好地区",placeholder:"美国, 欧洲, 亚洲, 等等","help-text":"用逗号分隔多个地区"},{prefix:K(()=>e[69]||(e[69]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)])),_:1},8,["modelValue"]),q(i,{modelValue:r.productForm.excludedLocations,"onUpdate:modelValue":e[16]||(e[16]=s=>r.productForm.excludedLocations=s),label:"排除地区",placeholder:"中国, 俄罗斯, 等等","help-text":"用逗号分隔多个地区"},{prefix:K(()=>e[70]||(e[70]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"})],-1)])),_:1},8,["modelValue"])])])])])):M("",!0)]),t("div",ui,[e[77]||(e[77]=t("div",{class:"flex items-center space-x-2 mb-4"},[t("div",{class:"w-1 h-6 bg-green-500 rounded-full"}),t("h4",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"选项配置")],-1)),t("div",ci,[q(i,{modelValue:r.productForm.promo_code,"onUpdate:modelValue":e[17]||(e[17]=s=>r.productForm.promo_code=s),label:"优惠码",placeholder:"请输入优惠码（可选）","help-text":"可选的优惠码，在下单时自动应用"},{prefix:K(()=>e[74]||(e[74]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)])),_:1},8,["modelValue"]),q(p,{modelValue:r.productForm.billing_cycle,"onUpdate:modelValue":e[18]||(e[18]=s=>r.productForm.billing_cycle=s),label:"计费周期",options:r.billingCycleOptions,"help-text":"选择产品的计费周期"},{prefix:K(()=>e[75]||(e[75]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)])),_:1},8,["modelValue","options"])]),q(i,{modelValue:r.productForm.max_order_retries,"onUpdate:modelValue":e[19]||(e[19]=s=>r.productForm.max_order_retries=s),modelModifiers:{number:!0},label:"最大下单重试次数",type:"number",min:0,max:10,"help-text":"下单失败时的最大重试次数（建议 3-5 次）"},{prefix:K(()=>e[76]||(e[76]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)])),_:1},8,["modelValue"])]),t("div",gi,[e[82]||(e[82]=t("div",{class:"flex items-center space-x-2 mb-4"},[t("div",{class:"w-1 h-6 bg-purple-500 rounded-full"}),t("h4",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"功能开关")],-1)),t("div",mi,[t("div",fi,[e[79]||(e[79]=Y('<div class="flex items-center space-x-3" data-v-ba0d8e7f><div class="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg" data-v-ba0d8e7f><svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-ba0d8e7f><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" data-v-ba0d8e7f></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" data-v-ba0d8e7f></path></svg></div><div data-v-ba0d8e7f><h5 class="font-medium text-gray-900 dark:text-white" data-v-ba0d8e7f>启用监控</h5><p class="text-sm text-gray-500 dark:text-gray-400" data-v-ba0d8e7f>监控产品库存状态</p></div></div>',1)),t("label",vi,[F(t("input",{"onUpdate:modelValue":e[20]||(e[20]=s=>r.productForm.enabled=s),type:"checkbox",class:"sr-only peer"},null,512),[[pe,r.productForm.enabled]]),e[78]||(e[78]=t("div",{class:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"},null,-1))])]),t("div",pi,[e[81]||(e[81]=Y('<div class="flex items-center space-x-3" data-v-ba0d8e7f><div class="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg" data-v-ba0d8e7f><svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-ba0d8e7f><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" data-v-ba0d8e7f></path></svg></div><div data-v-ba0d8e7f><h5 class="font-medium text-gray-900 dark:text-white" data-v-ba0d8e7f>自动下单</h5><p class="text-sm text-gray-500 dark:text-gray-400" data-v-ba0d8e7f>有货时自动购买</p></div></div>',1)),t("label",bi,[F(t("input",{"onUpdate:modelValue":e[21]||(e[21]=s=>r.productForm.auto_order_enabled=s),type:"checkbox",class:"sr-only peer"},null,512),[[pe,r.productForm.auto_order_enabled]]),e[80]||(e[80]=t("div",{class:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"},null,-1))])])])]),t("div",xi,[t("button",{type:"button",onClick:e[22]||(e[22]=(...s)=>r.closeProductModal&&r.closeProductModal(...s)),class:"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-lg transition-colors"}," 取消 "),t("button",{type:"submit",disabled:r.isLoadingSave,class:"px-4 py-2 bg-indigo-500 hover:bg-indigo-600 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center space-x-2"},[r.isLoadingSave?(u(),g("svg",yi,e[83]||(e[83]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):M("",!0),t("span",null,x(r.editingProductId?"更新":"添加"),1)],8,hi)])],32)])])):M("",!0)])}const wi=ue(jn,[["render",ki],["__scopeId","data-v-ba0d8e7f"]]),_i={name:"Settings",setup(){const o=_e(),e=A(()=>o.config),a=A(()=>o.status),r=ie({monitor:!1,notification:!1,proxy:!1}),l=ie({frequency:30,min_in_stock_count:1,retry_delay:5,max_concurrent_checks:5}),d=ie({notice_type:"telegram",telegram_token:"",chat_id:"",wechat_key:"",custom_url:""}),n=ie({use_proxy:!1,proxy_host:""}),i=[{value:"telegram",label:"Telegram Bot"},{value:"wechat",label:"微信推送"},{value:"custom",label:"自定义URL"}],p=()=>{e.value&&e.value.monitor&&Object.assign(l,e.value.monitor),e.value&&e.value.notification&&Object.assign(d,e.value.notification),e.value&&e.value.proxy&&Object.assign(n,e.value.proxy)},s=async()=>{r.monitor=!0;try{await o.saveSettings("monitor",l)&&R("success","监控设置保存成功")}catch(N){console.error("保存监控设置失败:",N),R("error","保存监控设置失败")}finally{r.monitor=!1}},m=async()=>{r.notification=!0;try{await o.saveSettings("notification",d)&&R("success","通知设置保存成功")}catch(N){console.error("保存通知设置失败:",N),R("error","保存通知设置失败")}finally{r.notification=!1}},c=async()=>{r.proxy=!0;try{await o.saveSettings("proxy",n)&&R("success","代理设置保存成功")}catch(N){console.error("保存代理设置失败:",N),R("error","保存代理设置失败")}finally{r.proxy=!1}};return we(async()=>{(!e.value||Object.keys(e.value).length===0)&&o.getConfig().catch(console.error),(!a.value||Object.keys(a.value).length===0)&&o.getStatus().catch(console.error),setTimeout(p,100)}),A(()=>(p(),e.value)),{config:e,status:a,isLoading:r,monitorConfig:l,notificationConfig:d,proxyConfig:n,notificationTypes:i,saveMonitorSettings:s,saveNotificationSettings:m,saveProxySettings:c,validateFrequency:()=>l.frequency?l.frequency<1||l.frequency>3600?"检查频率必须在 1-3600 秒之间":l.frequency<10?"建议检查频率不小于 10 秒，避免过于频繁的请求":"":"",validateMinInStockCount:()=>!l.min_in_stock_count&&l.min_in_stock_count!==0?"":l.min_in_stock_count<0||l.min_in_stock_count>10?"连续有货次数阈值必须在 0-10 之间":"",validateRetryDelay:()=>l.retry_delay&&(l.retry_delay<1||l.retry_delay>300)?"重试延迟必须在 1-300 秒之间":"",validateMaxConcurrentChecks:()=>l.max_concurrent_checks&&(l.max_concurrent_checks<1||l.max_concurrent_checks>20)?"最大并发检查数必须在 1-20 之间":"",validateTelegramToken:()=>d.notice_type!=="telegram"?"":d.telegram_token?/^\d+:[A-Za-z0-9_-]+$/.test(d.telegram_token)?"":"Telegram Bot Token 格式不正确":"Telegram Bot Token 是必填项",validateChatId:()=>d.notice_type!=="telegram"?"":d.chat_id?/^-?\d+$/.test(d.chat_id)?"":"Chat ID 必须是数字":"Chat ID 是必填项",validateCustomUrl:()=>{if(d.notice_type!=="custom")return"";if(!d.custom_url)return"自定义URL是必填项";try{new URL(d.custom_url)}catch{return"请输入有效的URL地址"}return""},validateProxyHost:()=>{if(!n.use_proxy||!n.proxy_host)return"";try{new URL(n.proxy_host)}catch{return"请输入有效的代理地址"}return""}}}},Ci={class:"space-y-6"},Mi={class:"grid grid-cols-1 xl:grid-cols-2 gap-6"},Si={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"},ji={class:"pt-4"},Li=["disabled"],Vi={key:0,class:"animate-spin h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Bi={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Ei={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"},Pi={key:0,class:"space-y-4"},Ri={key:1},zi={key:2},Ti={class:"pt-4"},$i=["disabled"],Ai={key:0,class:"animate-spin h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Hi={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Ii={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"},Oi={class:"p-6 border-b border-gray-200 dark:border-gray-700"},Fi={class:"flex items-center justify-between"},Ui={class:"flex items-center"},Ni={key:0,class:"p-6"},Di={class:"flex items-end"},qi=["disabled"],Wi={key:0,class:"animate-spin h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Gi={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Ki={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"},Qi={class:"p-6"},Ji={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Yi={class:"text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},Xi={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Zi={class:"text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},e0={class:"text-2xl font-bold text-green-600 dark:text-green-400"},t0={class:"text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},r0={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"};function o0(o,e,a,r,l,d){return u(),g("div",Ci,[e[49]||(e[49]=t("div",{class:"flex items-center justify-between"},[t("div",null,[t("h2",{class:"text-3xl font-bold text-gray-900 dark:text-white"},"系统设置"),t("p",{class:"text-gray-600 dark:text-gray-400 mt-1"},"配置监控系统的各项参数")])],-1)),t("div",Mi,[t("div",Si,[e[25]||(e[25]=Y('<div class="p-6 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center space-x-3"><div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg></div><div><h3 class="text-lg font-semibold text-gray-900 dark:text-white">监控设置</h3><p class="text-sm text-gray-500 dark:text-gray-400">配置库存监控参数</p></div></div></div>',1)),t("form",{onSubmit:e[4]||(e[4]=ve((...n)=>r.saveMonitorSettings&&r.saveMonitorSettings(...n),["prevent"])),class:"p-6 space-y-6"},[t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-blue-500 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})]),$(" 检查频率 (秒) ")],-1)),F(t("input",{"onUpdate:modelValue":e[0]||(e[0]=n=>r.monitorConfig.frequency=n),type:"number",min:"1",max:"3600",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"建议: 30-300秒"},null,512),[[X,r.monitorConfig.frequency,void 0,{number:!0}]]),e[15]||(e[15]=t("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"},"监控系统检查库存的时间间隔",-1))]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-green-500 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})]),$(" 连续有货次数阈值 ")],-1)),F(t("input",{"onUpdate:modelValue":e[1]||(e[1]=n=>r.monitorConfig.min_in_stock_count=n),type:"number",min:"0",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"建议: 1-5次"},null,512),[[X,r.monitorConfig.min_in_stock_count,void 0,{number:!0}]]),e[17]||(e[17]=t("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"},"连续检测到有货多少次后触发通知",-1))]),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-yellow-500 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})]),$(" 重试延迟 (秒) ")],-1)),F(t("input",{"onUpdate:modelValue":e[2]||(e[2]=n=>r.monitorConfig.retry_delay=n),type:"number",min:"1",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"建议: 5-30秒"},null,512),[[X,r.monitorConfig.retry_delay,void 0,{number:!0}]]),e[19]||(e[19]=t("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"},"失败后重试的等待时间",-1))]),t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-purple-500 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),$(" 最大并发检查数 ")],-1)),F(t("input",{"onUpdate:modelValue":e[3]||(e[3]=n=>r.monitorConfig.max_concurrent_checks=n),type:"number",min:"1",max:"20",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"建议: 5-10个"},null,512),[[X,r.monitorConfig.max_concurrent_checks,void 0,{number:!0}]]),e[21]||(e[21]=t("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"},"同时进行库存检查的最大任务数量",-1))]),t("div",ji,[t("button",{type:"submit",disabled:r.isLoading.monitor,class:"w-full flex items-center justify-center px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:opacity-50 text-white rounded-lg transition-colors"},[r.isLoading.monitor?(u(),g("svg",Vi,e[22]||(e[22]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):(u(),g("svg",Bi,e[23]||(e[23]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"},null,-1)]))),e[24]||(e[24]=$(" 保存监控设置 "))],8,Li)])],32)]),t("div",Ei,[e[37]||(e[37]=Y('<div class="p-6 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center space-x-3"><div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.5 5v9a1.5 1.5 0 001.5 1.5h13a1.5 1.5 0 001.5-1.5V5a1.5 1.5 0 00-1.5-1.5H6A1.5 1.5 0 004.5 5z"></path></svg></div><div><h3 class="text-lg font-semibold text-gray-900 dark:text-white">通知设置</h3><p class="text-sm text-gray-500 dark:text-gray-400">配置消息推送参数</p></div></div></div>',1)),t("form",{onSubmit:e[10]||(e[10]=ve((...n)=>r.saveNotificationSettings&&r.saveNotificationSettings(...n),["prevent"])),class:"p-6 space-y-6"},[t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-purple-500 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2c0-1.1.9-2 2-2h6c1.1 0 2 .9 2 2v2M7 4h10M7 4v16c0 1.1.9 2 2 2h6c1.1 0 2 .9 2 2V4M7 4l3 3m0 0l3-3M10 7v10"})]),$(" 通知类型 ")],-1)),F(t("select",{"onUpdate:modelValue":e[5]||(e[5]=n=>r.notificationConfig.notice_type=n),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500"},e[26]||(e[26]=[t("option",{value:"telegram"},"Telegram Bot",-1),t("option",{value:"wechat"},"微信推送",-1),t("option",{value:"custom"},"自定义URL",-1)]),512),[[Ve,r.notificationConfig.notice_type]]),e[28]||(e[28]=t("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"},"选择通知推送方式",-1))]),r.notificationConfig.notice_type==="telegram"?(u(),g("div",Pi,[t("div",null,[e[29]||(e[29]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Telegram Bot Token ",-1)),F(t("input",{"onUpdate:modelValue":e[6]||(e[6]=n=>r.notificationConfig.telegram_token=n),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500",placeholder:"1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZ"},null,512),[[X,r.notificationConfig.telegram_token]])]),t("div",null,[e[30]||(e[30]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Chat ID ",-1)),F(t("input",{"onUpdate:modelValue":e[7]||(e[7]=n=>r.notificationConfig.chat_id=n),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500",placeholder:"-1234567890"},null,512),[[X,r.notificationConfig.chat_id]])])])):M("",!0),r.notificationConfig.notice_type==="wechat"?(u(),g("div",Ri,[e[31]||(e[31]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," 微信推送密钥 ",-1)),F(t("input",{"onUpdate:modelValue":e[8]||(e[8]=n=>r.notificationConfig.wechat_key=n),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500",placeholder:"微信推送服务的密钥"},null,512),[[X,r.notificationConfig.wechat_key]])])):M("",!0),r.notificationConfig.notice_type==="custom"?(u(),g("div",zi,[e[32]||(e[32]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-indigo-500 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"})]),$(" 自定义通知URL ")],-1)),F(t("input",{"onUpdate:modelValue":e[9]||(e[9]=n=>r.notificationConfig.custom_url=n),type:"url",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500",placeholder:"https://api.example.com/webhook"},null,512),[[X,r.notificationConfig.custom_url]]),e[33]||(e[33]=t("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"},"接收通知的URL地址",-1))])):M("",!0),t("div",Ti,[t("button",{type:"submit",disabled:r.isLoading.notification,class:"w-full flex items-center justify-center px-4 py-2 bg-purple-500 hover:bg-purple-600 disabled:opacity-50 text-white rounded-lg transition-colors"},[r.isLoading.notification?(u(),g("svg",Ai,e[34]||(e[34]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):(u(),g("svg",Hi,e[35]||(e[35]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"},null,-1)]))),e[36]||(e[36]=$(" 保存通知设置 "))],8,$i)])],32)])]),t("div",Ii,[t("div",Oi,[t("div",Fi,[e[39]||(e[39]=Y('<div class="flex items-center space-x-3"><div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path></svg></div><div><h3 class="text-lg font-semibold text-gray-900 dark:text-white">代理设置</h3><p class="text-sm text-gray-500 dark:text-gray-400">配置网络代理参数</p></div></div>',1)),t("label",Ui,[F(t("input",{"onUpdate:modelValue":e[11]||(e[11]=n=>r.proxyConfig.use_proxy=n),type:"checkbox",class:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"},null,512),[[pe,r.proxyConfig.use_proxy]]),e[38]||(e[38]=t("span",{class:"ml-2 text-sm font-medium text-gray-700 dark:text-gray-300"},"启用代理",-1))])])]),r.proxyConfig.use_proxy?(u(),g("div",Ni,[t("form",{onSubmit:e[13]||(e[13]=ve((...n)=>r.saveProxySettings&&r.saveProxySettings(...n),["prevent"])),class:"grid grid-cols-1 md:grid-cols-2 gap-6"},[t("div",null,[e[40]||(e[40]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-green-500 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h6a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h6a2 2 0 002-2v-4a2 2 0 00-2-2m8-8V6a2 2 0 012-2h4a2 2 0 012 2v2a2 2 0 01-2 2h-4a2 2 0 01-2-2z"})]),$(" 代理主机地址 ")],-1)),F(t("input",{"onUpdate:modelValue":e[12]||(e[12]=n=>r.proxyConfig.proxy_host=n),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-green-500 focus:border-green-500",placeholder:"例如: http://proxy.example.com:8080"},null,512),[[X,r.proxyConfig.proxy_host]]),e[41]||(e[41]=t("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"},"包含协议、主机名和端口的完整代理地址",-1))]),t("div",Di,[t("button",{type:"submit",disabled:r.isLoading.proxy,class:"w-full flex items-center justify-center px-4 py-2 bg-green-500 hover:bg-green-600 disabled:opacity-50 text-white rounded-lg transition-colors"},[r.isLoading.proxy?(u(),g("svg",Wi,e[42]||(e[42]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):(u(),g("svg",Gi,e[43]||(e[43]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"},null,-1)]))),e[44]||(e[44]=$(" 保存代理设置 "))],8,qi)])],32)])):M("",!0)]),t("div",Ki,[e[48]||(e[48]=Y('<div class="p-6 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center space-x-3"><div class="w-10 h-10 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div><div><h3 class="text-lg font-semibold text-gray-900 dark:text-white">系统信息</h3><p class="text-sm text-gray-500 dark:text-gray-400">当前系统运行状态</p></div></div></div>',1)),t("div",Qi,[t("div",Ji,[t("div",Yi,[t("div",Xi,x(r.status.total_sites||0),1),e[45]||(e[45]=t("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"网站数量",-1))]),t("div",Zi,[t("div",e0,x(r.status.total_products||0),1),e[46]||(e[46]=t("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"产品总数",-1))]),t("div",t0,[t("div",r0,x(r.status.is_running?"运行中":"已停止"),1),e[47]||(e[47]=t("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"监控状态",-1))])])])])])}const s0=ue(_i,[["render",o0]]),a0={name:"LetMonitor",setup(){const o=O({enabled:!1,running:!1,processed_posts:0,stored_posts:0,check_interval:300,proxy_url:null}),e=ie({check_interval:300,data_dir:"data/let_posts",proxy_url:""}),a=O([]),r=O(null),l=O(null),d=O(0),n=O(20),i=ie({status:!1,posts:!1,saveConfig:!1,testRss:!1,postDetail:!1,loadingMore:!1}),p=w=>{if(!w)return"N/A";try{return new Date(w).toLocaleString("zh-CN")}catch{return"N/A"}},s=async()=>{i.status=!0;try{const w=await re.get("/api/let/status");o.value={...o.value,...w},w.enabled&&(e.check_interval=w.check_interval||300,e.proxy_url=w.proxy_url||"")}catch(w){console.error("获取LET状态失败:",w),R("error","获取LET状态失败")}finally{i.status=!1}},m=async(w=!0)=>{i.posts=!0;try{const E=w?n.value:a.value.length+n.value,N=await re.get(`/api/let/posts?limit=${E}`);a.value=N.posts||[],d.value=N.total||0}catch(E){console.error("获取帖子列表失败:",E),R("error","获取帖子列表失败")}finally{i.posts=!1}},c=async()=>{i.saveConfig=!0;try{await re.post("/api/let/config",e),R("success","LET监控配置已保存"),await s()}catch(w){console.error("保存配置失败:",w),R("error","保存配置失败")}finally{i.saveConfig=!1}},b=async()=>{i.testRss=!0;try{const w=await re.post("/api/let/test-fetch");w.success?R("success",w.message):R("error",w.message)}catch(w){console.error("RSS测试失败:",w),R("error","RSS测试失败")}finally{i.testRss=!1}},h=async w=>{r.value=w,l.value=null,i.postDetail=!0;try{const E=w.post_id||w.file_name.replace(".json",""),N=await re.get(`/api/let/posts/${E}`);l.value=N}catch(E){console.error("获取帖子详情失败:",E),R("error","获取帖子详情失败"),r.value=null}finally{i.postDetail=!1}},B=async()=>{if(!(i.loadingMore||a.value.length>=d.value)){i.loadingMore=!0;try{const w=a.value.length,E=await re.get(`/api/let/posts?limit=${w+n.value}`);a.value=E.posts||[],d.value=E.total||0}catch(w){console.error("加载更多帖子失败:",w),R("error","加载更多帖子失败")}finally{i.loadingMore=!1}}},C=A(()=>a.value.length<d.value),v=A(()=>l.value?l.value.analysis?l.value.analysis:l.value.post_title||l.value.provider?l.value:null:null),k=A(()=>l.value&&l.value.rss_item?l.value.rss_item:null);return we(()=>{s(),m()}),{letStatus:o,letConfig:e,posts:a,selectedPost:r,postDetail:l,isLoading:i,formatDate:p,hasMorePosts:C,totalPosts:d,analysisData:v,rssData:k,loadLetStatus:s,loadPosts:m,loadMorePosts:B,saveLetConfig:c,testRssFetch:b,viewPostDetail:h}}},n0={class:"space-y-6"},l0={class:"relative overflow-hidden bg-gradient-to-r from-teal-600 via-cyan-600 to-blue-600 rounded-2xl p-6 text-white"},i0={class:"relative z-10 flex items-center justify-between"},d0=["disabled"],u0={class:"flex items-center"},c0={key:0,class:"animate-spin h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24"},g0={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},m0={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},f0={class:"group bg-gradient-to-br from-white via-teal-50/30 to-cyan-100/20 dark:from-gray-800 dark:via-teal-900/10 dark:to-cyan-900/20 rounded-xl shadow-lg border border-teal-200/30 dark:border-teal-700/30 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] hover:-translate-y-1 p-4 relative overflow-hidden"},v0={class:"relative z-10"},p0={class:"flex items-center justify-between"},b0={class:"mt-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5"},x0={class:"group bg-gradient-to-br from-white via-blue-50/30 to-indigo-100/20 dark:from-gray-800 dark:via-blue-900/10 dark:to-indigo-900/20 rounded-xl shadow-lg border border-blue-200/30 dark:border-blue-700/30 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] hover:-translate-y-1 p-4 relative overflow-hidden"},h0={class:"relative z-10"},y0={class:"flex items-center justify-between"},k0={class:"text-xl font-bold text-blue-500"},w0={class:"group bg-gradient-to-br from-white via-green-50/30 to-emerald-100/20 dark:from-gray-800 dark:via-green-900/10 dark:to-emerald-900/20 rounded-xl shadow-lg border border-green-200/30 dark:border-green-700/30 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] hover:-translate-y-1 p-4 relative overflow-hidden"},_0={class:"relative z-10"},C0={class:"flex items-center justify-between"},M0={class:"text-xl font-bold text-green-500"},S0={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},j0={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"},L0={class:"pt-3"},V0=["disabled"],B0={key:0,class:"animate-spin h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24"},E0={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},P0={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"},R0={class:"p-4"},z0={key:0,class:"flex items-center justify-between mb-3 p-2 bg-gradient-to-r from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 rounded-lg border border-teal-200 dark:border-teal-700"},T0={class:"flex items-center"},$0={class:"text-sm font-medium text-teal-700 dark:text-teal-300"},A0=["disabled"],H0={key:0,class:"animate-spin h-3 w-3 mr-1",fill:"none",viewBox:"0 0 24 24"},I0={key:1,class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},O0={key:1,class:"flex justify-center py-8"},F0={key:2,class:"text-center py-8 text-gray-500 dark:text-gray-400"},U0={key:3,class:"h-72 overflow-y-auto space-y-2 pr-2"},N0=["onClick"],D0={class:"relative z-10"},q0={class:"flex items-start justify-between"},W0={class:"flex-1 min-w-0"},G0={class:"font-medium text-sm text-gray-900 dark:text-white truncate group-hover:text-teal-600 dark:group-hover:text-teal-400 transition-colors"},K0={class:"flex items-center mt-1 space-x-2 flex-wrap"},Q0={class:"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"},J0={class:"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-teal-100 dark:bg-teal-900/50 text-teal-800 dark:text-teal-200"},Y0={class:"text-xs text-gray-500 dark:text-gray-400"},X0={key:0,class:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4"},Z0={class:"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden border border-gray-200 dark:border-gray-700"},ed={class:"p-6 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700"},td={class:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]"},rd={key:0},od={key:0},sd={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},ad={key:0,class:"bg-blue-50 dark:bg-blue-900/50 rounded-lg p-4 mb-4"},nd={class:"text-gray-700 dark:text-gray-300"},ld={class:"text-gray-700 dark:text-gray-300"},id={class:"text-gray-700 dark:text-gray-300"},dd={key:0,class:"mt-2"},ud=["href"],cd={key:1,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4"},gd={class:"text-gray-700 dark:text-gray-300"},md={key:0,class:"text-gray-700 dark:text-gray-300"},fd=["href"],vd={key:2,class:"bg-yellow-50 dark:bg-yellow-900/50 rounded-lg p-4 mb-4"},pd={key:0,class:"text-gray-700 dark:text-gray-300"},bd={class:"bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white px-2 py-1 rounded"},xd={key:1,class:"text-gray-700 dark:text-gray-300"},hd={key:3,class:"mb-4"},yd={class:"flex flex-wrap gap-2"},kd={key:4,class:"space-y-4"},wd={class:"font-medium text-gray-900 dark:text-white"},_d={class:"font-medium text-gray-900 dark:text-white"},Cd={class:"mt-2 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm"},Md={class:"text-gray-700 dark:text-gray-300"},Sd={class:"text-gray-700 dark:text-gray-300"},jd={class:"text-gray-700 dark:text-gray-300"},Ld={key:0,class:"text-gray-700 dark:text-gray-300"},Vd={class:"text-gray-700 dark:text-gray-300"},Bd={class:"text-gray-700 dark:text-gray-300"},Ed={key:0,class:"text-gray-700 dark:text-gray-300"},Pd={class:"bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white px-2 py-1 rounded"},Rd={class:"text-gray-700 dark:text-gray-300"},zd={key:0,class:"text-gray-500 dark:text-gray-400"},Td={key:1,class:"text-green-600 dark:text-green-400"},$d={key:2,class:"text-red-600 dark:text-red-400"},Ad={key:3,class:"text-yellow-600 dark:text-yellow-400"},Hd={key:0,class:"mt-3"},Id=["href"],Od={key:5,class:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 text-sm text-gray-500 dark:text-gray-400"},Fd={key:1,class:"bg-red-50 dark:bg-red-900/50 rounded-lg p-4"},Ud={class:"mt-2"},Nd={class:"mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-auto"},Dd={key:1,class:"flex justify-center py-8"};function qd(o,e,a,r,l,d){var n,i,p,s;return u(),g("div",n0,[t("div",l0,[e[10]||(e[10]=t("div",{class:"absolute inset-0 bg-black/20"},null,-1)),e[11]||(e[11]=t("div",{class:"absolute inset-0 bg-gradient-to-r from-blue-600/20 via-cyan-600/20 to-teal-600/20 backdrop-blur-sm"},null,-1)),e[12]||(e[12]=t("div",{class:"absolute top-0 right-0 -translate-y-6 translate-x-6 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"},null,-1)),t("div",i0,[e[9]||(e[9]=t("div",null,[t("h1",{class:"text-2xl font-bold mb-2 flex items-center"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-8 w-8 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})]),$(" LET RSS 监控 ")]),t("p",{class:"text-blue-100"},"监控 LowEndTalk 论坛的服务器优惠信息，智能解析和通知")],-1)),t("button",{onClick:e[0]||(e[0]=(...m)=>r.testRssFetch&&r.testRssFetch(...m)),disabled:r.isLoading.testRss,class:"relative px-6 py-3 bg-white/20 hover:bg-white/30 disabled:opacity-50 text-white rounded-xl shadow-lg backdrop-blur-sm transition-all duration-200 transform hover:scale-105 border border-white/30"},[t("div",u0,[r.isLoading.testRss?(u(),g("svg",c0,e[7]||(e[7]=[t("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4",class:"opacity-25"},null,-1),t("path",{fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",class:"opacity-75"},null,-1)]))):(u(),g("svg",g0,e[8]||(e[8]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))),$(" "+x(r.isLoading.testRss?"测试中...":"测试RSS获取"),1)])],8,d0)])]),t("div",m0,[t("div",f0,[e[15]||(e[15]=t("div",{class:"absolute inset-0"},[t("div",{class:"absolute top-0 right-0 -translate-y-6 translate-x-6 w-24 h-24 bg-teal-400/10 rounded-full blur-xl animate-pulse"})],-1)),t("div",v0,[t("div",p0,[t("div",null,[e[13]||(e[13]=t("h3",{class:"text-base font-semibold text-gray-900 dark:text-white mb-1"},"监控状态",-1)),t("p",{class:P(["text-xl font-bold",r.letStatus.enabled&&r.letStatus.running?"text-emerald-500":"text-red-500"])},x(r.letStatus.enabled?r.letStatus.running?"运行中":"已停止":"未启用"),3),t("div",b0,[t("div",{class:P(["h-1.5 rounded-full transition-all duration-500",r.letStatus.enabled&&r.letStatus.running?"bg-emerald-500 w-full":"bg-red-500 w-1/3"])},null,2)])]),t("div",{class:P(["p-3 rounded-xl transform group-hover:rotate-12 transition-transform duration-300",r.letStatus.enabled&&r.letStatus.running?"bg-emerald-100 dark:bg-emerald-900/50":"bg-red-100 dark:bg-red-900/50"])},[(u(),g("svg",{class:P(["w-8 h-8",r.letStatus.enabled&&r.letStatus.running?"text-emerald-600 dark:text-emerald-400":"text-red-600 dark:text-red-400"]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},e[14]||(e[14]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]),2))],2)])])]),t("div",x0,[e[19]||(e[19]=t("div",{class:"absolute inset-0"},[t("div",{class:"absolute top-0 right-0 -translate-y-6 translate-x-6 w-20 h-20 bg-blue-400/10 rounded-full blur-xl animate-pulse"})],-1)),t("div",h0,[t("div",y0,[t("div",null,[e[16]||(e[16]=t("h3",{class:"text-base font-semibold text-gray-900 dark:text-white mb-1"},"已处理帖子",-1)),t("p",k0,x(r.letStatus.processed_posts||0),1),e[17]||(e[17]=t("p",{class:"text-xs text-gray-600 dark:text-gray-400 mt-0.5"},"总计处理数量",-1))]),e[18]||(e[18]=t("div",{class:"p-3 bg-blue-100 dark:bg-blue-900/50 rounded-xl transform group-hover:rotate-12 transition-transform duration-300"},[t("svg",{class:"w-8 h-8 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1))])])]),t("div",w0,[e[23]||(e[23]=t("div",{class:"absolute inset-0"},[t("div",{class:"absolute top-0 right-0 -translate-y-6 translate-x-6 w-20 h-20 bg-green-400/10 rounded-full blur-xl animate-pulse"})],-1)),t("div",_0,[t("div",C0,[t("div",null,[e[20]||(e[20]=t("h3",{class:"text-base font-semibold text-gray-900 dark:text-white mb-1"},"存储帖子",-1)),t("p",M0,x(r.letStatus.stored_posts||0),1),e[21]||(e[21]=t("p",{class:"text-xs text-gray-600 dark:text-gray-400 mt-0.5"},"本地存储文件",-1))]),e[22]||(e[22]=t("div",{class:"p-3 bg-green-100 dark:bg-green-900/50 rounded-xl transform group-hover:rotate-12 transition-transform duration-300"},[t("svg",{class:"w-8 h-8 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z"})])],-1))])])])]),t("div",S0,[t("div",j0,[e[32]||(e[32]=Y('<div class="p-4 border-b border-gray-200 dark:border-gray-700" data-v-df2430ff><div class="flex items-center space-x-3" data-v-df2430ff><div class="w-8 h-8 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-lg flex items-center justify-center" data-v-df2430ff><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-df2430ff><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" data-v-df2430ff></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" data-v-df2430ff></path></svg></div><div data-v-df2430ff><h3 class="text-base font-semibold text-gray-900 dark:text-white" data-v-df2430ff>LET监控配置</h3><p class="text-xs text-gray-500 dark:text-gray-400" data-v-df2430ff>配置RSS监控参数</p></div></div></div>',1)),t("form",{onSubmit:e[4]||(e[4]=ve((...m)=>r.saveLetConfig&&r.saveLetConfig(...m),["prevent"])),class:"p-4 space-y-4"},[t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 检查间隔 (秒) ",-1)),F(t("input",{"onUpdate:modelValue":e[1]||(e[1]=m=>r.letConfig.check_interval=m),type:"number",min:"60",max:"7200",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"建议: 300-1800秒"},null,512),[[X,r.letConfig.check_interval,void 0,{number:!0}]]),e[25]||(e[25]=t("p",{class:"mt-0.5 text-xs text-gray-500 dark:text-gray-400"},"LET RSS检查的时间间隔",-1))]),t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 数据存储目录 ",-1)),F(t("input",{"onUpdate:modelValue":e[2]||(e[2]=m=>r.letConfig.data_dir=m),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"data/let_posts"},null,512),[[X,r.letConfig.data_dir]]),e[27]||(e[27]=t("p",{class:"mt-0.5 text-xs text-gray-500 dark:text-gray-400"},"LET帖子数据的存储目录",-1))]),t("div",null,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 代理URL (可选) ",-1)),F(t("input",{"onUpdate:modelValue":e[3]||(e[3]=m=>r.letConfig.proxy_url=m),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"http://127.0.0.1:7890"},null,512),[[X,r.letConfig.proxy_url]]),e[29]||(e[29]=t("p",{class:"mt-0.5 text-xs text-gray-500 dark:text-gray-400"},"如需使用代理访问RSS，请设置代理URL",-1))]),t("div",L0,[t("button",{type:"submit",disabled:r.isLoading.saveConfig,class:"w-full flex items-center justify-center px-4 py-2.5 bg-gradient-to-r from-teal-500 to-cyan-600 hover:from-teal-600 hover:to-cyan-700 disabled:opacity-50 text-white rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200"},[r.isLoading.saveConfig?(u(),g("svg",B0,e[30]||(e[30]=[t("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4",class:"opacity-25"},null,-1),t("path",{fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",class:"opacity-75"},null,-1)]))):(u(),g("svg",E0,e[31]||(e[31]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"},null,-1)]))),$(" "+x(r.isLoading.saveConfig?"保存中...":"保存配置"),1)],8,V0)])],32)]),t("div",P0,[e[40]||(e[40]=Y('<div class="p-4 border-b border-gray-200 dark:border-gray-700" data-v-df2430ff><div class="flex items-center space-x-3" data-v-df2430ff><div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center" data-v-df2430ff><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-df2430ff><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" data-v-df2430ff></path></svg></div><div data-v-df2430ff><h3 class="text-base font-semibold text-gray-900 dark:text-white" data-v-df2430ff>最近帖子</h3><p class="text-xs text-gray-500 dark:text-gray-400" data-v-df2430ff>已解析的优惠信息</p></div></div></div>',1)),t("div",R0,[!r.isLoading.posts&&r.posts.length>0?(u(),g("div",z0,[t("div",T0,[e[33]||(e[33]=t("svg",{class:"w-5 h-5 text-teal-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)),t("span",$0," 已显示 "+x(r.posts.length)+" / "+x(r.totalPosts)+" 个帖子 ",1)]),r.hasMorePosts?(u(),g("button",{key:0,onClick:e[5]||(e[5]=ve((...m)=>r.loadMorePosts&&r.loadMorePosts(...m),["prevent"])),disabled:r.isLoading.loadingMore,class:"flex items-center px-3 py-1 bg-teal-500 hover:bg-teal-600 disabled:opacity-50 text-white text-xs rounded-md transition-colors"},[r.isLoading.loadingMore?(u(),g("svg",H0,e[34]||(e[34]=[t("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4",class:"opacity-25"},null,-1),t("path",{fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",class:"opacity-75"},null,-1)]))):(u(),g("svg",I0,e[35]||(e[35]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]))),$(" "+x(r.isLoading.loadingMore?"加载中":"加载更多"),1)],8,A0)):M("",!0)])):M("",!0),r.isLoading.posts?(u(),g("div",O0,e[36]||(e[36]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"},null,-1)]))):r.posts.length===0?(u(),g("div",F0,e[37]||(e[37]=[t("svg",{class:"w-12 h-12 mx-auto mb-3 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),t("p",{class:"text-lg font-medium"},"暂无帖子数据",-1),t("p",{class:"text-sm mt-1"},"LET监控尚未发现任何帖子",-1)]))):(u(),g("div",U0,[(u(!0),g(ne,null,de(r.posts,m=>(u(),g("div",{key:m.file_name,class:"group relative bg-gradient-to-br from-white via-gray-50/50 to-gray-100/30 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800 rounded-lg p-3 shadow-sm border border-gray-200/60 dark:border-gray-700/60 hover:shadow-md hover:border-teal-300/50 dark:hover:border-teal-500/50 transition-all duration-200 cursor-pointer",onClick:c=>r.viewPostDetail(m)},[e[39]||(e[39]=t("div",{class:"absolute inset-0 bg-gradient-to-r from-teal-500/5 to-cyan-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"},null,-1)),t("div",D0,[t("div",q0,[t("div",W0,[t("h3",G0,x(m.post_title),1),t("div",K0,[t("span",Q0,x(m.provider_name||"未知供应商"),1),t("span",J0,x(m.product_count)+" 个产品 ",1),t("span",Y0,x(r.formatDate(m.created_time)),1)])]),e[38]||(e[38]=t("div",{class:"ml-3 flex-shrink-0"},[t("svg",{class:"w-4 h-4 text-gray-400 group-hover:text-teal-500 transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})])],-1))])])],8,N0))),128))]))])])]),r.selectedPost?(u(),g("div",X0,[t("div",Z0,[t("div",ed,[e[42]||(e[42]=t("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white flex items-center"},[t("svg",{class:"w-6 h-6 mr-2 text-teal-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})]),$(" 帖子详情 ")],-1)),t("button",{onClick:e[6]||(e[6]=m=>r.selectedPost=null),class:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"},e[41]||(e[41]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",td,[r.postDetail?(u(),g("div",rd,[r.analysisData?(u(),g("div",od,[t("h3",sd,x(r.analysisData.post_title),1),r.rssData?(u(),g("div",ad,[e[46]||(e[46]=t("h4",{class:"font-medium text-blue-800 dark:text-blue-200 mb-2"},"原始帖子信息",-1)),t("p",nd,[e[43]||(e[43]=t("strong",{class:"text-gray-900 dark:text-white"},"作者:",-1)),$(" "+x(r.rssData.creator),1)]),t("p",ld,[e[44]||(e[44]=t("strong",{class:"text-gray-900 dark:text-white"},"分类:",-1)),$(" "+x(r.rssData.category),1)]),t("p",id,[e[45]||(e[45]=t("strong",{class:"text-gray-900 dark:text-white"},"发布时间:",-1)),$(" "+x(r.formatDate(r.rssData.pub_date)),1)]),r.rssData.link?(u(),g("div",dd,[t("a",{href:r.rssData.link,target:"_blank",class:"text-blue-600 dark:text-blue-400 hover:underline"}," 查看原帖 → ",8,ud)])):M("",!0)])):M("",!0),r.analysisData.provider?(u(),g("div",cd,[e[49]||(e[49]=t("h4",{class:"font-medium text-gray-900 dark:text-white mb-2"},"供应商信息",-1)),t("p",gd,[e[47]||(e[47]=t("strong",{class:"text-gray-900 dark:text-white"},"名称:",-1)),$(" "+x(((n=r.analysisData.provider)==null?void 0:n.name)||"未知"),1)]),(i=r.analysisData.provider)!=null&&i.website?(u(),g("p",md,[e[48]||(e[48]=t("strong",{class:"text-gray-900 dark:text-white"},"网站:",-1)),t("a",{href:r.analysisData.provider.website,target:"_blank",class:"text-blue-600 dark:text-blue-400 hover:underline"},x(r.analysisData.provider.website),9,fd)])):M("",!0)])):M("",!0),r.analysisData.global_promo_code||r.analysisData.global_discount_info?(u(),g("div",vd,[e[53]||(e[53]=t("h4",{class:"font-medium text-yellow-800 dark:text-yellow-200 mb-2"},"全局优惠",-1)),r.analysisData.global_promo_code?(u(),g("p",pd,[e[50]||(e[50]=t("strong",{class:"text-gray-900 dark:text-white"},"优惠码:",-1)),e[51]||(e[51]=$()),t("code",bd,x(r.analysisData.global_promo_code),1)])):M("",!0),r.analysisData.global_discount_info?(u(),g("p",xd,[e[52]||(e[52]=t("strong",{class:"text-gray-900 dark:text-white"},"优惠信息:",-1)),$(" "+x(r.analysisData.global_discount_info),1)])):M("",!0)])):M("",!0),(p=r.analysisData.tags)!=null&&p.length?(u(),g("div",hd,[e[54]||(e[54]=t("h4",{class:"font-medium text-gray-900 dark:text-white mb-2"},"标签",-1)),t("div",yd,[(u(!0),g(ne,null,de(r.analysisData.tags,m=>(u(),g("span",{key:m,class:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200"},x(m),1))),128))])])):M("",!0),(s=r.analysisData.products)!=null&&s.length?(u(),g("div",kd,[t("h4",wd,"产品列表 ("+x(r.analysisData.products.length)+")",1),(u(!0),g(ne,null,de(r.analysisData.products,(m,c)=>{var b,h,B,C,v,k,w,E;return u(),g("div",{key:c,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},[t("h5",_d,x(m.title),1),t("div",Cd,[t("div",null,[t("p",Md,[e[55]||(e[55]=t("strong",{class:"text-gray-900 dark:text-white"},"CPU:",-1)),$(" "+x(((b=m.specs)==null?void 0:b.cpu)||"N/A"),1)]),t("p",Sd,[e[56]||(e[56]=t("strong",{class:"text-gray-900 dark:text-white"},"内存:",-1)),$(" "+x(((h=m.specs)==null?void 0:h.ram)||"N/A"),1)]),t("p",jd,[e[57]||(e[57]=t("strong",{class:"text-gray-900 dark:text-white"},"存储:",-1)),$(" "+x(((B=m.specs)==null?void 0:B.storage)||"N/A"),1)]),(C=m.specs)!=null&&C.bandwidth?(u(),g("p",Ld,[e[58]||(e[58]=t("strong",{class:"text-gray-900 dark:text-white"},"带宽:",-1)),$(" "+x(m.specs.bandwidth),1)])):M("",!0)]),t("div",null,[t("p",Vd,[e[59]||(e[59]=t("strong",{class:"text-gray-900 dark:text-white"},"价格:",-1)),$(" $"+x(((v=m.pricing)==null?void 0:v.price)||"N/A")+"/"+x(((k=m.pricing)==null?void 0:k.billing_cycle)||"month"),1)]),t("p",Bd,[e[60]||(e[60]=t("strong",{class:"text-gray-900 dark:text-white"},"位置:",-1)),$(" "+x(((w=m.location)==null?void 0:w.city)||"N/A")+", "+x(((E=m.location)==null?void 0:E.country)||"N/A"),1)]),m.promo_code?(u(),g("p",Ed,[e[61]||(e[61]=t("strong",{class:"text-gray-900 dark:text-white"},"优惠码:",-1)),e[62]||(e[62]=$()),t("code",Pd,x(m.promo_code),1)])):M("",!0),t("p",Rd,[e[63]||(e[63]=t("strong",{class:"text-gray-900 dark:text-white"},"可用性:",-1)),m.availability===null||m.availability===void 0?(u(),g("span",zd," 不确定 ")):m.availability&&(m.availability.toLowerCase().includes("stock")||m.availability.toLowerCase().includes("available")||m.availability.toLowerCase().includes("in stock"))?(u(),g("span",Td," 有货 ")):m.availability&&(m.availability.toLowerCase().includes("out")||m.availability.toLowerCase().includes("sold")||m.availability.toLowerCase().includes("unavailable"))?(u(),g("span",$d," 缺货 ")):(u(),g("span",Ad,x(m.availability||"不确定"),1))])])]),m.order_url?(u(),g("div",Hd,[t("a",{href:m.order_url,target:"_blank",class:"inline-flex items-center px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"},e[64]||(e[64]=[t("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7l5 5m0 0l-5 5m5-5H6"})],-1),$(" 立即订购 ")]),8,Id)])):M("",!0)])}),128))])):M("",!0),r.postDetail.processed_at?(u(),g("div",Od,[t("p",null,[e[65]||(e[65]=t("strong",{class:"text-gray-900 dark:text-white"},"处理时间:",-1)),$(" "+x(r.formatDate(r.postDetail.processed_at)),1)])])):M("",!0)])):(u(),g("div",Fd,[e[67]||(e[67]=t("h4",{class:"font-medium text-red-800 dark:text-red-200 mb-2"},"数据格式异常",-1)),e[68]||(e[68]=t("p",{class:"text-sm text-red-700 dark:text-red-300"},"无法解析帖子数据，请检查数据格式。",-1)),t("details",Ud,[e[66]||(e[66]=t("summary",{class:"cursor-pointer text-red-600 dark:text-red-400"},"查看原始数据",-1)),t("pre",Nd,x(JSON.stringify(r.postDetail,null,2)),1)])]))])):(u(),g("div",Dd,e[69]||(e[69]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"},null,-1)])))])])])):M("",!0)])}const Wd=ue(a0,[["render",qd],["__scopeId","data-v-df2430ff"]]),Gd={class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},Kd={class:"bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"},Qd={class:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700"},Jd={class:"mb-4"},Yd={class:"mb-4"},Xd={class:"mb-4"},Zd={class:"mb-4"},eu={class:"mb-4"},tu={class:"space-y-2 max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-3"},ru={class:"flex items-center"},ou=["value"],su={class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},au={class:"mb-4"},nu={class:"mb-6"},lu={class:"flex items-center"},iu={class:"mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-md"},du={class:"text-sm"},uu={class:"font-semibold text-gray-900 dark:text-white"},cu={class:"text-gray-600 dark:text-gray-400 mt-1"},gu={class:"flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400"},mu={class:"flex items-center"},fu={key:0},vu={class:"flex justify-end space-x-3 pt-4"},pu=["disabled"],bu={key:0,class:"animate-spin h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},xu={__name:"SendNotificationModal",props:{visible:Boolean,channels:Array,notificationTypes:Array},emits:["close","send"],setup(o,{emit:e}){const a=o,r=e,l=O(!1),d=O(!1),n=O(""),i=ie({title:"",content:"",notification_type:"custom",priority:"normal",target_channels:[],format_markdown:!0,source:"manual",tags:[],extra_data:{}}),p=A(()=>a.channels.filter(C=>C.enabled));Le(n,C=>{i.tags=C.split(",").map(v=>v.trim()).filter(v=>v.length>0)}),Le(()=>i.target_channels,C=>{d.value=C.length===p.value.length&&p.value.length>0});const s=()=>{d.value?i.target_channels=p.value.map(C=>C.id):i.target_channels=[]},m=async()=>{try{l.value=!0;const C={title:i.title,content:i.content,notification_type:i.notification_type,priority:i.priority,target_channels:i.target_channels.length>0?i.target_channels:null,format_markdown:i.format_markdown,source:i.source,tags:i.tags,extra_data:i.extra_data};r("send",C)}catch(C){console.error("发送通知失败:",C)}finally{l.value=!1}},c=C=>({telegram:"Telegram",discord:"Discord",wechat:"微信",webhook:"Webhook"})[C]||C,b=C=>({low:"低",normal:"普通",high:"高",critical:"紧急"})[C]||C,h=C=>({low:"bg-gray-400",normal:"bg-blue-400",high:"bg-yellow-400",critical:"bg-red-400"})[C]||"bg-gray-400",B=C=>({whmcs_stock:"WHMCS库存",whmcs_order:"WHMCS订单",let_monitor:"LET监控",let_match:"LET智能匹配",system_status:"系统状态",error_alert:"错误警报",custom:"自定义"})[C]||C;return Le(()=>a.visible,C=>{C&&(Object.assign(i,{title:"",content:"",notification_type:"custom",priority:"normal",target_channels:[],format_markdown:!0,source:"manual",tags:[],extra_data:{}}),n.value="",d.value=!1)}),(C,v)=>(u(),g("div",Gd,[t("div",Kd,[t("div",Qd,[v[11]||(v[11]=t("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 发送自定义通知 ",-1)),t("button",{onClick:v[0]||(v[0]=k=>C.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},v[10]||(v[10]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("form",{onSubmit:ve(m,["prevent"]),class:"p-6 space-y-6"},[t("div",Jd,[v[12]||(v[12]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," 消息标题 * ",-1)),F(t("input",{"onUpdate:modelValue":v[1]||(v[1]=k=>i.title=k),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"请输入通知标题",required:""},null,512),[[X,i.title]])]),t("div",Yd,[v[13]||(v[13]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," 消息内容 * ",-1)),F(t("textarea",{"onUpdate:modelValue":v[2]||(v[2]=k=>i.content=k),rows:"4",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"请输入通知内容",required:""},null,512),[[X,i.content]])]),t("div",Xd,[v[15]||(v[15]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," 通知类型 ",-1)),F(t("select",{"onUpdate:modelValue":v[3]||(v[3]=k=>i.notification_type=k),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"},v[14]||(v[14]=[Y('<option value="custom" data-v-48044172>自定义</option><option value="whmcs_stock" data-v-48044172>WHMCS库存</option><option value="whmcs_order" data-v-48044172>WHMCS订单</option><option value="let_monitor" data-v-48044172>LET监控</option><option value="system_status" data-v-48044172>系统状态</option><option value="error_alert" data-v-48044172>错误警报</option>',6)]),512),[[Ve,i.notification_type]])]),t("div",Zd,[v[17]||(v[17]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," 优先级 ",-1)),F(t("select",{"onUpdate:modelValue":v[4]||(v[4]=k=>i.priority=k),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"},v[16]||(v[16]=[t("option",{value:"low"},"低",-1),t("option",{value:"normal"},"普通",-1),t("option",{value:"high"},"高",-1),t("option",{value:"critical"},"紧急",-1)]),512),[[Ve,i.priority]])]),t("div",eu,[v[20]||(v[20]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," 目标渠道 ",-1)),t("div",tu,[t("label",ru,[F(t("input",{"onUpdate:modelValue":v[5]||(v[5]=k=>d.value=k),type:"checkbox",onChange:s,class:"rounded text-blue-600 focus:ring-blue-500"},null,544),[[pe,d.value]]),v[18]||(v[18]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300 font-medium"},"全选",-1))]),v[19]||(v[19]=t("hr",{class:"border-gray-200 dark:border-gray-600"},null,-1)),(u(!0),g(ne,null,de(p.value,k=>(u(),g("label",{key:k.id,class:"flex items-center"},[F(t("input",{"onUpdate:modelValue":v[6]||(v[6]=w=>i.target_channels=w),value:k.id,type:"checkbox",class:"rounded text-blue-600 focus:ring-blue-500"},null,8,ou),[[pe,i.target_channels]]),t("span",su,x(k.name)+" ("+x(c(k.channel_type))+") ",1)]))),128))]),v[21]||(v[21]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-1"}," 不选择任何渠道将发送到所有适用的渠道 ",-1))]),t("div",au,[v[22]||(v[22]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," 标签 (可选) ",-1)),F(t("input",{"onUpdate:modelValue":v[7]||(v[7]=k=>n.value=k),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"用逗号分隔多个标签，如：urgent,manual"},null,512),[[X,n.value]])]),t("div",nu,[t("label",lu,[F(t("input",{"onUpdate:modelValue":v[8]||(v[8]=k=>i.format_markdown=k),type:"checkbox",class:"rounded text-blue-600 focus:ring-blue-500"},null,512),[[pe,i.format_markdown]]),v[23]||(v[23]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"启用 Markdown 格式",-1))])]),t("div",iu,[v[24]||(v[24]=t("h4",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"预览",-1)),t("div",du,[t("div",uu,x(i.title||"标题预览"),1),t("div",cu,x(i.content||"内容预览"),1),t("div",gu,[t("span",mu,[t("span",{class:P([h(i.priority),"w-2 h-2 rounded-full mr-1"])},null,2),$(" 优先级: "+x(b(i.priority)),1)]),t("span",null,"类型: "+x(B(i.notification_type)),1),i.target_channels.length?(u(),g("span",fu," 目标: "+x(i.target_channels.length)+"个渠道 ",1)):M("",!0)])])]),t("div",vu,[t("button",{type:"button",onClick:v[9]||(v[9]=k=>C.$emit("close")),class:"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-lg transition-colors"}," 取消 "),t("button",{type:"submit",disabled:!i.title||!i.content||l.value,class:"px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center space-x-2"},[l.value?(u(),g("svg",bu,v[25]||(v[25]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):M("",!0),t("span",null,x(l.value?"发送中...":"发送通知"),1)],8,pu)])],32)])]))}},hu=ue(xu,[["__scopeId","data-v-48044172"]]),yu={class:"space-y-6"},ku={class:"flex items-center justify-between"},wu={class:"flex gap-3"},_u={key:0,class:"text-center p-12"},Cu={key:1,class:"text-center p-12 bg-white dark:bg-gray-800 rounded-xl shadow-sm"},Mu={key:2,class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},Su={class:"p-4 border-b border-gray-200 dark:border-gray-700"},ju={class:"flex items-start justify-between mb-3"},Lu={class:"flex-1"},Vu={class:"flex items-center space-x-2 mb-2"},Bu={class:"relative"},Eu={key:0,class:"absolute inset-0 w-2.5 h-2.5 bg-blue-400 rounded-full animate-ping opacity-75"},Pu={class:"text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate"},Ru={class:"flex items-center space-x-2 mb-2"},zu={key:0,class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 24 24"},Tu={key:1,class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 24 24"},$u={key:2,class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 24 24"},Au={key:3,class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Hu={class:"text-sm text-gray-600 dark:text-gray-300 capitalize"},Iu={class:"p-4 flex-1 flex flex-col"},Ou={class:"space-y-3 mb-6 flex-1"},Fu={class:"text-sm"},Uu={class:"mt-1 flex flex-wrap gap-1"},Nu={key:1,class:"text-gray-400 text-xs"},Du={class:"flex items-center justify-between text-sm"},qu={class:"flex items-center justify-between text-sm"},Wu={class:"text-gray-900 dark:text-white"},Gu={class:"mt-auto space-y-3"},Ku={class:"flex flex-wrap gap-2"},Qu=["onClick","disabled"],Ju={key:0,class:"animate-spin w-4 h-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Yu={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Xu=["onClick"],Zu={class:"flex gap-2"},ec=["onClick"],tc=["onClick"],rc={key:0,class:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"},oc={class:"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto border border-gray-200/50 dark:border-gray-700/50"},sc={class:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl"},ac={class:"flex items-center space-x-3"},nc={class:"text-xl font-bold text-gray-900 dark:text-white"},lc={class:"space-y-6"},ic={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},dc={class:"flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl"},uc={class:"space-y-6"},cc={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},gc={class:"bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4 space-y-3 max-h-48 overflow-y-auto"},mc={class:"flex items-center p-2 bg-white dark:bg-gray-700 rounded-lg shadow-sm"},fc={class:"border-t border-gray-200 dark:border-gray-600 pt-2"},vc=["value"],pc={class:"ml-3 text-sm text-gray-700 dark:text-gray-300"},bc={class:"mt-4"},xc={key:0,class:"space-y-6"},hc={class:"text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2"},yc={key:0,class:"grid grid-cols-1 gap-6"},kc={key:1,class:"grid grid-cols-1 gap-6"},wc={key:2},_c={key:3,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Cc={class:"md:col-span-2"},Mc={class:"flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700"},Sc={type:"submit",class:"px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5"},jc=Object.assign({name:"NotificationManager"},{__name:"Notifications",setup(o){const e=O([]),a=O([]),r=O(!1),l=O(!1),d=O(!1),n=O(null),i=O(!1),p=O({}),s=ie({name:"",channel_type:"",enabled:!0,notification_types:[],min_priority:"normal",config:{}}),m=O(!1),c={whmcs_stock:"WHMCS库存通知",whmcs_order:"WHMCS订单通知",let_monitor:"LET监控通知",let_match:"LET智能匹配",system_status:"系统状态通知",error_alert:"错误警报",custom:"自定义通知"},b=()=>{s.config={}},h=()=>{m.value?s.notification_types=Object.keys(c):s.notification_types=[]},B=S=>({low:"低",normal:"普通",high:"高",critical:"紧急"})[S]||S,C=S=>({telegram:"Telegram",discord:"Discord",wechat:"微信",webhook:"Webhook"})[S]||S,v=S=>({telegram:"bg-blue-500",discord:"bg-indigo-500",wechat:"bg-green-500",webhook:"bg-gray-500"})[S]||"bg-gray-500",k=async()=>{try{i.value=!0;const f=await(await fetch("/api/notifications/channels")).json();e.value=f.channels||[],a.value=f.notification_types||[]}catch(S){console.error("加载通知渠道失败:",S)}finally{i.value=!1}},w=async S=>{try{p.value[S]=!0;const _=await(await fetch(`/api/notifications/channels/${S}/test`,{method:"POST"})).json();_.success?R("success","测试通知发送成功！"):R("error",`测试失败: ${_.message}`)}catch(f){console.error("测试渠道失败:",f),R("error","测试失败，请检查网络连接")}finally{p.value[S]=!1}},E=S=>{n.value=S,Object.assign(s,{name:S.name,channel_type:S.channel_type,enabled:S.enabled,notification_types:S.notification_types||[],min_priority:S.min_priority||"normal",config:{...S.config}}),m.value=s.notification_types.length===Object.keys(c).length,l.value=!0},N=async S=>{try{const f=e.value.find(W=>W.id===S);(await fetch(`/api/notifications/channels/${S}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({enabled:!f.enabled})})).ok?(f.enabled=!f.enabled,R("success",`渠道已${f.enabled?"启用":"禁用"}`)):R("error","切换渠道状态失败")}catch(f){console.error("切换渠道状态失败:",f),R("error","操作失败，请重试")}},Z=async S=>{if(await gt("确认删除","确定要删除这个通知渠道吗？"))try{(await fetch(`/api/notifications/channels/${S}`,{method:"DELETE"})).ok?(e.value=e.value.filter(W=>W.id!==S),R("success","渠道删除成功")):R("error","删除失败")}catch(_){console.error("删除渠道失败:",_),R("error","删除失败，请重试")}},H=async()=>{try{const S=n.value?`/api/notifications/channels/${n.value.id}`:"/api/notifications/channels",f=n.value?"PUT":"POST",_={name:s.name,channel_type:s.channel_type,enabled:s.enabled,notification_types:s.notification_types,min_priority:s.min_priority,config:s.config},W=await fetch(S,{method:f,headers:{"Content-Type":"application/json"},body:JSON.stringify(_)});if(W.ok)await k(),V(),R("success",n.value?"渠道更新成功":"渠道创建成功");else{const ae=await W.json();R("error",`保存失败: ${ae.detail}`)}}catch(S){console.error("保存渠道失败:",S),R("error","保存失败，请重试")}},V=()=>{r.value=!1,l.value=!1,n.value=null,Object.assign(s,{name:"",channel_type:"",enabled:!0,notification_types:[],min_priority:"normal",config:{}}),m.value=!1},z=async S=>{try{const _=await(await fetch("/api/notifications/send",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(S)})).json();_.success?(d.value=!1,R("success",`通知发送成功！成功: ${_.successful_channels.length}, 失败: ${_.failed_channels.length}`)):R("error",`发送失败: ${_.message}`)}catch(f){console.error("发送通知失败:",f),R("error","发送失败，请重试")}};return we(()=>{k()}),(S,f)=>(u(),g(ne,null,[t("div",yu,[t("div",ku,[f[20]||(f[20]=t("div",null,[t("h2",{class:"text-3xl font-bold text-gray-900 dark:text-white"},"通知管理"),t("p",{class:"text-gray-600 dark:text-gray-400 mt-1"},"管理通知渠道和发送自定义消息")],-1)),t("div",wu,[t("button",{onClick:f[0]||(f[0]=_=>r.value=!0),class:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200 flex items-center space-x-2"},f[18]||(f[18]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),t("span",null,"添加渠道",-1)])),t("button",{onClick:f[1]||(f[1]=_=>d.value=!0),class:"bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200 flex items-center space-x-2"},f[19]||(f[19]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})],-1),t("span",null,"发送通知",-1)]))])]),i.value?(u(),g("div",_u,f[21]||(f[21]=[t("div",{class:"spinner border-4 border-blue-500 rounded-full w-8 h-8 mx-auto mb-4"},null,-1),t("p",{class:"text-gray-500"},"加载通知渠道中...",-1)]))):e.value.length===0?(u(),g("div",Cu,[f[22]||(f[22]=t("svg",{class:"h-16 w-16 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM4 19h7a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),f[23]||(f[23]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"暂无通知渠道",-1)),f[24]||(f[24]=t("p",{class:"text-gray-500 mb-4"},"开始添加您的第一个通知渠道",-1)),t("button",{onClick:f[2]||(f[2]=_=>r.value=!0),class:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200"}," 添加第一个渠道 ")])):(u(),g("div",Mu,[(u(!0),g(ne,null,de(e.value,_=>(u(),g("div",{key:_.id,class:"group bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-300 transform hover:-translate-y-0.5 hover:scale-[1.01] flex flex-col"},[t("div",Su,[t("div",ju,[t("div",Lu,[t("div",Vu,[t("div",Bu,[t("div",{class:P(["w-2.5 h-2.5 rounded-full",_.enabled?"bg-blue-500 animate-pulse":"bg-red-500"])},null,2),_.enabled?(u(),g("div",Eu)):M("",!0)]),t("h3",Pu,x(_.name),1)]),t("div",Ru,[t("div",{class:P(["p-1.5 rounded-lg",v(_.channel_type)])},[_.channel_type==="telegram"?(u(),g("svg",zu,f[25]||(f[25]=[t("path",{d:"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"},null,-1)]))):_.channel_type==="discord"?(u(),g("svg",Tu,f[26]||(f[26]=[t("path",{d:"M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"},null,-1)]))):_.channel_type==="wechat"?(u(),g("svg",$u,f[27]||(f[27]=[t("path",{d:"M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.53-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.53-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.34 2.867c-1.797-.052-3.746.512-5.28 1.786-1.72 1.428-2.687 3.72-1.78 6.22.942 2.453 3.666 4.229 6.884 4.229.826 0 1.622-.12 2.361-.336a.722.722 0 0 1 .598.082l1.584.926a.272.272 0 0 0 .14.04c.134 0 .24-.111.24-.247 0-.06-.023-.12-.038-.177l-.327-1.233a.582.582 0 0 1-.023-.156.49.49 0 0 1 .201-.398C23.024 18.48 24 16.82 24 14.98c0-3.21-2.931-5.837-6.656-6.088V8.858zm-3.288 1.87c.54 0 .97.424.97.949 0 .525-.43.949-.97.949-.54 0-.97-.424-.97-.949 0-.525.43-.949.97-.949zm4.714 0c.54 0 .97.424.97.949 0 .525-.43.949-.97.949-.54 0-.97-.424-.97-.949 0-.525.43-.949.97-.949z"},null,-1)]))):(u(),g("svg",Au,f[28]||(f[28]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"},null,-1)])))],2),t("span",Hu,x(C(_.channel_type)),1)]),t("span",{class:P(["inline-flex items-center px-2 py-1 text-xs font-medium rounded-full",_.enabled?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"])},[(u(),g("svg",{class:P(["w-2 h-2 mr-1",_.enabled?"text-green-500":"text-red-500"]),fill:"currentColor",viewBox:"0 0 8 8"},f[29]||(f[29]=[t("circle",{cx:"4",cy:"4",r:"3"},null,-1)]),2)),$(" "+x(_.enabled?"运行中":"已停用"),1)],2)])])]),t("div",Iu,[t("div",Ou,[t("div",Fu,[f[30]||(f[30]=t("span",{class:"text-gray-500 dark:text-gray-400"},"通知类型:",-1)),t("div",Uu,[_.notification_types&&_.notification_types.length>0?(u(!0),g(ne,{key:0},de(_.notification_types,W=>(u(),g("span",{key:W,class:"px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full"},x(c[W]||W),1))),128)):(u(),g("span",Nu,"全部类型"))])]),t("div",Du,[f[31]||(f[31]=t("span",{class:"text-gray-500 dark:text-gray-400"},"最低优先级",-1)),t("span",{class:P(["px-2 py-1 text-xs rounded-full",_.min_priority==="critical"?"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200":_.min_priority==="high"?"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200":_.min_priority==="normal"?"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200":"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"])},x(B(_.min_priority)),3)]),t("div",qu,[f[32]||(f[32]=t("span",{class:"text-gray-500 dark:text-gray-400"},"重试次数",-1)),t("span",Wu,x(_.retry_count||3),1)])]),t("div",Gu,[t("div",Ku,[t("button",{onClick:W=>w(_.id),disabled:p.value[_.id],class:"flex-1 px-3 py-2 text-sm bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center gap-1"},[p.value[_.id]?(u(),g("svg",Ju,f[33]||(f[33]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):(u(),g("svg",Yu,f[34]||(f[34]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},null,-1)]))),f[35]||(f[35]=$(" 测试 "))],8,Qu),t("button",{onClick:W=>E(_),class:"flex-1 px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center justify-center gap-1"},f[36]||(f[36]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),$(" 编辑 ")]),8,Xu)]),t("div",Zu,[t("button",{onClick:W=>N(_.id),class:P(["flex-1 px-3 py-2 text-sm rounded-lg transition-all duration-200 font-medium",_.enabled?"bg-orange-50 dark:bg-orange-900 text-orange-600 dark:text-orange-300 hover:bg-orange-100 dark:hover:bg-orange-800":"bg-green-50 dark:bg-green-900 text-green-600 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-800"])},x(_.enabled?"禁用":"启用"),11,ec),t("button",{onClick:W=>Z(_.id),class:"flex-1 px-3 py-2 text-sm bg-red-50 dark:bg-red-900 text-red-600 dark:text-red-300 hover:bg-red-100 dark:hover:bg-red-800 rounded-lg transition-colors"}," 删除 ",8,tc)])])])]))),128))]))]),r.value||l.value?(u(),g("div",rc,[t("div",oc,[t("div",sc,[t("div",ac,[f[37]||(f[37]=t("div",{class:"p-2 bg-blue-500 rounded-lg"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM4 19h7a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z"})])],-1)),t("h3",nc,x(n.value?"编辑通知渠道":"添加通知渠道"),1)]),t("button",{onClick:V,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"},f[38]||(f[38]=[t("svg",{class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("form",{onSubmit:ve(H,["prevent"]),class:"p-8 space-y-8"},[t("div",lc,[f[43]||(f[43]=t("h4",{class:"text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2"},"基本信息",-1)),t("div",ic,[t("div",null,[f[39]||(f[39]=t("label",{class:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"},"渠道名称",-1)),F(t("input",{"onUpdate:modelValue":f[3]||(f[3]=_=>s.name=_),type:"text",placeholder:"输入渠道名称",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200",required:""},null,512),[[X,s.name]])]),t("div",null,[f[41]||(f[41]=t("label",{class:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"},"渠道类型",-1)),F(t("select",{"onUpdate:modelValue":f[4]||(f[4]=_=>s.channel_type=_),onChange:b,class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200",required:""},f[40]||(f[40]=[Y('<option value="" data-v-c2726947>请选择渠道类型</option><option value="telegram" data-v-c2726947>📱 Telegram</option><option value="discord" data-v-c2726947>🎮 Discord</option><option value="wechat" data-v-c2726947>💬 微信</option><option value="webhook" data-v-c2726947>🔗 Webhook</option>',5)]),544),[[Ve,s.channel_type]])])]),t("div",dc,[F(t("input",{"onUpdate:modelValue":f[5]||(f[5]=_=>s.enabled=_),type:"checkbox",class:"w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"},null,512),[[pe,s.enabled]]),f[42]||(f[42]=t("div",null,[t("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"启用此渠道"),t("p",{class:"text-xs text-gray-500 dark:text-gray-400"},"开启后将开始接收通知消息")],-1))])]),t("div",uc,[f[52]||(f[52]=t("h4",{class:"text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2"},"通知配置",-1)),t("div",cc,[t("div",null,[f[45]||(f[45]=t("label",{class:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"}," 通知类型 ",-1)),t("div",gc,[t("label",mc,[F(t("input",{"onUpdate:modelValue":f[6]||(f[6]=_=>m.value=_),type:"checkbox",onChange:h,class:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"},null,544),[[pe,m.value]]),f[44]||(f[44]=t("span",{class:"ml-3 text-sm font-medium text-gray-900 dark:text-white"},"全选",-1))]),t("div",fc,[(u(),g(ne,null,de(c,(_,W)=>t("label",{key:W,class:"flex items-center p-2 hover:bg-white dark:hover:bg-gray-700 rounded-lg transition-colors cursor-pointer"},[F(t("input",{"onUpdate:modelValue":f[7]||(f[7]=ae=>s.notification_types=ae),value:W,type:"checkbox",class:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"},null,8,vc),[[pe,s.notification_types]]),t("span",pc,x(_),1)])),64))])]),f[46]||(f[46]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," 💡 不选择任何类型将接收所有类型的通知 ",-1))]),t("div",null,[f[50]||(f[50]=t("label",{class:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"}," 最低优先级 ",-1)),F(t("select",{"onUpdate:modelValue":f[8]||(f[8]=_=>s.min_priority=_),class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"},f[47]||(f[47]=[t("option",{value:"low"},"🔵 低优先级",-1),t("option",{value:"normal"},"🟡 普通优先级",-1),t("option",{value:"high"},"🟠 高优先级",-1),t("option",{value:"critical"},"🔴 紧急优先级",-1)]),512),[[Ve,s.min_priority]]),f[51]||(f[51]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," 🎯 只接收此优先级及以上的通知 ",-1)),t("div",bc,[f[48]||(f[48]=t("label",{class:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"}," 重试次数 ",-1)),F(t("input",{"onUpdate:modelValue":f[9]||(f[9]=_=>s.retry_count=_),type:"number",min:"0",max:"10",placeholder:"3",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"},null,512),[[X,s.retry_count,void 0,{number:!0}]]),f[49]||(f[49]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," 🔄 发送失败时的重试次数 (0-10) ",-1))])])])]),s.channel_type?(u(),g("div",xc,[t("h4",hc,x(C(s.channel_type))+" 配置 ",1),s.channel_type==="telegram"?(u(),g("div",yc,[t("div",null,[f[53]||(f[53]=t("label",{class:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"}," 🤖 Bot Token ",-1)),F(t("input",{"onUpdate:modelValue":f[10]||(f[10]=_=>s.config.bot_token=_),type:"text",placeholder:"输入 Telegram Bot Token",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200",required:""},null,512),[[X,s.config.bot_token]]),f[54]||(f[54]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," 从 @BotFather 获取的机器人令牌 ",-1))]),t("div",null,[f[55]||(f[55]=t("label",{class:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"}," 💬 Chat ID ",-1)),F(t("input",{"onUpdate:modelValue":f[11]||(f[11]=_=>s.config.chat_id=_),type:"text",placeholder:"输入 Chat ID",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200",required:""},null,512),[[X,s.config.chat_id]]),f[56]||(f[56]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," 接收消息的聊天 ID (个人或群组) ",-1))])])):M("",!0),s.channel_type==="discord"?(u(),g("div",kc,[t("div",null,[f[57]||(f[57]=t("label",{class:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"}," 🔗 Webhook URL ",-1)),F(t("input",{"onUpdate:modelValue":f[12]||(f[12]=_=>s.config.webhook_url=_),type:"url",placeholder:"https://discord.com/api/webhooks/...",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200",required:""},null,512),[[X,s.config.webhook_url]]),f[58]||(f[58]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," Discord 频道的 Webhook 地址 ",-1))]),t("div",null,[f[59]||(f[59]=t("label",{class:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"}," 👤 用户名 (可选) ",-1)),F(t("input",{"onUpdate:modelValue":f[13]||(f[13]=_=>s.config.username=_),type:"text",placeholder:"机器人显示名称",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"},null,512),[[X,s.config.username]]),f[60]||(f[60]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," 消息发送者的显示名称 ",-1))])])):M("",!0),s.channel_type==="wechat"?(u(),g("div",wc,[f[61]||(f[61]=t("label",{class:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"}," 🔑 Webhook Key ",-1)),F(t("input",{"onUpdate:modelValue":f[14]||(f[14]=_=>s.config.webhook_key=_),type:"text",placeholder:"输入企业微信 Webhook Key",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200",required:""},null,512),[[X,s.config.webhook_key]]),f[62]||(f[62]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," 企业微信群机器人的 Webhook 密钥 ",-1))])):M("",!0),s.channel_type==="webhook"?(u(),g("div",_c,[t("div",Cc,[f[63]||(f[63]=t("label",{class:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"}," 🌐 Webhook URL ",-1)),F(t("input",{"onUpdate:modelValue":f[15]||(f[15]=_=>s.config.url=_),type:"url",placeholder:"https://example.com/webhook",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200",required:""},null,512),[[X,s.config.url]]),f[64]||(f[64]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," 接收通知的 Webhook 地址 ",-1))]),t("div",null,[f[66]||(f[66]=t("label",{class:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"}," 📡 请求方法 ",-1)),F(t("select",{"onUpdate:modelValue":f[16]||(f[16]=_=>s.config.method=_),class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"},f[65]||(f[65]=[t("option",{value:"POST"},"POST",-1),t("option",{value:"GET"},"GET",-1),t("option",{value:"PUT"},"PUT",-1)]),512),[[Ve,s.config.method]])])])):M("",!0)])):M("",!0),t("div",Mc,[t("button",{type:"button",onClick:V,class:"px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-xl font-medium transition-all duration-200"}," 取消 "),t("button",Sc,x(n.value?"更新渠道":"创建渠道"),1)])],32)])])):M("",!0),d.value?(u(),Be(hu,{key:1,visible:d.value,channels:e.value,"notification-types":a.value,onClose:f[17]||(f[17]=_=>d.value=!1),onSend:z},null,8,["visible","channels","notification-types"])):M("",!0)],64))}}),Lc=ue(jc,[["__scopeId","data-v-c2726947"]]),lr=Vo({history:oo(),routes:[{path:"/",name:"dashboard",component:Us,meta:{requiresAuth:!0}},{path:"/sites",name:"sites",component:Sn,meta:{requiresAuth:!0}},{path:"/sites/:siteId/products",name:"products",component:wi,props:!0,meta:{requiresAuth:!0}},{path:"/let-monitor",name:"let-monitor",component:Wd,meta:{requiresAuth:!0}},{path:"/notifications",name:"notifications",component:Lc,meta:{requiresAuth:!0}},{path:"/settings",name:"settings",component:s0,meta:{requiresAuth:!0}},{path:"/:pathMatch(.*)*",redirect:"/"}]});let ce={isAuthenticated:null,lastCheck:0,cacheTimeout:3e4};const Vc=async()=>{const o=Date.now();if(ce.isAuthenticated!==null&&o-ce.lastCheck<ce.cacheTimeout)return ce.isAuthenticated;try{const e=await fetch("/api/status",{method:"GET",headers:{Accept:"application/json"}}),a=e.status!==401&&e.ok;return ce={isAuthenticated:a,lastCheck:o,cacheTimeout:ce.cacheTimeout},a}catch(e){return console.error("授权检查失败:",e),ce.isAuthenticated!==null&&o-ce.lastCheck<6e4?ce.isAuthenticated:(ce.isAuthenticated=!1,ce.lastCheck=o,!1)}},Tt=()=>{ce={isAuthenticated:null,lastCheck:0,cacheTimeout:3e4}};lr.beforeEach(async(o,e,a)=>{if(o.matched.some(r=>r.meta.requiresAuth)&&!await Vc()){window.location.href="/login.html";return}a()});const _e=Sr("main",()=>{const o=O(localStorage.getItem("theme")||"dark"),e=O("dashboard"),a=O(!0),r=O({}),l=O("stopped"),d=O([]),n=O([]),i=O([]),p=O(null),s=O(null),m=O({whmcs:{},let_monitor:{},notifications:{}}),c=O({status:!1,monitor:!1,sites:!1,products:!1,config:!1,testingLogin:null}),b=A(()=>o.value==="dark"?"☀️":"🌙"),h=()=>{o.value=o.value==="dark"?"light":"dark",localStorage.setItem("theme",o.value),o.value==="dark"?(document.documentElement.classList.add("dark"),document.body.classList.add("dark")):(document.documentElement.classList.remove("dark"),document.body.classList.remove("dark"))},B=()=>{o.value==="dark"?(document.documentElement.classList.add("dark"),document.body.classList.add("dark")):(document.documentElement.classList.remove("dark"),document.body.classList.remove("dark"))},C=async()=>{if(!c.value.status){c.value.status=!0;try{const I=await re.get("/api/status");r.value=I,l.value=I.is_running||I.monitor_running?"running":"stopped"}catch(I){console.error("Failed to get status:",I)}finally{c.value.status=!1}}},v=async()=>{try{const I=await re.get("/api/logs");d.value=I.logs||[]}catch(I){console.error("Failed to get logs:",I)}},k=async()=>{if(!c.value.sites){c.value.sites=!0;try{const I=await re.get("/api/sites");n.value=Object.entries(I||{}).map(([Q,ee])=>({site_id:Q,...ee}))}catch(I){console.error("Failed to get sites:",I)}finally{c.value.sites=!1}}},w=async I=>{if(!c.value.products){c.value.products=!0;try{const Q=await re.get(`/api/sites/${I}/products`);i.value=Object.entries(Q||{}).map(([ee,te])=>({product_id:ee,...te})),s.value=I}catch(Q){console.error("Failed to get products:",Q)}finally{c.value.products=!1}}},E=async()=>{if(!c.value.config){c.value.config=!0;try{const I=await re.get("/api/config");m.value=I}catch(I){console.error("Failed to get config:",I)}finally{c.value.config=!1}}};return{theme:o,currentView:e,isSidebarOpen:a,status:r,monitorStatus:l,logs:d,sites:n,products:i,selectedSiteId:p,currentSiteId:s,config:m,isLoading:c,themeIcon:b,toggleTheme:h,initTheme:B,getStatus:C,getLogs:v,getSites:k,getProducts:w,getConfig:E,startMonitor:async()=>{c.value.monitor=!0;try{await re.post("/api/monitor/start"),await C()}finally{c.value.monitor=!1}},stopMonitor:async()=>{c.value.monitor=!0;try{await re.post("/api/monitor/stop"),await C()}finally{c.value.monitor=!1}},deleteSite:async I=>{try{return await re.delete(`/api/sites/${I}`),await k(),!0}catch{return!1}},deleteProduct:async(I,Q)=>{try{return await re.delete(`/api/sites/${I}/products/${Q}`),await w(I),!0}catch{return!1}},toggleProduct:async(I,Q,ee)=>{try{return await re.post(`/api/sites/${I}/products/${Q}/toggle`,{enabled:ee}),await w(I),!0}catch{return!1}},toggleAutoOrder:async(I,Q)=>{try{return await re.post(`/api/sites/${I}/products/${Q}/toggle-auto-order`),await w(I),!0}catch{return!1}},resetFailures:async(I,Q)=>{try{return await re.post(`/api/sites/${I}/products/${Q}/reset-failures`),await w(I),!0}catch{return!1}},testLogin:async I=>{c.value.testingLogin=I;try{return await re.get(`/api/test-login/${I}`)}finally{c.value.testingLogin=null}},saveSettings:async(I,Q)=>{try{const ee=await re.post(`/api/config/${I}`,Q);return await E(),ee}catch{return!1}},logout:async()=>{try{await re.post("/api/logout"),Tt(),window.location.href="/login"}catch{Tt(),window.location.href="/login"}}}}),Bc={name:"Sidebar",emits:["toggle-sidebar","toggle-theme"],setup(){const o=_e(),e=[{name:"dashboard",path:"/",label:"仪表盘",icon:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
        </svg>`},{name:"sites",path:"/sites",label:"WHMCS管理",icon:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>`},{name:"let-monitor",path:"/let-monitor",label:"LET监控",icon:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>`},{name:"notifications",path:"/notifications",label:"通知管理",icon:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 19h16a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>`},{name:"settings",path:"/settings",label:"系统设置",icon:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>`}],a=A(()=>o.theme),r=A(()=>a.value==="dark"?`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>`:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>`);return{menuItems:e,theme:a,themeIcon:r,logout:async()=>{try{await fetch("/api/logout"),window.location.href="/login"}catch(n){console.error("Logout failed:",n),window.location.href="/login"}},toggleTheme:()=>{o.toggleTheme()}}}},Ec={class:"sidebar fixed top-0 left-0 h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-4 z-20 flex flex-col"},Pc={class:"flex items-center justify-between mb-6"},Rc={class:"flex-grow"},zc=["innerHTML"],Tc={class:"font-medium"},$c={class:"mt-auto space-y-2"},Ac=["innerHTML"],Hc={class:"font-medium"};function Ic(o,e,a,r,l,d){const n=ye("router-link");return u(),g("aside",Ec,[t("div",Pc,[e[4]||(e[4]=t("h1",{class:"text-2xl font-bold text-indigo-600 dark:text-indigo-400"},"集成监控",-1)),t("button",{onClick:e[0]||(e[0]=i=>o.$emit("toggle-sidebar")),class:"lg:hidden p-1"},e[3]||(e[3]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-6 h-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("nav",Rc,[t("ul",null,[(u(!0),g(ne,null,de(r.menuItems,i=>(u(),g("li",{key:i.name,class:"mb-2"},[q(n,{to:i.path,class:P(["nav-item flex items-center p-3 rounded-lg transition-all duration-200",o.$route.name===i.name?"bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 shadow-md":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"])},{default:K(()=>[t("span",{innerHTML:i.icon,class:"mr-3 w-6 h-6"},null,8,zc),t("span",Tc,x(i.label),1)]),_:2},1032,["to","class"])]))),128))])]),t("div",$c,[t("button",{onClick:e[1]||(e[1]=(...i)=>r.toggleTheme&&r.toggleTheme(...i)),class:"w-full flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 text-gray-700 dark:text-gray-300"},[t("span",{innerHTML:r.themeIcon,class:"mr-3 w-6 h-6"},null,8,Ac),t("span",Hc,x(r.theme==="dark"?"切换亮色":"切换暗色"),1)]),t("button",{onClick:e[2]||(e[2]=(...i)=>r.logout&&r.logout(...i)),class:"w-full flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 text-red-500 dark:text-red-400"},e[5]||(e[5]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})],-1),t("span",{class:"font-medium"},"退出登录",-1)]))])])}const Oc=ue(Bc,[["render",Ic],["__scopeId","data-v-4e154bc8"]]),Fc={class:"relative bg-white/80 dark:bg-gray-900/90 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50 shadow-lg shadow-gray-200/20 dark:shadow-gray-900/20"},Uc={class:"relative px-6 py-3"},Nc={class:"flex items-center justify-between"},Dc={class:"flex items-center space-x-4"},qc={class:"flex items-center space-x-3"},Wc={class:"text-2xl font-bold bg-gradient-to-r from-gray-900 via-indigo-900 to-purple-900 dark:from-white dark:via-blue-100 dark:to-purple-100 bg-clip-text text-transparent"},Gc={class:"flex items-center space-x-4"},Kc={class:"flex items-center space-x-3"},Qc={class:"relative"},Jc=["title"],Yc=["d"],Xc={key:1,class:"w-5 h-5 text-white animate-spin",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Zc={key:0,class:"absolute inset-0 rounded-full border border-green-400 animate-ping opacity-30"},e1={class:"relative"},t1=["title"],r1={class:"relative w-6 h-6"},o1={key:0,class:"w-6 h-6 text-white animate-spin-slow transition-all duration-500",fill:"currentColor",viewBox:"0 0 24 24"},s1={key:1,class:"w-6 h-6 text-white animate-pulse transition-all duration-500",fill:"currentColor",viewBox:"0 0 24 24"},a1={class:"relative"},n1={key:0,class:"absolute right-0 mt-3 w-48 bg-white/90 dark:bg-gray-800/90 rounded-2xl shadow-2xl border border-gray-200/60 dark:border-gray-700/60 py-2 z-50 backdrop-blur-xl"},l1={__name:"TopNavbar",setup(o){const e=sr(),a=_e(),r=O(!1),l=O(!1),d=A(()=>a.theme);A(()=>a.themeIcon);const n=A(()=>a.monitorStatus),i=A(()=>({Dashboard:"仪表板",Sites:"网站管理",Products:"产品管理",Settings:"系统设置"})[e.name]||"管理面板"),p=async()=>{l.value=!0;try{n.value==="running"?(await a.stopMonitor(),R("success","监控已停止")):(await a.startMonitor(),R("success","监控已启动"))}catch{R("error","操作失败")}finally{l.value=!1}},s=()=>{r.value=!1,a.logout()},m={beforeMount(c,b){c.clickOutsideEvent=function(h){c===h.target||c.contains(h.target)||b.value()},document.addEventListener("click",c.clickOutsideEvent)},unmounted(c){document.removeEventListener("click",c.clickOutsideEvent)}};return(c,b)=>(u(),g("header",Fc,[b[11]||(b[11]=t("div",{class:"absolute inset-0 bg-gradient-to-r from-blue-50/30 via-indigo-50/20 to-purple-50/30 dark:from-blue-900/5 dark:via-indigo-900/3 dark:to-purple-900/5"},null,-1)),t("div",Uc,[t("div",Nc,[t("div",Dc,[t("button",{onClick:b[0]||(b[0]=h=>fe(a).isSidebarOpen=!fe(a).isSidebarOpen),class:"lg:hidden p-2.5 rounded-xl text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-white/60 dark:hover:bg-gray-800/60 backdrop-blur-sm border border-transparent hover:border-gray-200/60 dark:hover:border-gray-700/60 transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md"},b[3]||(b[3]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),t("div",qc,[b[4]||(b[4]=t("div",{class:"w-1 h-8 bg-gradient-to-b from-indigo-500 to-purple-600 rounded-full"},null,-1)),t("h1",Wc,x(i.value),1)])]),t("div",Gc,[t("div",Kc,[t("div",Qc,[t("div",{class:P(["relative w-11 h-11 rounded-2xl border-2 transition-all duration-500 cursor-pointer group backdrop-blur-sm",n.value==="running"?"border-green-400/60 bg-gradient-to-br from-green-50/80 to-emerald-100/80 dark:from-green-900/40 dark:to-emerald-900/40 shadow-xl shadow-green-200/30 dark:shadow-green-900/30 hover:shadow-2xl hover:shadow-green-200/40":"border-gray-300/60 dark:border-gray-600/60 bg-gradient-to-br from-gray-50/80 to-gray-100/80 dark:from-gray-800/60 dark:to-gray-700/60 shadow-lg hover:shadow-xl"]),onClick:p,title:n.value==="running"?"点击停止监控":"点击启动监控"},[t("div",{class:P(["absolute inset-1 rounded-xl flex items-center justify-center transition-all duration-500 transform",n.value==="running"?"bg-gradient-to-br from-green-400 to-emerald-500 scale-100 rotate-0":"bg-gradient-to-br from-gray-400 to-gray-500 scale-75 rotate-180"])},[l.value?(u(),g("svg",Xc,b[5]||(b[5]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):(u(),g("svg",{key:0,class:P(["w-5 h-5 transition-all duration-500",n.value==="running"?"text-white animate-pulse":"text-gray-200"]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:n.value==="running"?"M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z":"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L17 17M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,8,Yc)],2))],2),t("div",{class:P(["absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full border border-white transition-all duration-300",n.value==="running"?"bg-green-500 animate-ping":"bg-red-500"])},null,2),n.value==="running"?(u(),g("div",Zc)):M("",!0)],10,Jc)]),t("div",e1,[t("div",{class:"relative w-11 h-11 cursor-pointer group transition-all duration-500 transform hover:scale-110 backdrop-blur-sm",onClick:b[1]||(b[1]=(...h)=>fe(a).toggleTheme&&fe(a).toggleTheme(...h)),title:d.value==="dark"?"切换到亮色主题":"切换到暗色主题"},[t("div",{class:P(["w-full h-full transition-all duration-700 transform rounded-2xl shadow-xl",d.value==="dark"?"bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-800 rotate-0":"bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rotate-180"])},null,2),t("div",{class:P(["absolute inset-1 rounded-xl flex items-center justify-center transition-all duration-500",d.value==="dark"?"bg-white/10 backdrop-blur-sm":"bg-white/20 backdrop-blur-sm"])},[t("div",r1,[d.value==="light"?(u(),g("svg",o1,b[6]||(b[6]=[t("path",{d:"M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z"},null,-1)]))):(u(),g("svg",s1,b[7]||(b[7]=[t("path",{d:"M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z"},null,-1)])))])],2),t("div",{class:P(["absolute inset-0 rounded-xl opacity-30 animate-pulse",d.value==="dark"?"bg-gradient-to-br from-indigo-400 to-purple-600":"bg-gradient-to-br from-yellow-300 to-orange-400"])},null,2)],8,t1)])]),b[10]||(b[10]=t("div",{class:"h-8 w-px bg-gradient-to-b from-transparent via-gray-300/60 dark:via-gray-600/60 to-transparent"},null,-1)),t("div",a1,[t("button",{onClick:b[2]||(b[2]=h=>r.value=!r.value),class:"flex items-center space-x-2 p-2.5 rounded-2xl text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-white/60 dark:hover:bg-gray-800/60 backdrop-blur-sm border border-transparent hover:border-gray-200/60 dark:hover:border-gray-700/60 transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-lg"},b[8]||(b[8]=[t("div",{class:"w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)])),r.value?F((u(),g("div",n1,[t("button",{onClick:s,class:"w-full flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100/60 dark:hover:bg-gray-700/60 transition-all duration-200 rounded-xl mx-1 backdrop-blur-sm"},b[9]||(b[9]=[t("div",{class:"p-1.5 bg-red-100 dark:bg-red-900/30 rounded-xl mr-3"},[t("svg",{class:"w-4 h-4 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})])],-1),t("span",{class:"font-medium"},"退出登录",-1)]))])),[[m,()=>r.value=!1]]):M("",!0)])])])])]))}},i1=ue(l1,[["__scopeId","data-v-0e621521"]]);let d1=0;const u1={name:"Toast",setup(){const o=O([]),e=d=>{const n="border backdrop-blur-sm";switch(d){case"success":return`${n} bg-green-50/90 dark:bg-green-900/90 border-green-200 dark:border-green-700 text-green-800 dark:text-green-200`;case"error":return`${n} bg-red-50/90 dark:bg-red-900/90 border-red-200 dark:border-red-700 text-red-800 dark:text-red-200`;case"warning":return`${n} bg-yellow-50/90 dark:bg-yellow-900/90 border-yellow-200 dark:border-yellow-700 text-yellow-800 dark:text-yellow-200`;default:return`${n} bg-blue-50/90 dark:bg-blue-900/90 border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-200`}},a=(d,n="info",i=3e3)=>{const p=++d1,s=ie({id:p,message:d,type:n,progress:100,showProgress:i>0});if(o.value.push(s),i>0){let m=0;const c=50,b=setInterval(()=>{m+=c,s.progress=Math.max(0,100-m/i*100),m>=i&&(clearInterval(b),r(p))},c);s.pauseTimer=()=>clearInterval(b),s.resumeTimer=()=>{}}return p},r=d=>{const n=o.value.findIndex(i=>i.id===d);n>-1&&o.value.splice(n,1)},l=()=>{o.value=[]};return window.showToast=a,{toasts:o,getToastClasses:e,addToast:a,removeToast:r,clearAllToasts:l}}},c1={class:"fixed top-4 right-4 z-50 space-y-2"},g1={class:"flex-shrink-0 mr-3"},m1={key:0,class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},f1={key:1,class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},v1={key:2,class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},p1={key:3,class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},b1={class:"flex-1 mr-2"},x1={class:"text-sm font-medium"},h1=["onClick"];function y1(o,e,a,r,l,d){return u(),Be(br,{to:"body"},[t("div",c1,[q(pr,{name:"toast",tag:"div",class:"space-y-2"},{default:K(()=>[(u(!0),g(ne,null,de(r.toasts,n=>(u(),g("div",{key:n.id,class:P([["flex items-center p-4 rounded-lg shadow-lg backdrop-blur-sm border transform transition-all duration-300",r.getToastClasses(n.type)],"max-w-sm"])},[t("div",g1,[n.type==="success"?(u(),g("svg",m1,e[0]||(e[0]=[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):n.type==="error"?(u(),g("svg",f1,e[1]||(e[1]=[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"},null,-1)]))):n.type==="warning"?(u(),g("svg",v1,e[2]||(e[2]=[t("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"},null,-1)]))):(u(),g("svg",p1,e[3]||(e[3]=[t("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z","clip-rule":"evenodd"},null,-1)])))]),t("div",b1,[t("p",x1,x(n.message),1)]),t("button",{onClick:i=>r.removeToast(n.id),class:"flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},e[4]||(e[4]=[t("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,h1),n.showProgress?(u(),g("div",{key:0,class:"absolute bottom-0 left-0 h-1 bg-current opacity-30 rounded-b-lg transition-all duration-100 ease-linear",style:xr({width:`${n.progress}%`})},null,4)):M("",!0)],2))),128))]),_:1})])])}const k1=ue(u1,[["render",y1],["__scopeId","data-v-ece7f6b0"]]),w1={class:"main-content"},_1={class:"p-6 bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-200"},C1={__name:"App",setup(o){const e=_e(),a=A(()=>e.theme),r=A({get:()=>e.isSidebarOpen,set:i=>e.isSidebarOpen=i}),l=A(()=>zo()),d=()=>{window.innerWidth<1024?e.isSidebarOpen=!1:e.isSidebarOpen=!0},n=async()=>{const i=[e.getStatus(),e.getLogs(),e.getSites(),e.getConfig()];Promise.allSettled(i).then(p=>{p.forEach((s,m)=>{s.status==="rejected"&&console.warn(`API ${["getStatus","getLogs","getSites","getConfig"][m]} 初始化失败:`,s.reason)})})};return we(async()=>{e.initTheme(),d(),window.addEventListener("resize",d),await dt(),n();const i=setInterval(()=>{Promise.allSettled([e.getStatus(),e.getLogs()]).catch(()=>{})},5e3);Ft(()=>{clearInterval(i),window.removeEventListener("resize",d)})}),(i,p)=>{const s=ye("RouterView");return u(),g("div",{id:"app",class:P([r.value?"":"sidebar-closed",a.value])},[q(Oc),t("div",w1,[q(i1),t("main",_1,[q(s)])]),r.value&&!l.value?(u(),g("div",{key:0,onClick:p[0]||(p[0]=m=>r.value=!1),class:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"})):M("",!0),q(k1)],2)}}},mt=hr(C1);mt.use(yr());mt.use(lr);mt.mount("#app");
