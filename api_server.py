#!/usr/bin/env python3
"""
集成监控管理API服务器

提供RESTful API接口用于管理WHMCS库存监控和LET RSS监控
"""

import asyncio
from contextlib import asynccontextmanager
from datetime import datetime
import logging
from pathlib import Path
import re

from fastapi import BackgroundTasks, Depends, FastAPI, Form, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from fastapi.security import APIKeyCookie
from fastapi.staticfiles import StaticFiles
from starlette.config import Config
import uvicorn

from advanced_notification_service import AdvancedNotificationService
from integrated_monitor import IntegratedMonitor
from notification_models import (
    NOTIFICATION_TYPE_GROUPS,
    NotificationChannelType,
    NotificationMessage,
    NotificationPriority,
    NotificationType,
    create_channel_config,
)
from whmcs.models import (
    ProductConfigRequest,
    WHMCSConfig,
    WHMCSConfigRequest,
)

# 全局变量
monitor: IntegratedMonitor | None = None
monitor_task: asyncio.Task | None = None
notification_service: AdvancedNotificationService | None = None

# --- Authentication ---
try:
    config = Config(".env")
    APP_PASSWORD = config("APP_PASSWORD", cast=str, default="default_password")
except Exception as e:
    logging.error(f"Error loading .env file: {e}")
    APP_PASSWORD = "default_password"

if APP_PASSWORD == "default_password":
    logging.warning("未设置APP_PASSWORD环境变量，使用默认密码'default_password'，请务必修改！")

COOKIE_NAME = "auth_session"
cookie_scheme = APIKeyCookie(name=COOKIE_NAME, auto_error=False)


async def get_current_user(cookie: str | None = Depends(cookie_scheme)):
    if cookie and cookie == APP_PASSWORD:
        return True
    return None


# --- End Authentication ---


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global monitor, monitor_task, notification_service

    # 启动时初始化
    try:
        # 先初始化通知服务
        notification_service = AdvancedNotificationService()
        await notification_service.__aenter__()
        logging.info("通知服务已初始化。")

        # 初始化集成监控器（它会使用同一个通知服务实例）
        monitor = IntegratedMonitor("data/integrated_config.json")
        # 将通知服务设置到监控器中
        monitor.notification_service = notification_service
        logging.info("集成监控API服务器启动成功，监控器已初始化。")

        # 自动启动监控
        if not monitor.is_running:
            logging.info("正在自动启动集成监控...")

            async def run_monitor():
                async with monitor:
                    await monitor.start_monitoring()

            monitor_task = asyncio.create_task(run_monitor())
            logging.info("集成监控任务已在后台启动。")
        else:
            logging.info("监控已经在运行中。")

    except Exception as e:
        logging.error(f"API服务器启动失败: {e}")

    yield  # 应用运行期间

    # 关闭时清理
    if monitor_task and not monitor_task.done():
        monitor.stop_monitoring()
        monitor_task.cancel()
        try:
            await monitor_task
        except asyncio.CancelledError:
            pass

    # 清理通知服务
    if notification_service:
        await notification_service.__aexit__(None, None, None)

    logging.info("API服务器已关闭")


app = FastAPI(title="集成监控管理系统", version="2.0.0", lifespan=lifespan)


@app.get("/login", response_class=HTMLResponse)
async def login_page():
    """提供登录页面"""
    return Path("static/login.html").read_text(encoding="utf-8")


@app.post("/api/login")
async def login(password: str = Form(...)):
    """处理登录请求"""
    if password == APP_PASSWORD:
        response = JSONResponse(content={"success": True, "message": "登录成功"})
        response.set_cookie(
            key=COOKIE_NAME,
            value=APP_PASSWORD,
            httponly=True,
            max_age=60 * 60 * 24 * 7,  # 7 days
            samesite="lax",
        )
        return response
    else:
        raise HTTPException(status_code=401, detail="密码错误")


@app.get("/api/logout")
async def logout():
    """处理登出请求"""
    response = RedirectResponse(url="/login")
    response.delete_cookie(COOKIE_NAME)
    return response


@app.get("/", response_class=HTMLResponse)
async def get_index(user: str | None = Depends(get_current_user)):
    """返回主页面，如果未登录则重定向到登录页"""
    if not user:
        return RedirectResponse(url="/login")

    html_file = Path("static/index.html")
    if html_file.exists():
        return HTMLResponse(content=html_file.read_text(encoding="utf-8"))
    else:
        return HTMLResponse(
            content="""
        <!DOCTYPE html>
        <html>
        <head><title>集成监控管理系统</title></head>
        <body>
            <h1>集成监控管理系统</h1>
            <p>请确保static/index.html文件存在</p>
        </body>
        </html>
        """
        )


# API路由
@app.get("/api/status")
async def get_status(user: bool = Depends(get_current_user)):
    """获取集成监控状态"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    status = monitor.get_status_summary()
    # 添加monitor_running字段以兼容前端
    status["monitor_running"] = status.get("is_running", False)

    # 添加LET监控相关状态
    if monitor.let_scheduler:
        let_stats = monitor.let_scheduler.get_statistics()
        status.update(
            {
                "let_monitor_running": monitor.let_scheduler.is_running
                if hasattr(monitor.let_scheduler, "is_running")
                else False,
                "let_processed_posts": let_stats.get("processed_posts", 0),
                "let_stored_posts": let_stats.get("stored_posts", 0),
                "let_data_directory": let_stats.get("data_directory", ""),
            }
        )

    return status


@app.get("/api/config")
async def get_config(user: bool = Depends(get_current_user)):
    """获取完整配置"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    config_dict = {}

    # WHMCS配置
    if monitor.stock_monitor and monitor.stock_monitor.config:
        config_dict["whmcs"] = monitor.stock_monitor.config.dict()

    # LET监控配置
    if monitor.let_scheduler and monitor.config.let_config:
        config_dict["let_monitor"] = {
            "proxy_url": monitor.config.let_config.proxy_url,
            "check_interval": monitor.config.let_config.check_interval,
            "data_dir": monitor.config.let_config.data_dir,
        }

    # 通知配置
    config_dict["notifications"] = {
        name: config.model_dump() for name, config in monitor.config.notification_configs.items()
    }

    return config_dict


# ===== LET 监控相关 API =====


@app.get("/api/let/status")
async def get_let_status(user: bool = Depends(get_current_user)):
    """获取LET监控状态"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    if not monitor.let_scheduler:
        return {"enabled": False, "message": "LET监控未启用"}

    stats = monitor.let_scheduler.get_statistics()

    # 检查调度器是否正在运行
    is_running = False
    if hasattr(monitor.let_scheduler, "running"):
        is_running = monitor.let_scheduler.running
    elif hasattr(monitor.let_scheduler, "scheduler") and hasattr(monitor.let_scheduler.scheduler, "running"):
        is_running = monitor.let_scheduler.scheduler.running
    else:
        # 通过检查最近是否有活动来判断
        data_dir = Path(monitor.config.let_config.data_dir if monitor.config.let_config else "data/let_posts")
        posts_dir = data_dir / "posts"
        if posts_dir.exists():
            recent_files = list(posts_dir.glob("*.json"))
            if recent_files:
                # 检查是否有最近5分钟内创建的文件
                import time

                current_time = time.time()
                for file_path in recent_files:
                    if current_time - file_path.stat().st_mtime < 300:  # 5分钟
                        is_running = True
                        break

    return {
        "enabled": True,
        "running": is_running,
        "processed_posts": stats.get("processed_posts", 0),
        "stored_posts": stats.get("stored_posts", 0),
        "data_directory": stats.get("data_directory", ""),
        "check_interval": monitor.config.let_config.check_interval if monitor.config.let_config else 300,
        "proxy_url": monitor.config.let_config.proxy_url if monitor.config.let_config else None,
    }


@app.get("/api/let/posts")
async def get_let_posts(limit: int = 50, user: bool = Depends(get_current_user)):
    """获取LET帖子列表"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor or not monitor.let_scheduler:
        return {"posts": [], "message": "LET监控未启用"}

    data_dir = Path(monitor.config.let_config.data_dir if monitor.config.let_config else "data/let_posts")
    posts_dir = data_dir / "posts"

    if not posts_dir.exists():
        return {"posts": [], "message": "帖子目录不存在"}

    posts = []
    # 查找所有JSON文件，包括新旧格式
    json_files = list(posts_dir.glob("*.json"))
    json_files = sorted(json_files, key=lambda x: x.stat().st_mtime, reverse=True)

    for file_path in json_files[:limit]:
        try:
            with open(file_path, encoding="utf-8") as f:
                import json

                post_data = json.load(f)

            # 从文件名提取信息，支持多种格式
            file_name = file_path.stem  # 移除.json后缀

            # 尝试提取帖子ID，支持新的命名格式
            if "_" in file_name:
                parts = file_name.split("_")
                if parts[-1] == "_discussions" and len(parts) >= 3:
                    # 旧格式: YYYYMMDD_HHMMSS_PostID__discussions
                    post_id = parts[-2]
                elif len(parts) >= 4 and parts[0].isdigit() and len(parts[0]) == 8:
                    # 新格式: YYYYMMDD_HHMM_Provider_Title_PostID.json
                    post_id = parts[-1]  # 最后一个部分是PostID
                elif len(parts) >= 3 and parts[0].isdigit() and len(parts[0]) == 8:
                    # 新格式: YYYYMMDD_HHMM_PostID.json 或 YYYYMMDD_HHMM_Provider_PostID.json
                    post_id = parts[-1]  # 最后一个部分是PostID
                else:
                    # 其他格式，假设第一个是PostID或最后一个是PostID
                    post_id = parts[-1] if len(parts) > 1 else parts[0]
            else:
                # 简单格式，整个文件名就是PostID
                post_id = file_name

            # 提取数据，兼容新旧格式
            if "rss_item" in post_data and "analysis" in post_data:
                # 新格式 (带RSS数据和分析结果)
                rss_item = post_data["rss_item"]
                analysis = post_data["analysis"]

                post_summary = {
                    "file_name": file_path.name,
                    "post_id": post_id,
                    "post_title": rss_item.get("title", analysis.get("post_title", "")),
                    "post_url": rss_item.get("link", analysis.get("post_url", "")),
                    "post_date": rss_item.get("pub_date", analysis.get("post_date", "")),
                    "provider_name": analysis.get("provider", {}).get("name", rss_item.get("creator", "")),
                    "provider_website": analysis.get("provider", {}).get("website", ""),
                    "product_count": len(analysis.get("products", [])),
                    "tags": analysis.get("tags", []),
                    "global_promo_code": analysis.get("global_promo_code", ""),
                    "category": rss_item.get("category", ""),
                    "created_time": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                }
            else:
                # 旧格式 (直接分析结果)
                post_summary = {
                    "file_name": file_path.name,
                    "post_id": post_id,
                    "post_title": post_data.get("post_title", ""),
                    "post_url": post_data.get("post_url", ""),
                    "post_date": post_data.get("post_date", ""),
                    "provider_name": post_data.get("provider", {}).get("name", ""),
                    "provider_website": post_data.get("provider", {}).get("website", ""),
                    "product_count": len(post_data.get("products", [])),
                    "tags": post_data.get("tags", []),
                    "global_promo_code": post_data.get("global_promo_code", ""),
                    "category": "",
                    "created_time": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                }

            posts.append(post_summary)

        except Exception as e:
            logging.error(f"读取LET帖子文件失败 {file_path}: {e}")
            continue

    return {"posts": posts, "total": len(json_files)}


@app.get("/api/let/posts/{post_id}")
async def get_let_post_detail(post_id: str, user: bool = Depends(get_current_user)):
    """获取LET帖子详情"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor or not monitor.let_scheduler:
        raise HTTPException(status_code=500, detail="LET监控未启用")

    data_dir = Path(monitor.config.let_config.data_dir if monitor.config.let_config else "data/let_posts")
    posts_dir = data_dir / "posts"

    # 尝试多种文件名格式
    possible_files = [
        posts_dir / f"{post_id}.json",  # 新简单格式
        posts_dir / f"{post_id}__discussions.json",  # 旧格式
        data_dir / f"{post_id}.json",  # 旧位置
    ]

    # 如果post_id包含文件扩展名，也尝试直接查找
    if post_id.endswith(".json") or post_id.endswith("__discussions"):
        possible_files.append(posts_dir / post_id)
        if not post_id.endswith(".json"):
            possible_files.append(posts_dir / f"{post_id}.json")

    # 通过模式匹配查找，支持新旧格式
    pattern_files = list(posts_dir.glob(f"{post_id}_*.json"))  # 老格式: PostID_Title.json
    pattern_files.extend(list(posts_dir.glob(f"*_{post_id}__discussions.json")))  # 旧格式
    pattern_files.extend(list(posts_dir.glob(f"*_{post_id}.json")))  # 新格式: 时间戳_供应商_标题_PostID.json
    possible_files.extend(pattern_files)

    post_file = None
    for file_path in possible_files:
        if file_path.exists():
            post_file = file_path
            break

    if not post_file:
        raise HTTPException(status_code=404, detail=f"帖子不存在: {post_id}")

    try:
        with open(post_file, encoding="utf-8") as f:
            import json

            post_data = json.load(f)
        return post_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取帖子详情失败: {e}")


@app.post("/api/let/config")
async def update_let_config(config: dict, user: bool = Depends(get_current_user)):
    """更新LET监控配置"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    try:
        # 更新LET配置
        if monitor.config.let_config:
            for key, value in config.items():
                if hasattr(monitor.config.let_config, key):
                    setattr(monitor.config.let_config, key, value)
        else:
            from let.scheduler import LETSchedulerConfig

            monitor.config.let_config = LETSchedulerConfig(**config)

        monitor.config.save_config(monitor.config_path)
        return {"message": "LET监控配置已更新"}

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"更新配置失败: {e!s}")


@app.post("/api/let/test-fetch")
async def test_let_fetch(user: bool = Depends(get_current_user)):
    """测试LET RSS获取"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    try:
        from let.rss_fetcher import fetch_let_rss

        proxy_url = monitor.config.let_config.proxy_url if monitor.config.let_config else None
        items = await fetch_let_rss(proxy_url=proxy_url)

        return {
            "success": True,
            "message": f"成功获取 {len(items)} 个RSS项目",
            "items_count": len(items),
            "latest_items": [
                {"title": item.title, "creator": item.creator, "pub_date": item.pub_date, "link": item.link}
                for item in items[:5]  # 只返回前5个作为示例
            ],
        }

    except Exception as e:
        return {"success": False, "message": f"RSS获取失败: {e!s}"}


@app.get("/api/sites")
async def get_sites(user: bool = Depends(get_current_user)):
    """获取所有WHMCS网站"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    if not monitor.stock_monitor or not monitor.stock_monitor.config:
        return {}

    sites = {}
    for site_id, site_config in monitor.stock_monitor.config.whmcs_sites.items():
        # 计算启用产品数量
        enabled_products = sum(1 for product in site_config.products.values() if product.enabled)
        # 计算自动下单产品数量
        auto_order_products = sum(1 for product in site_config.products.values() if product.auto_order_enabled)

        sites[site_id] = {
            "base_url": str(site_config.base_url),
            "username": site_config.username,
            "hostname_base": site_config.hostname_base,
            "payment_method": site_config.payment_method,
            "cookies_string": site_config.cookies_string,
            "use_cfbypass": getattr(site_config, 'use_cfbypass', False),
            "cfbypass_retries": getattr(site_config, 'cfbypass_retries', 3),
            "product_count": len(site_config.products),
            "enabled_products": enabled_products,
            "auto_order_products": auto_order_products,
        }

    return sites


@app.post("/api/sites/{site_id}")
async def add_site(
    site_id: str,
    site_config: WHMCSConfigRequest,
    user: bool = Depends(get_current_user),
):
    """添加或更新WHMCS网站"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    try:
        if not monitor.stock_monitor:
            raise HTTPException(status_code=500, detail="WHMCS监控器未初始化")

        if site_id in monitor.stock_monitor.config.whmcs_sites:
            existing_config = monitor.stock_monitor.config.whmcs_sites[site_id]
            update_data = site_config.model_dump(exclude_none=True)

            updated_config = existing_config.model_copy(update=update_data)
            monitor.stock_monitor.config.whmcs_sites[site_id] = updated_config
            message = f"网站 {site_id} 已更新"
        else:
            if not site_config.password or not site_config.rootpw:
                raise HTTPException(
                    status_code=422,
                    detail=[
                        {
                            "loc": ["body", "password"],
                            "msg": "创建新网站时必须提供密码和root密码",
                        }
                    ],
                )

            new_config = WHMCSConfig(**site_config.dict(), products={})
            monitor.stock_monitor.config.whmcs_sites[site_id] = new_config
            message = f"网站 {site_id} 已添加"

        monitor.stock_monitor.save_config()
        return {"message": message, "site_id": site_id}

    except Exception as e:
        from pydantic import ValidationError

        if isinstance(e, ValidationError):
            raise HTTPException(status_code=422, detail=e.errors())
        raise HTTPException(status_code=400, detail=f"操作网站失败: {e!s}")


@app.delete("/api/sites/{site_id}")
async def delete_site(site_id: str, user: bool = Depends(get_current_user)):
    """删除WHMCS网站"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    if not monitor.stock_monitor:
        raise HTTPException(status_code=500, detail="WHMCS监控器未初始化")

    if site_id not in monitor.stock_monitor.config.whmcs_sites:
        raise HTTPException(status_code=404, detail="网站不存在")

    site_name = monitor.stock_monitor.config.whmcs_sites[site_id].base_url
    del monitor.stock_monitor.config.whmcs_sites[site_id]
    monitor.stock_monitor.save_config()

    return {"message": f"网站 {site_id} ({site_name}) 已删除"}


@app.get("/api/sites/{site_id}/products")
async def get_products(site_id: str, user: bool = Depends(get_current_user)):
    """获取指定网站的所有产品"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    if not monitor.stock_monitor:
        raise HTTPException(status_code=500, detail="WHMCS监控器未初始化")

    if site_id not in monitor.stock_monitor.config.whmcs_sites:
        raise HTTPException(status_code=404, detail="网站不存在")

    products = {}
    for product_id, product in monitor.stock_monitor.config.whmcs_sites[site_id].products.items():
        product_data = {
            "name": product.name,
            "product_type": getattr(product, "product_type", "traditional"),
            "enabled": product.enabled,
            "auto_order_enabled": product.auto_order_enabled,
            "max_order_retries": product.max_order_retries,
            "order_failure_count": product.order_failure_count,
            "last_order_attempt": product.last_order_attempt.isoformat() if product.last_order_attempt else None,
        }

        # 传统产品字段
        if getattr(product, "product_type", "traditional") == "traditional":
            product_data.update(
                {
                    "url": str(product.url) if product.url else None,
                    "promo_code": product.promo_code,
                    "billing_cycle": product.billing_cycle,
                    "status": product.status,
                    "in_stock_count": product.in_stock_count,
                    "order_attempted": product.order_attempted,
                    "last_check": product.last_check.isoformat() if product.last_check else None,
                }
            )
        else:
            # LET智能产品字段
            product_data.update(
                {
                    "description": getattr(product, "description", None),
                    "let_match_criteria": getattr(product, "let_match_criteria", None),
                    "last_let_match_count": getattr(product, "last_let_match_count", 0),
                    "total_let_matches": getattr(product, "total_let_matches", 0),
                    "successful_let_orders": getattr(product, "successful_let_orders", 0),
                    "last_let_match_time": getattr(product, "last_let_match_time", None),
                    "billing_cycle": getattr(product, "billing_cycle", "monthly"),
                }
            )

            if product_data["last_let_match_time"]:
                product_data["last_let_match_time"] = product_data["last_let_match_time"].isoformat()

        products[product_id] = product_data

    return products


@app.post("/api/sites/{site_id}/products/{product_id}")
async def add_product(
    site_id: str,
    product_id: str,
    product_config: ProductConfigRequest,
    user: bool = Depends(get_current_user),
):
    """添加或更新产品"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    if not monitor.stock_monitor or site_id not in monitor.stock_monitor.config.whmcs_sites:
        raise HTTPException(status_code=404, detail="网站不存在")

    try:
        if not monitor.stock_monitor:
            raise HTTPException(status_code=500, detail="WHMCS监控器未初始化")

        # 处理不同的产品类型
        product_data = product_config.dict()

        if product_config.product_type == "let_smart":
            # LET智能产品处理
            # 转换匹配条件格式
            if product_config.let_match_criteria:
                from whmcs.models import LETMatchCriteriaConfig

                criteria_config = LETMatchCriteriaConfig(**product_config.let_match_criteria.dict())
                product_data["let_match_criteria"] = criteria_config.dict()

            # 初始化LET相关统计字段
            product_data.update(
                {
                    "last_let_match_count": 0,
                    "total_let_matches": 0,
                    "successful_let_orders": 0,
                    "last_let_match_time": None,
                }
            )
        elif product_config.product_type == "traditional":
            # 传统产品处理 - 确保必需字段存在
            if not product_config.url:
                raise HTTPException(status_code=422, detail="传统产品类型必须提供URL")

        await monitor.stock_monitor.add_product(site_id, product_id, product_data)
        return {
            "message": f"产品 {product_id} 已保存",
            "product_id": product_id,
            "product_type": product_config.product_type,
        }

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"添加产品失败: {e!s}")


@app.delete("/api/sites/{site_id}/products/{product_id}")
async def delete_product(site_id: str, product_id: str, user: bool = Depends(get_current_user)):
    """删除产品"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    if not monitor.stock_monitor:
        raise HTTPException(status_code=500, detail="WHMCS监控器未初始化")

    if (
        site_id not in monitor.stock_monitor.config.whmcs_sites
        or product_id not in monitor.stock_monitor.config.whmcs_sites[site_id].products
    ):
        raise HTTPException(status_code=404, detail="产品不存在")

    await monitor.stock_monitor.remove_product(site_id, product_id)
    return {"message": f"产品 {product_id} 已删除"}


@app.post("/api/sites/{site_id}/products/{product_id}/toggle")
async def toggle_product(site_id: str, product_id: str, user: bool = Depends(get_current_user)):
    """切换产品启用状态"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    if not monitor.stock_monitor:
        raise HTTPException(status_code=500, detail="WHMCS监控器未初始化")

    if (
        site_id not in monitor.stock_monitor.config.whmcs_sites
        or product_id not in monitor.stock_monitor.config.whmcs_sites[site_id].products
    ):
        raise HTTPException(status_code=404, detail="产品不存在")

    product = monitor.stock_monitor.config.whmcs_sites[site_id].products[product_id]
    product.enabled = not product.enabled
    monitor.stock_monitor.save_config()

    status = "启用" if product.enabled else "禁用"
    return {"message": f"产品 {product_id} 已{status}", "enabled": product.enabled}


@app.post("/api/sites/{site_id}/products/{product_id}/toggle-auto-order")
async def toggle_product_auto_order(site_id: str, product_id: str, user: bool = Depends(get_current_user)):
    """切换产品自动下单状态"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    if not monitor.stock_monitor:
        raise HTTPException(status_code=500, detail="WHMCS监控器未初始化")

    if (
        site_id not in monitor.stock_monitor.config.whmcs_sites
        or product_id not in monitor.stock_monitor.config.whmcs_sites[site_id].products
    ):
        raise HTTPException(status_code=404, detail="产品不存在")

    product = monitor.stock_monitor.config.whmcs_sites[site_id].products[product_id]
    product.auto_order_enabled = not product.auto_order_enabled
    monitor.stock_monitor.save_config()

    status = "启用" if product.auto_order_enabled else "禁用"
    return {
        "message": f"产品 {product_id} 的自动下单已{status}",
        "auto_order_enabled": product.auto_order_enabled,
    }


@app.post("/api/sites/{site_id}/products/{product_id}/reset-failures")
async def reset_product_failures(site_id: str, product_id: str, user: bool = Depends(get_current_user)):
    """重置产品的下单失败计数"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    if not monitor.stock_monitor:
        raise HTTPException(status_code=500, detail="WHMCS监控器未初始化")

    if (
        site_id not in monitor.stock_monitor.config.whmcs_sites
        or product_id not in monitor.stock_monitor.config.whmcs_sites[site_id].products
    ):
        raise HTTPException(status_code=404, detail="产品不存在")

    product = monitor.stock_monitor.config.whmcs_sites[site_id].products[product_id]
    old_failure_count = product.order_failure_count

    # 重置失败相关字段
    product.order_failure_count = 0
    product.order_attempted = False
    product.last_order_attempt = None

    # 如果之前因为失败次数过多而禁用了自动下单，重新启用
    if not product.auto_order_enabled and old_failure_count >= product.max_order_retries:
        product.auto_order_enabled = True

    monitor.stock_monitor.save_config()

    return {
        "message": f"产品 {product_id} 的失败计数已重置 (之前失败 {old_failure_count} 次)",
        "order_failure_count": product.order_failure_count,
        "order_attempted": product.order_attempted,
        "auto_order_enabled": product.auto_order_enabled,
    }


@app.post("/api/monitor/start")
async def start_monitor(background_tasks: BackgroundTasks, user: bool = Depends(get_current_user)):
    """启动监控"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor, monitor_task

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    if monitor.is_running:
        return {"message": "监控已在运行中"}

    # 在后台任务中启动监控
    async def run_monitor():
        async with monitor:
            await monitor.start_monitoring()

    monitor_task = asyncio.create_task(run_monitor())

    return {"message": "监控已启动"}


@app.post("/api/monitor/stop")
async def stop_monitor(user: bool = Depends(get_current_user)):
    """停止监控"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor, monitor_task

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    if not monitor.is_running:
        return {"message": "监控未在运行"}

    monitor.stop_monitoring()

    if monitor_task and not monitor_task.done():
        monitor_task.cancel()
        try:
            await monitor_task
        except asyncio.CancelledError:
            pass

    return {"message": "监控已停止"}


@app.get("/api/logs")
async def get_logs(lines: int = 100, user: bool = Depends(get_current_user)):
    """获取最近的日志"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    log_file = Path("data/logs/stock_monitor.log")

    # 正则表达式用于匹配和移除ANSI颜色代码
    ansi_escape = re.compile(r"\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])")

    if not log_file.exists():
        return {"logs": [], "message": "日志文件不存在"}

    try:
        with open(log_file, encoding="utf-8") as f:
            all_lines = f.readlines()

        # 获取最后N行
        recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

        logs = []
        for line in recent_lines:
            # 移除ANSI颜色代码
            clean_line = ansi_escape.sub("", line).strip()

            if clean_line:
                # 使用更健壮的正则表达式解析日志
                # 格式: 2025-06-20 03:22:20,028 - stock_monitor - INFO - ...
                match = re.match(
                    r"^(?P<timestamp>[\d\-:,\s]+) - (?P<name>[\w\.]+) - (?P<level>\w+) - (?P<message>.*)$",
                    clean_line,
                )
                if match:
                    log_data = match.groupdict()
                    # 格式化时间戳为ISO格式
                    try:
                        timestamp_str = log_data["timestamp"].strip()
                        # 将逗号替换为点，便于解析毫秒
                        if "," in timestamp_str:
                            timestamp_str = timestamp_str.replace(",", ".")
                        # 转换为ISO格式
                        dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S.%f")
                        iso_timestamp = dt.isoformat()
                    except ValueError:
                        # 如果解析失败，使用原始时间戳
                        iso_timestamp = log_data["timestamp"].strip()

                    logs.append(
                        {
                            "timestamp": iso_timestamp,
                            "level": log_data["level"].strip(),
                            "message": log_data["message"].strip(),
                        }
                    )
                else:
                    # 如果无法解析，则作为普通消息处理
                    logs.append(
                        {
                            "timestamp": datetime.now().isoformat(),
                            "level": "INFO",
                            "message": clean_line,
                        }
                    )

        return {"logs": logs, "total": len(logs)}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取日志失败: {e!s}")


@app.post("/api/config/notification")
async def update_notification_config(config: dict, user: bool = Depends(get_current_user)):
    """更新通知配置"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    try:
        # 更新主通知配置
        if "main" in monitor.config.notification_configs:
            for key, value in config.items():
                if hasattr(monitor.config.notification_configs["main"], key):
                    setattr(monitor.config.notification_configs["main"], key, value)

        # 同时更新WHMCS监控器的通知配置
        if monitor.stock_monitor and monitor.stock_monitor.config:
            for key, value in config.items():
                if hasattr(monitor.stock_monitor.config.notification, key):
                    setattr(monitor.stock_monitor.config.notification, key, value)
            monitor.stock_monitor.save_config()

        monitor.config.save_config()
        return {"message": "通知配置已更新"}

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"更新配置失败: {e!s}")


@app.post("/api/config/monitor")
async def update_monitor_config(config: dict, user: bool = Depends(get_current_user)):
    """更新监控配置"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    try:
        # 更新WHMCS监控配置
        if monitor.stock_monitor and monitor.stock_monitor.config:
            for key, value in config.items():
                if hasattr(monitor.stock_monitor.config.monitor, key):
                    setattr(monitor.stock_monitor.config.monitor, key, value)
            monitor.stock_monitor.save_config()

        return {"message": "监控配置已更新"}

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"更新配置失败: {e!s}")


@app.get("/api/test-login/{site_id}")
async def test_login(site_id: str, user: bool = Depends(get_current_user)):
    """测试WHMCS登录"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    global monitor

    if not monitor:
        raise HTTPException(status_code=500, detail="监控器未初始化")

    if not monitor.stock_monitor:
        raise HTTPException(status_code=500, detail="WHMCS监控器未初始化")

    if site_id not in monitor.stock_monitor.config.whmcs_sites:
        raise HTTPException(status_code=404, detail="网站不存在")

    try:
        from whmcs.core import login_only

        site = monitor.stock_monitor.config.whmcs_sites[site_id]

        # 支持使用cookies_string进行测试
        success = await login_only(
            str(site.base_url),
            site.username,
            site.password,
            cookies_string=site.cookies_string,
            use_cfbypass=getattr(site, 'use_cfbypass', False),
            cfbypass_retries=getattr(site, 'cfbypass_retries', 3),
        )

        if success:
            return {"success": True, "message": "登录测试成功"}
        else:
            return {"success": False, "message": "登录测试失败"}

    except Exception as e:
        return {"success": False, "message": f"登录测试异常: {e!s}"}


# ===============================
# 通知渠道管理 API
# ===============================


@app.get("/api/notifications/channels")
async def get_notification_channels(user: bool = Depends(get_current_user)):
    """获取所有通知渠道"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    global notification_service
    if not notification_service:
        raise HTTPException(status_code=500, detail="通知服务未初始化")

    try:
        channels = notification_service.get_channels()
        return {
            "channels": [
                {
                    "id": channel.id,
                    "name": channel.name,
                    "channel_type": channel.channel_type.value,
                    "enabled": channel.enabled,
                    "notification_types": list(channel.notification_types),
                    "min_priority": channel.min_priority.value,
                    "rate_limit": channel.rate_limit,
                    "quiet_hours": channel.quiet_hours,
                    "retry_count": channel.retry_count,
                    "timeout": channel.timeout,
                    "config": channel.config,
                }
                for channel in channels
            ],
            "total": len(channels),
            "notification_types": [t.value for t in NotificationType],
            "channel_types": [t.value for t in NotificationChannelType],
            "notification_type_groups": NOTIFICATION_TYPE_GROUPS,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取通知渠道失败: {e}")


@app.post("/api/notifications/channels")
async def create_notification_channel(request: dict, user: bool = Depends(get_current_user)):
    """创建新的通知渠道"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    global notification_service
    if not notification_service:
        raise HTTPException(status_code=500, detail="通知服务未初始化")

    try:
        # 验证必需字段
        if "name" not in request or "channel_type" not in request:
            raise HTTPException(status_code=400, detail="缺少必需字段: name, channel_type")

        # 创建渠道配置
        channel_type = NotificationChannelType(request["channel_type"])
        channel = create_channel_config(
            channel_type=channel_type,
            name=request["name"],
            enabled=request.get("enabled", True),
            notification_types={NotificationType(t) for t in request.get("notification_types", [])},
            min_priority=NotificationPriority(request.get("min_priority", "low")),
            config=request.get("config", {}),
            rate_limit=request.get("rate_limit"),
            quiet_hours=request.get("quiet_hours"),
            retry_count=request.get("retry_count", 3),
            timeout=request.get("timeout", 30),
            message_template=request.get("message_template"),
        )

        channel_id = notification_service.add_channel(channel)
        return {"success": True, "channel_id": channel_id, "message": "通知渠道创建成功"}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"无效的参数: {e}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建通知渠道失败: {e}")


@app.put("/api/notifications/channels/{channel_id}")
async def update_notification_channel(channel_id: str, request: dict, user: bool = Depends(get_current_user)):
    """更新通知渠道"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    global notification_service
    if not notification_service:
        raise HTTPException(status_code=500, detail="通知服务未初始化")

    try:
        # 检查渠道是否存在
        channel = notification_service.get_channel(channel_id)
        if not channel:
            raise HTTPException(status_code=404, detail="通知渠道不存在")

        # 准备更新数据
        updates = {}
        if "name" in request:
            updates["name"] = request["name"]
        if "enabled" in request:
            updates["enabled"] = request["enabled"]
        if "notification_types" in request:
            updates["notification_types"] = {NotificationType(t) for t in request["notification_types"]}
        if "min_priority" in request:
            updates["min_priority"] = NotificationPriority(request["min_priority"])
        if "config" in request:
            updates["config"] = request["config"]
        if "rate_limit" in request:
            updates["rate_limit"] = request["rate_limit"]
        if "quiet_hours" in request:
            updates["quiet_hours"] = request["quiet_hours"]
        if "retry_count" in request:
            updates["retry_count"] = request["retry_count"]
        if "timeout" in request:
            updates["timeout"] = request["timeout"]
        if "message_template" in request:
            updates["message_template"] = request["message_template"]

        success = notification_service.update_channel(channel_id, updates)
        if success:
            return {"success": True, "message": "通知渠道更新成功"}
        else:
            raise HTTPException(status_code=500, detail="更新通知渠道失败")

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"无效的参数: {e}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新通知渠道失败: {e}")


@app.delete("/api/notifications/channels/{channel_id}")
async def delete_notification_channel(channel_id: str, user: bool = Depends(get_current_user)):
    """删除通知渠道"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    global notification_service
    if not notification_service:
        raise HTTPException(status_code=500, detail="通知服务未初始化")

    try:
        success = notification_service.remove_channel(channel_id)
        if success:
            return {"success": True, "message": "通知渠道删除成功"}
        else:
            raise HTTPException(status_code=404, detail="通知渠道不存在")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除通知渠道失败: {e}")


@app.post("/api/notifications/channels/{channel_id}/test")
async def test_notification_channel(channel_id: str, user: bool = Depends(get_current_user)):
    """测试通知渠道"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    global notification_service
    if not notification_service:
        raise HTTPException(status_code=500, detail="通知服务未初始化")

    try:
        success = await notification_service.test_channel(channel_id)
        if success:
            return {"success": True, "message": "测试消息发送成功"}
        else:
            return {"success": False, "message": "测试消息发送失败"}

    except Exception as e:
        return {"success": False, "message": f"测试失败: {e}"}


@app.post("/api/notifications/send")
async def send_notification(request: dict, user: bool = Depends(get_current_user)):
    """发送自定义通知"""
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    global notification_service
    if not notification_service:
        raise HTTPException(status_code=500, detail="通知服务未初始化")

    try:
        # 验证必需字段
        if "title" not in request or "content" not in request:
            raise HTTPException(status_code=400, detail="缺少必需字段: title, content")

        # 创建通知消息
        message = NotificationMessage(
            title=request["title"],
            content=request["content"],
            notification_type=NotificationType(request.get("notification_type", "custom")),
            priority=NotificationPriority(request.get("priority", "normal")),
            source=request.get("source", "manual"),
            tags=request.get("tags", []),
            extra_data=request.get("extra_data", {}),
            format_markdown=request.get("format_markdown", True),
        )

        # 发送通知
        target_channels = request.get("target_channels")
        results = await notification_service.send_notification(message, target_channels)

        successful_channels = [ch_id for ch_id, success in results.items() if success]
        failed_channels = [ch_id for ch_id, success in results.items() if not success]

        return {
            "success": len(successful_channels) > 0,
            "message": f"通知发送完成，成功: {len(successful_channels)}，失败: {len(failed_channels)}",
            "results": results,
            "successful_channels": successful_channels,
            "failed_channels": failed_channels,
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"无效的参数: {e}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送通知失败: {e}")


# ===============================
# 静态文件服务 - 必须在所有API路由之后
# ===============================

# 挂载前端资源文件
app.mount("/assets", StaticFiles(directory="static/assets"), name="assets")


# SPA fallback处理 - 处理Vue Router的history模式
@app.get("/{full_path:path}")
async def spa_fallback(full_path: str):
    """
    SPA fallback处理：
    - 如果是API请求或已知静态文件，直接404
    - 其他所有路径都返回index.html，让Vue Router处理
    """
    # 检查是否是API请求
    if full_path.startswith("api/"):
        raise HTTPException(status_code=404, detail="API endpoint not found")

    # 检查是否是静态资源请求
    if full_path.startswith("assets/"):
        raise HTTPException(status_code=404, detail="Static file not found")

    # 检查是否是已知的静态文件扩展名
    static_extensions = {
        ".js",
        ".css",
        ".ico",
        ".png",
        ".jpg",
        ".svg",
        ".woff",
        ".woff2",
        ".ttf",
    }
    if any(full_path.endswith(ext) for ext in static_extensions):
        raise HTTPException(status_code=404, detail="Static file not found")

    # 对于所有其他路径，返回index.html让Vue Router处理
    html_file = Path("static/index.html")
    if html_file.exists():
        return HTMLResponse(content=html_file.read_text(encoding="utf-8"))
    else:
        raise HTTPException(status_code=500, detail="Frontend application not found")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="集成监控管理系统API服务器")
    parser.add_argument("--host", default="127.0.0.1", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="启用自动重载")

    args = parser.parse_args()

    print(f"🚀 启动API服务器: http://{args.host}:{args.port}")
    print(f"📋 管理界面: http://{args.host}:{args.port}")
    print(f"📚 API文档: http://{args.host}:{args.port}/docs")

    uvicorn.run("api_server:app", host=args.host, port=args.port, reload=args.reload)
