#!/usr/bin/env python3
"""
LET RSS 定时任务调度器
集成RSS获取、AI分析和数据存储的完整流程
"""

import asyncio
from datetime import datetime, timedelta, timezone
import json
import logging
import os
from pathlib import Path

from pydantic import BaseModel

from .agent import get_let_analyzer
from .integration_service import process_let_post_for_integration
from .models import LETPostAnalysis
from .rss_fetcher import RSSFetcher, RSSFetcherConfig, RSSItem


class LETSchedulerConfig(BaseModel):
    """调度器配置"""

    rss_url: str = "https://lowendtalk.com/categories/offers/feed.rss"
    proxy_url: str | None = None
    check_interval: int = 300  # 5分钟
    data_dir: str = "data/let_posts"
    max_concurrent_analysis: int = 3

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        Path(self.data_dir).mkdir(parents=True, exist_ok=True)

    @property
    def processed_file(self) -> Path:
        """已处理帖子记录文件"""
        return Path(self.data_dir) / "processed_posts.json"

    @property
    def posts_dir(self) -> Path:
        """帖子数据存储目录"""
        posts_dir = Path(self.data_dir) / "posts"
        posts_dir.mkdir(exist_ok=True)
        return posts_dir


class ProcessedPostsTracker:
    """已处理帖子追踪器，用于去重"""

    def __init__(self, file_path: Path):
        self.file_path = file_path
        self._processed: set[str] = set()
        self.load()

    def load(self):
        """加载已处理的帖子ID"""
        if self.file_path.exists():
            try:
                with open(self.file_path, encoding="utf-8") as f:
                    data = json.load(f)
                    self._processed = set(data.get("processed_guids", []))
            except Exception as e:
                logging.warning(f"加载已处理帖子记录失败: {e}")

    def save(self):
        """保存已处理的帖子ID"""
        try:
            data = {
                "processed_guids": list(self._processed),
                "last_update": datetime.now(timezone.utc).isoformat(),
            }
            with open(self.file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"保存已处理帖子记录失败: {e}")

    def is_processed(self, guid: str) -> bool:
        """检查帖子是否已处理"""
        return guid in self._processed

    def mark_processed(self, guid: str):
        """标记帖子为已处理"""
        self._processed.add(guid)
        self.save()

    def get_processed_count(self) -> int:
        """获取已处理帖子数量"""
        return len(self._processed)


class LETScheduler:
    """LET RSS 定时调度器"""

    def __init__(self, config: LETSchedulerConfig | None = None):
        self.config = config or LETSchedulerConfig()
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        self.tracker = ProcessedPostsTracker(self.config.processed_file)
        self.running = False

        # 通知回调函数
        self.notification_callback = None

        # 集成监控器实例（由外部设置）
        self.integrated_monitor = None

        # 初始化RSS获取器
        rss_config = RSSFetcherConfig(rss_url=self.config.rss_url, proxy_url=self.config.proxy_url)
        self.rss_fetcher = RSSFetcher(rss_config)

        # 信号量控制并发分析数量
        self._analysis_semaphore = asyncio.Semaphore(self.config.max_concurrent_analysis)

    def setup_logging(self):
        """设置日志配置"""
        log_dir = Path("data/logs")
        log_dir.mkdir(parents=True, exist_ok=True)

        # 为文件和控制台定义不同的格式
        file_log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        console_log_format = "\033[36m%(asctime)s\033[0m - %(name)s - \033[1;33m%(levelname)s\033[0m - %(message)s"

        logger = logging.getLogger(__name__)

        # 防止重复添加handler
        if not logger.handlers:
            logger.setLevel(logging.INFO)
            logger.propagate = False  # 防止日志向上传播到root logger

            # 创建文件handler (无颜色)
            file_handler = logging.FileHandler(log_dir / "let_scheduler.log", encoding="utf-8")
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(logging.Formatter(file_log_format))

            # 创建控制台handler (有颜色)
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(logging.Formatter(console_log_format))

            logger.addHandler(file_handler)
            logger.addHandler(console_handler)

    def set_notification_callback(self, callback):
        """设置通知回调函数"""
        self.notification_callback = callback

    async def start(self):
        """启动调度器"""
        self.running = True
        self.logger.info("LET 调度器启动")
        self.logger.info(f"检查间隔: {self.config.check_interval}秒")
        self.logger.info(f"数据目录: {self.config.data_dir}")
        self.logger.info(f"已处理帖子数: {self.tracker.get_processed_count()}")

        while self.running:
            try:
                self.logger.info("开始检查RSS数据...")
                await self._check_and_process()
                await asyncio.sleep(self.config.check_interval)
            except Exception as e:
                self.logger.error(f"调度器运行出错: {e}")
                await asyncio.sleep(30)  # 出错时短暂等待

    def stop(self):
        """停止调度器"""
        self.running = False
        self.logger.info("LET 调度器停止")

    async def _check_and_process(self):
        """检查RSS并处理新帖子"""
        try:
            # 获取RSS数据
            self.logger.info("开始获取RSS数据...")
            rss_items = await self.rss_fetcher.fetch_rss()
            self.logger.info(f"获取到 {len(rss_items)} 个RSS项目")
            # 筛选新帖子
            new_items = [item for item in rss_items if not self.tracker.is_processed(item.guid)]
            # 48小时内的帖子
            new_items = [item for item in new_items if item.pub_date > datetime.now(timezone.utc) - timedelta(hours=48)]
            if not new_items:
                self.logger.info("没有发现新帖子")
                return

            self.logger.info(f"发现 {len(new_items)} 个新帖子，开始处理...")

            # 并发处理新帖子
            tasks = [self._process_item(item) for item in new_items]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计处理结果
            success_count = sum(1 for r in results if r is True)
            error_count = len(results) - success_count

            self.logger.info(f"处理完成: 成功 {success_count}, 失败 {error_count}")

        except Exception as e:
            self.logger.error(f"RSS检查处理失败: {e}")

    async def _process_item(self, item: RSSItem) -> bool:
        """处理单个RSS项目"""
        async with self._analysis_semaphore:
            try:
                self.logger.info(f"开始分析帖子: {item.title}")

                # 使用AI分析帖子内容
                analyzer = get_let_analyzer()
                analysis_input = f"""
帖子标题: {item.title}
帖子链接: {item.link}
发布日期: {item.pub_date}
作者: {item.creator}
内容: {item.description}
"""

                analysis: LETPostAnalysis = (await analyzer.run(analysis_input)).output

                # 补充帖子基本信息
                analysis.post_url = item.link
                analysis.post_date = item.pub_date.isoformat()

                # 保存分析结果
                await self._save_analysis(item, analysis)

                # 集成LET-WHMCS服务处理
                await self._process_let_integration(analysis)

                # 发送通知（如果有回调函数且检测到优惠内容）
                if self.notification_callback and self._should_notify(analysis):
                    try:
                        # 格式化为Telegram消息
                        message = analysis.format_for_telegram()
                        title = f"🎯 LET新优惠: {analysis.provider.name}"
                        await self.notification_callback(message, title)
                    except Exception as e:
                        self.logger.error(f"发送通知失败: {e}")

                # 标记为已处理
                self.tracker.mark_processed(item.guid)

                self.logger.info(f"成功处理帖子: {item.title}")
                return True

            except Exception as e:
                self.logger.error(f"处理帖子失败 {item.title}: {e}")
                return False

    def _should_notify(self, analysis: LETPostAnalysis) -> bool:
        """判断是否应该发送通知"""
        return True
        # 检查是否为优惠帖子
        if not analysis.is_offer:
            return False

        # 检查是否有产品信息
        if not analysis.products:
            return False

        # 检查是否有合理的价格信息
        for product in analysis.products:
            if product.pricing.price and product.pricing.price > 0:
                return True

        # 检查是否有优惠码
        if analysis.global_promo_code:
            return True

        return False

    async def _save_analysis(self, item: RSSItem, analysis: LETPostAnalysis):
        """保存分析结果到文件"""
        # 从GUID中提取帖子ID (格式通常为 "postid@/discussions")
        post_id = item.guid.split("@")[0] if "@" in item.guid else item.guid

        # 生成友好的文件名，包含供应商和时间信息
        provider_name = analysis.provider.name if analysis.provider and analysis.provider.name else "Unknown"
        # 清理供应商名称
        safe_provider = "".join(c for c in provider_name[:20] if c.isalnum() or c in "-_").strip()

        # 清理标题
        safe_title = "".join(c for c in analysis.post_title[:30] if c.isalnum() or c in " -_").rstrip()
        safe_title = safe_title.replace(" ", "_")

        # 生成时间戳
        timestamp = item.pub_date.strftime("%Y%m%d_%H%M")

        # 构建文件名: 时间戳_供应商_标题_PostID.json
        if safe_provider and safe_title:
            filename = f"{timestamp}_{safe_provider}_{safe_title}_{post_id}.json"
        elif safe_provider:
            filename = f"{timestamp}_{safe_provider}_{post_id}.json"
        elif safe_title:
            filename = f"{timestamp}_{safe_title}_{post_id}.json"
        else:
            filename = f"{timestamp}_{post_id}.json"

        file_path = self.config.posts_dir / filename

        # 如果文件已存在，添加序号避免覆盖
        counter = 1
        original_path = file_path
        while file_path.exists():
            stem = original_path.stem
            suffix = original_path.suffix
            file_path = original_path.parent / f"{stem}_{counter}{suffix}"
            counter += 1

        # 构建保存数据
        save_data = {
            "rss_item": {
                "title": item.title,
                "link": item.link,
                "pub_date": item.pub_date.isoformat(),
                "category": item.category,
                "creator": item.creator,
                "guid": item.guid,
                "description_length": len(item.description),
            },
            "analysis": analysis.model_dump(),
            "processed_at": datetime.now(timezone.utc).isoformat(),
        }

        # 保存到文件
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)

        self.logger.debug(f"分析结果已保存: {file_path}")

    async def _process_let_integration(self, analysis: LETPostAnalysis):
        """处理LET-WHMCS集成"""
        try:
            # 只有优惠帖子才进行集成处理
            if not analysis.is_offer or not analysis.products:
                return

            self.logger.info(f"🔗 开始LET-WHMCS集成处理: {analysis.post_title}")

            # 调用集成服务处理
            result = await process_let_post_for_integration(analysis, self.integrated_monitor)

            if result.get("status") == "completed":
                total_matches = result.get("total_matches", 0)
                successful_orders = result.get("successful_orders", 0)

                if total_matches > 0:
                    self.logger.info(f"✅ 集成处理完成: {total_matches} 个匹配, {successful_orders} 个成功订单")
                else:
                    self.logger.debug("ℹ️ 集成处理完成: 无匹配产品")
            else:
                reason = result.get("reason", "未知原因")
                self.logger.debug(f"ℹ️ 跳过集成处理: {reason}")

        except Exception as e:
            self.logger.warning(f"⚠️ LET-WHMCS集成处理失败: {e}")

    def set_integrated_monitor(self, integrated_monitor):
        """设置集成监控器实例"""
        self.integrated_monitor = integrated_monitor

    def get_statistics(self) -> dict:
        """获取运行统计信息"""
        posts_count = len(list(self.config.posts_dir.glob("*.json")))
        return {
            "processed_posts": self.tracker.get_processed_count(),
            "stored_posts": posts_count,
            "data_directory": str(self.config.data_dir),
            "is_running": self.running,
        }


# 便捷函数
async def run_let_scheduler(proxy_url: str | None = None, check_interval: int = 300, data_dir: str = "data/let_posts"):
    """运行LET调度器的便捷函数"""
    config = LETSchedulerConfig(proxy_url=proxy_url, check_interval=check_interval, data_dir=data_dir)
    scheduler = LETScheduler(config)
    await scheduler.start()


if __name__ == "__main__":
    import signal
    import sys

    from dotenv import load_dotenv

    load_dotenv()

    # 配置日志
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")

    # 创建调度器
    proxy_url = os.getenv("HTTP_PROXY") or os.getenv("HTTPS_PROXY")
    config = LETSchedulerConfig(proxy_url=proxy_url)
    scheduler = LETScheduler(config)

    # 优雅退出处理
    def signal_handler(signum, frame):
        print("\n正在停止调度器...")
        scheduler.stop()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 运行调度器
    try:
        asyncio.run(scheduler.start())
    except KeyboardInterrupt:
        print("调度器已停止")
