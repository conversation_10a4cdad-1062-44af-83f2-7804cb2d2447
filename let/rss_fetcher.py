#!/usr/bin/env python3
"""
LowEndTalk RSS 获取模块
支持代理配置的RSS数据获取和解析
"""

import asyncio
from datetime import datetime, timezone
import logging
import os
from pathlib import Path
import subprocess
import xml.etree.ElementTree as ET

import aiohttp
from pydantic import BaseModel, Field


class RSSItem(BaseModel):
    """RSS item 数据模型"""

    title: str
    link: str
    pub_date: datetime
    category: str
    creator: str
    guid: str
    description: str  # HTML内容

    @classmethod
    def from_xml_element(cls, item_element: ET.Element) -> "RSSItem":
        """从XML元素创建RSSItem实例"""
        # 提取各个字段
        title = item_element.find("title").text if item_element.find("title") is not None else ""
        link = item_element.find("link").text if item_element.find("link") is not None else ""

        # 处理pubDate
        pub_date_str = item_element.find("pubDate").text if item_element.find("pubDate") is not None else ""
        try:
            # 解析RFC 2822格式的日期
            pub_date = datetime.strptime(pub_date_str, "%a, %d %b %Y %H:%M:%S %z")
        except ValueError:
            # 如果解析失败，使用当前时间
            pub_date = datetime.now(timezone.utc)

        category = item_element.find("category").text if item_element.find("category") is not None else ""

        # 处理dc:creator
        creator_element = item_element.find(".//{http://purl.org/dc/elements/1.1/}creator")
        creator = creator_element.text if creator_element is not None else ""

        guid = item_element.find("guid").text if item_element.find("guid") is not None else ""
        description = item_element.find("description").text if item_element.find("description") is not None else ""

        return cls(
            title=title,
            link=link,
            pub_date=pub_date,
            category=category,
            creator=creator,
            guid=guid,
            description=description,
        )


class RSSFetcherConfig(BaseModel):
    """RSS获取器配置"""

    rss_url: str = "https://lowendtalk.com/categories/offers/feed.rss"
    proxy_url: str | None = Field(None, description="代理URL，如 http://127.0.0.1:7890")
    timeout: int = Field(30, description="请求超时时间（秒）")
    user_agent: str = Field(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        description="User Agent",
    )
    retry_attempts: int = Field(3, description="重试次数")
    retry_delay: int = Field(5, description="重试延迟（秒）")


class RSSFetcher:
    """RSS获取器"""

    def __init__(self, config: RSSFetcherConfig | None = None):
        self.config = config or RSSFetcherConfig()
        self.logger = logging.getLogger(__name__)

        # 创建debug目录
        self.debug_dir = Path("data/debug")
        self.debug_dir.mkdir(parents=True, exist_ok=True)
        self.cfbypass_base_url = os.getenv("CFPASS_BASE_URL") or "http://cfbypass:8000"

    def _save_debug_content(self, content: str, error_msg: str) -> str:
        """保存调试内容到debug文件夹"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            debug_file = self.debug_dir / f"rss_debug_{timestamp}.txt"

            debug_info = f"""调试信息 - RSS解析失败
时间: {datetime.now().isoformat()}
错误: {error_msg}
RSS URL: {self.config.rss_url}
内容长度: {len(content)} 字符

================ 原始内容 ================
{content}
================ 内容结束 ================
"""

            with open(debug_file, "w", encoding="utf-8") as f:
                f.write(debug_info)

            self.logger.info(f"调试内容已保存到: {debug_file}")
            return str(debug_file)
        except Exception as e:
            self.logger.error(f"保存调试内容失败: {e}")
            return ""

    def _parse_rss_content(self, xml_content: str) -> list[RSSItem]:
        """解析RSS XML内容"""
        try:
            # 记录接收到的内容基本信息
            self.logger.info(f"开始解析XML内容，长度: {len(xml_content)} 字符")

            # 检查内容是否以XML声明开始
            content_preview = xml_content[:200].strip()
            self.logger.debug(f"内容预览: {content_preview}")

            # 检查是否看起来像HTML而不是XML
            if "just a moment" in content_preview.lower():
                error_msg = "该页面存在反爬虫机制，请使用代理或cloudflare bypass"
                self.logger.error(error_msg)
                debug_file = self._save_debug_content(xml_content, error_msg)
                raise ValueError(f"{error_msg}，调试文件: {debug_file}")

            root = ET.fromstring(xml_content)

            # 查找所有item元素
            items = root.findall(".//item")

            rss_items = []
            for item_element in items:
                try:
                    rss_item = RSSItem.from_xml_element(item_element)
                    rss_items.append(rss_item)
                except Exception as e:
                    self.logger.warning(f"解析RSS item失败: {e}")
                    continue

            self.logger.info(f"成功解析 {len(rss_items)} 个RSS项目")
            return rss_items

        except ET.ParseError as e:
            error_msg = f"XML解析失败: {e}"
            self.logger.error(error_msg)
            debug_file = self._save_debug_content(xml_content, error_msg)
            raise Exception(f"{error_msg}，调试文件已保存: {debug_file}")
        except Exception as e:
            error_msg = f"RSS内容解析失败: {e}"
            self.logger.error(error_msg)
            debug_file = self._save_debug_content(xml_content, error_msg)
            raise Exception(f"{error_msg}，调试文件已保存: {debug_file}")

    async def fetch_rss_with_cfbypass(self) -> list[RSSItem]:
        """使用cfbypass获取RSS数据"""
        full_url = f"{self.cfbypass_base_url}/html?url={self.config.rss_url}"
        async with aiohttp.ClientSession() as session:
            async with session.get(full_url) as response:
                return self._parse_rss_content(await response.text())

    def fetch_rss_with_curl(self) -> list[RSSItem]:
        """使用curl获取RSS数据（作为最后的备用方案）"""

        # 构建curl命令
        cmd = [
            "curl",
            "-s",
            "-L",
            "-H",
            f"User-Agent: {self.config.user_agent}",
            "-H",
            "Accept: application/rss+xml, application/xml, text/xml, */*",
            "-H",
            "Accept-Charset: utf-8",
        ]

        # 添加代理配置
        if self.config.proxy_url:
            cmd.extend(["--proxy", self.config.proxy_url])
            self.logger.info(f"使用代理 (curl): {self.config.proxy_url}")

        cmd.append(self.config.rss_url)

        for attempt in range(self.config.retry_attempts):
            try:
                self.logger.info(
                    f"获取RSS数据 (curl): {self.config.rss_url} (尝试 {attempt + 1}/{self.config.retry_attempts})"
                )

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.config.timeout, check=True)

                if result.stdout:
                    self.logger.info(f"成功获取内容，长度: {len(result.stdout)} 字符")

                    # 检查HTTP状态
                    if result.stderr:
                        self.logger.warning(f"curl stderr: {result.stderr}")

                    # 解析XML
                    return self._parse_rss_content(result.stdout)
                else:
                    error_msg = "curl返回空内容"
                    self.logger.error(error_msg)
                    if result.stderr:
                        debug_file = self._save_debug_content(
                            f"STDOUT: {result.stdout}\nSTDERR: {result.stderr}", error_msg
                        )
                        raise ValueError(f"{error_msg}，调试文件: {debug_file}")
                    raise ValueError(error_msg)

            except subprocess.CalledProcessError as e:
                error_msg = f"curl调用失败 (尝试 {attempt + 1}/{self.config.retry_attempts}): 返回码 {e.returncode}"
                self.logger.warning(error_msg)

                # 保存错误调试信息
                debug_content = f"STDOUT: {e.stdout}\nSTDERR: {e.stderr}\n命令: {' '.join(cmd)}"
                debug_file = self._save_debug_content(debug_content, error_msg)

                if attempt < self.config.retry_attempts - 1:
                    import time

                    time.sleep(self.config.retry_delay)
                else:
                    raise Exception(f"{error_msg}，调试文件: {debug_file}")
            except Exception as e:
                self.logger.warning(f"获取RSS失败 (curl, 尝试 {attempt + 1}/{self.config.retry_attempts}): {e}")
                if attempt < self.config.retry_attempts - 1:
                    import time

                    time.sleep(self.config.retry_delay)
                else:
                    raise

    async def fetch_rss(self) -> list[RSSItem]:
        return await self.fetch_rss_with_cfbypass()

    async def fetch_rss_combined(self) -> list[RSSItem]:
        return await self.fetch_rss()


# 便捷函数
async def fetch_let_rss(proxy_url: str | None = None) -> list[RSSItem]:
    """获取LowEndTalk RSS数据的便捷函数"""
    config = RSSFetcherConfig(proxy_url=proxy_url)
    fetcher = RSSFetcher(config)
    return await fetcher.fetch_rss_combined()


if __name__ == "__main__":
    import os

    from dotenv import load_dotenv

    load_dotenv()

    # 配置日志
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")

    async def main():
        # 从环境变量读取代理配置
        proxy_url = os.getenv("HTTP_PROXY") or os.getenv("HTTPS_PROXY")

        try:
            items = await fetch_let_rss(proxy_url=proxy_url)

            print(f"获取到 {len(items)} 个RSS项目:")
            for i, item in enumerate(items[:5], 1):  # 只显示前5个
                print(f"\n{i}. {item.title}")
                print(f"   链接: {item.link}")
                print(f"   发布: {item.pub_date}")
                print(f"   作者: {item.creator}")
                print(f"   描述长度: {len(item.description)} 字符")

        except Exception as e:
            print(f"获取RSS失败: {e}")

    asyncio.run(main())
