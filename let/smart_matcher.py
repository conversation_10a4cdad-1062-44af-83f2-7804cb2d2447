#!/usr/bin/env python3
"""
LET智能匹配器

基于pydantic-ai实现，输入LET帖子分析结果，输出匹配的产品列表。
复用现有的LET模型，不进行复杂评分，只做简单的是/否匹配判断。
"""

import os

import logfire
from pydantic import BaseModel, Field
from pydantic_ai import Agent, format_as_xml

from .models import LETPostAnalysis, ServerProduct

logfire.configure(send_to_logfire="if-token-present")
logfire.instrument_pydantic_ai()


class LETMatchResult(BaseModel):
    """LET智能匹配结果"""

    has_match: bool = Field(..., description="是否找到匹配的产品")
    matched_products: list[ServerProduct] = Field(default_factory=list, description="匹配的产品列表")
    reason: str = Field(..., description="匹配或不匹配的原因")


class MatchCriteria(BaseModel):
    """匹配条件"""

    natural_language: str = Field("", description="自然语言描述需求")
    keywords: list[str] = Field(default_factory=list, description="关键词列表")
    min_price: float | None = Field(None, description="最低价格")
    max_price: float | None = Field(None, description="最高价格")
    min_cpu_cores: int | None = Field(None, description="最少CPU核心数")
    min_ram_gb: int | None = Field(None, description="最少内存GB")
    min_storage_gb: int | None = Field(None, description="最少存储GB")
    preferred_locations: list[str] = Field(default_factory=list, description="偏好地区")
    excluded_locations: list[str] = Field(default_factory=list, description="排除地区")


# 全局Agent实例
_smart_matcher: Agent[tuple[LETPostAnalysis, MatchCriteria, str], LETMatchResult] | None = None


def get_smart_matcher() -> Agent[tuple[LETPostAnalysis, MatchCriteria, str], LETMatchResult]:
    """获取或创建智能匹配器实例"""

    global _smart_matcher
    if _smart_matcher is None:
        logfire.info("🔧 创建新的LET智能匹配器实例")

        # 使用环境变量配置模型
        model = os.getenv("PYDANTIC_AI_MODEL", "openai:gpt-4o")
        logfire.info(f"🤖 使用AI模型: {model}")

        _smart_matcher = Agent(
            model=model,
            result_type=LETMatchResult,
            system_prompt="""
你是一个LET帖子产品匹配专家。

任务：分析LET帖子中的产品，找出符合用户要求的产品。

输入：
1. LET帖子的结构化分析数据（包含产品列表）
2. 用户的匹配条件，包括：
   - natural_language: 用户的自然语言描述（这是最重要的参考）
   - 具体的技术参数（价格范围、关键词、配置要求等）
3. 目标网站域名

输出要求：
- 如果找到符合条件的产品，返回has_match=true和产品列表
- 如果没有找到，返回has_match=false和原因
- 选择最符合要求的产品（优先考虑性价比和用户自然语言描述的匹配度）

匹配规则（按优先级排序）：
1. **自然语言描述匹配**：优先考虑用户的自然语言描述，理解用户的真实需求
2. 价格必须在用户指定范围内
3. 如果指定了关键词，产品标题必须包含至少一个关键词
4. 配置要求（CPU、内存、存储）必须满足最低要求
5. 地区偏好优先考虑，但不是硬性要求
6. 产品必须有有效的订购链接

特别注意：
- 自然语言描述是用户需求的核心表达，应该综合理解并匹配
- 如果自然语言描述与具体参数有冲突，优先理解用户的真实意图
- 考虑性价比，推荐最适合用户需求的产品

重要：直接返回原始的ServerProduct对象，不要修改字段名称。
""",
        )
        logfire.info("✅ LET智能匹配器创建完成")

    return _smart_matcher


async def match_let_post_for_site(
    post_analysis: LETPostAnalysis, site_domain: str, match_criteria: MatchCriteria
) -> LETMatchResult:
    """
    为特定网站匹配LET帖子产品

    Args:
        post_analysis: LET帖子分析结果
        site_domain: 目标网站域名
        match_criteria: 匹配条件

    Returns:
        匹配结果
    """

    with logfire.span("match_let_post_for_site") as span:
        span.set_attribute("site_domain", site_domain)
        span.set_attribute("post_title", post_analysis.post_title)
        span.set_attribute("products_count", len(post_analysis.products))

        # 1. 检查帖子是否与目标网站相关
        if not _is_site_related(post_analysis, site_domain):
            logfire.info(f"❌ 帖子与网站 {site_domain} 无关")
            return LETMatchResult(has_match=False, matched_products=[], reason=f"帖子内容与网站 {site_domain} 无关")

        logfire.info(f"✅ 帖子与网站 {site_domain} 相关，开始AI匹配")

        # 2. 使用AI进行产品匹配
        try:
            matcher = get_smart_matcher()
            formatted_input = format_as_xml(
                {
                    "post_analysis": post_analysis,
                    "match_criteria": match_criteria,
                    "site_domain": site_domain,
                }
            )
            result = await matcher.run(formatted_input)

            span.set_attribute("has_match", result.data.has_match)
            span.set_attribute("matched_count", len(result.data.matched_products))

            logfire.info(f"🎯 匹配完成: {result.data.reason}")
            return result.data

        except Exception as e:
            logfire.error(f"AI匹配失败: {e}")
            return LETMatchResult(has_match=False, matched_products=[], reason=f"AI匹配过程出错: {e!s}")


def _is_site_related(post_analysis: LETPostAnalysis, site_domain: str) -> bool:
    """检查帖子是否与目标网站相关"""
    logfire.info(f"检查帖子是否与目标网站相关: {site_domain}")
    logfire.info(f"供应商网站: {post_analysis.provider.website}")
    logfire.info(f"产品订购链接: {post_analysis.products[0].order_url}")
    # 检查供应商网站
    if post_analysis.provider.website and site_domain in post_analysis.provider.website:
        return True

    # 检查产品订购链接
    for product in post_analysis.products:
        if product.order_url and site_domain in product.order_url:
            return True

    return False
