#!/usr/bin/env python3
"""
LET RSS Monitor 主启动脚本
自动监控LowEndTalk RSS并分析产品信息
"""

import argparse
import asyncio
import logging
import os
from pathlib import Path
import sys

from dotenv import load_dotenv

from .rss_fetcher import fetch_let_rss
from .scheduler import LETScheduler, LETSchedulerConfig


def setup_logging(log_level: str = "INFO"):
    """配置日志"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(), logging.FileHandler("data/logs/let_monitor.log", encoding="utf-8")],
    )


async def test_rss_fetch(proxy_url: str | None = None):
    """测试RSS获取功能"""
    print("测试RSS获取功能...")
    try:
        items = await fetch_let_rss(proxy_url=proxy_url)
        print(f"✅ 成功获取 {len(items)} 个RSS项目")

        if items:
            print("\n最新的3个帖子:")
            for i, item in enumerate(items[:3], 1):
                print(f"{i}. {item.title}")
                print(f"   作者: {item.creator}")
                print(f"   时间: {item.pub_date}")
                print(f"   链接: {item.link}")
                print()
    except Exception as e:
        print(f"❌ RSS获取失败: {e}")
        return False
    return True


async def run_once(proxy_url: str | None = None, data_dir: str = "data/let_posts"):
    """运行一次RSS检查和处理"""
    print("运行一次RSS检查...")

    config = LETSchedulerConfig(proxy_url=proxy_url, data_dir=data_dir)
    scheduler = LETScheduler(config)

    try:
        await scheduler._check_and_process()
        stats = scheduler.get_statistics()
        print("✅ 处理完成")
        print(f"   已处理帖子: {stats['processed_posts']}")
        print(f"   存储帖子: {stats['stored_posts']}")
        print(f"   数据目录: {stats['data_directory']}")
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False
    return True


async def start_scheduler(proxy_url: str | None = None, interval: int = 300, data_dir: str = "data/let_posts"):
    """启动定时调度器"""
    print(f"启动LET RSS定时监控器 (间隔: {interval}秒)")

    config = LETSchedulerConfig(proxy_url=proxy_url, check_interval=interval, data_dir=data_dir)
    scheduler = LETScheduler(config)

    # 设置信号处理
    import signal

    def signal_handler(signum, frame):
        print("\n正在停止监控器...")
        scheduler.stop()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        await scheduler.start()
    except KeyboardInterrupt:
        print("监控器已停止")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LET RSS Monitor - LowEndTalk自动监控工具")

    parser.add_argument(
        "command",
        choices=["test", "run-once", "start"],
        help="运行模式: test(测试), run-once(运行一次), start(启动监控)",
    )

    parser.add_argument("--proxy", help="代理URL (如: http://127.0.0.1:7890)")

    parser.add_argument("--interval", type=int, default=300, help="检查间隔(秒) [默认: 300]")

    parser.add_argument("--data-dir", default="data/let_posts", help="数据存储目录 [默认: data/let_posts]")

    parser.add_argument(
        "--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="日志级别 [默认: INFO]"
    )

    args = parser.parse_args()

    # 加载环境变量
    load_dotenv()

    # 设置代理
    proxy_url = args.proxy or os.getenv("HTTP_PROXY") or os.getenv("HTTPS_PROXY")
    if proxy_url:
        print(f"使用代理: {proxy_url}")

    # 配置日志
    setup_logging(args.log_level)

    # 确保数据目录存在
    Path(args.data_dir).mkdir(parents=True, exist_ok=True)
    Path("data").mkdir(exist_ok=True)

    # 根据命令执行相应操作
    if args.command == "test":
        success = asyncio.run(test_rss_fetch(proxy_url))
        sys.exit(0 if success else 1)

    elif args.command == "run-once":
        success = asyncio.run(run_once(proxy_url, args.data_dir))
        sys.exit(0 if success else 1)

    elif args.command == "start":
        asyncio.run(start_scheduler(proxy_url, args.interval, args.data_dir))


if __name__ == "__main__":
    main()
