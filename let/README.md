# LET RSS Monitor

自动监控 LowEndTalk RSS 并分析服务器产品信息的工具。

## 功能特性

- 🔄 **定时RSS监控**: 自动获取 LowEndTalk Offers 频道的RSS数据
- 🤖 **AI智能分析**: 使用 pydantic-ai 自动提取和结构化产品信息
- 🗃️ **数据存储**: 自动存储分析结果，避免重复处理
- 🌐 **代理支持**: 支持HTTP代理访问RSS
- 📱 **多格式输出**: 支持Telegram、Discord、微信、邮件等多种格式
- ⚡ **并发处理**: 支持多线程并发分析提高效率

## 安装要求

- Python 3.12+
- uv (Python 包管理器)
- pydantic-ai 支持的AI模型 (OpenAI/Anthropic/Google)

## 快速开始

### 1. 环境配置

```bash
# 复制环境变量模板
cp let/.env.example .env

# 编辑配置文件，设置API密钥
vim .env
```

关键配置项：
```env
# AI模型配置
PYDANTIC_AI_MODEL=openai:gpt-4o
OPENAI_API_KEY=your_api_key

# 可选：代理配置
HTTP_PROXY=http://127.0.0.1:7890
```

### 2. 安装依赖

```bash
# 在项目根目录执行
uv sync
```

### 3. 运行测试

```bash
# 测试RSS获取功能
uv run python -m let.main test

# 测试代理配置
uv run python -m let.main test --proxy http://127.0.0.1:7890
```

### 4. 运行一次处理

```bash
# 运行一次完整的RSS检查和分析
uv run python -m let.main run-once

# 指定数据目录
uv run python -m let.main run-once --data-dir data/my_posts
```

### 5. 启动定时监控

```bash
# 启动定时监控器 (默认5分钟间隔)
uv run python -m let.main start

# 自定义间隔时间
uv run python -m let.main start --interval 600  # 10分钟

# 后台运行
nohup uv run python -m let.main start > let_monitor.log 2>&1 &
```

## 使用方法

### 命令行参数

```bash
uv run python -m let.main {test|run-once|start} [options]
```

**命令:**
- `test`: 测试RSS获取功能
- `run-once`: 运行一次完整处理
- `start`: 启动定时监控

**选项:**
- `--proxy URL`: 代理服务器地址
- `--interval N`: 检查间隔(秒，默认300)
- `--data-dir PATH`: 数据存储目录
- `--log-level LEVEL`: 日志级别(DEBUG/INFO/WARNING/ERROR)

### 编程接口

```python
from let import fetch_let_rss, LETScheduler

# 获取RSS数据
items = await fetch_let_rss(proxy_url="http://127.0.0.1:7890")

# 创建调度器
scheduler = LETScheduler()
await scheduler.start()
```

## 数据结构

### RSS项目
- title: 帖子标题
- link: 帖子链接
- pub_date: 发布时间
- creator: 作者
- description: HTML内容

### 分析结果
- provider: 供应商信息
- products: 产品列表
- global_promo_code: 全局优惠码
- tags: 帖子标签

## 输出格式

支持多种格式化输出：

```python
from let.models import LETPostAnalysis

# 从分析结果生成各种格式
telegram_msg = analysis.format_for_telegram()
discord_msg = analysis.format_for_discord() 
wechat_article = analysis.format_for_wechat()
email_content = analysis.format_for_email()
compact_msg = analysis.format_compact()
```

## 存储结构

```
data/let_posts/
├── processed_posts.json    # 已处理帖子记录
└── posts/
    ├── 20250625_120000_12345.json
    └── 20250625_120500_12346.json
```

每个帖子文件包含：
- RSS原始数据
- AI分析结果
- 处理时间戳

## 代理配置

支持多种代理配置方式：

1. **命令行参数**: `--proxy http://127.0.0.1:7890`
2. **环境变量**: `HTTP_PROXY=http://127.0.0.1:7890`
3. **配置文件**: 在 `.env` 中设置

## 故障排除

### 常见问题

1. **RSS获取失败**
   - 检查网络连接
   - 确认代理配置
   - 查看日志详细错误

2. **AI分析失败**
   - 检查API密钥配置
   - 确认模型配置正确
   - 查看API配额限制

3. **数据存储问题**
   - 检查目录权限
   - 确认磁盘空间
   - 查看文件锁定情况

### 日志调试

```bash
# 启用详细日志
uv run python -m let.main start --log-level DEBUG

# 查看日志文件
tail -f data/let_monitor.log
```

## 开发说明

### 项目结构

```
let/
├── __init__.py         # 包初始化
├── agent.py           # AI分析器
├── models.py          # 数据模型
├── rss_fetcher.py     # RSS获取器
├── scheduler.py       # 任务调度器
├── main.py           # 主启动脚本
└── README.md         # 说明文档
```

### 测试格式化功能

```bash
# 运行格式化测试
uv run python let/test_formatter.py
```

### 代码规范

```bash
# 格式化代码
uv run ruff format let/

# 检查代码规范
uv run ruff check let/
```

## 许可证

本项目基于 MIT 许可证开源。