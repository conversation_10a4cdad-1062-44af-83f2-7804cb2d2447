#!/usr/bin/env python3
"""
LET-WHMCS集成服务

完整实现：
1. 获取用户配置的LET智能产品
2. 匹配LET帖子与产品需求
3. 触发WHMCS自动下单
4. 更新统计信息和通知
"""

from datetime import datetime
import logging

import logfire

from whmcs.core import auto_order
from whmcs.models import ProductConfig, WHMCSConfig

from .models import LETPostAnalysis
from .smart_matcher import LETMatchResult, MatchCriteria, match_let_post_for_site


class LETWHMCSIntegrationService:
    """LET-WHMCS集成服务"""

    def __init__(self, integrated_monitor=None):
        """
        初始化集成服务

        Args:
            integrated_monitor: IntegratedMonitor实例，用于获取配置和发送通知
        """
        self.logger = logging.getLogger(__name__)
        self.processed_posts = set()
        self.integrated_monitor = integrated_monitor

    async def process_new_post(self, post_analysis: LETPostAnalysis) -> dict:
        """
        处理新的LET帖子

        Args:
            post_analysis: LET帖子分析结果

        Returns:
            处理结果摘要
        """

        post_id = f"{post_analysis.post_url}_{post_analysis.post_title}"

        if post_id in self.processed_posts:
            return {"status": "skipped", "reason": "already_processed"}

        self.logger.info(f"🚀 开始处理LET帖子: {post_analysis.post_title}")

        with logfire.span("process_let_post") as span:
            span.set_attribute("post_title", post_analysis.post_title)
            span.set_attribute("provider", post_analysis.provider.name)

            try:
                # 获取所有LET智能产品配置
                let_smart_products = await self._get_let_smart_products()

                if not let_smart_products:
                    self.logger.info("❌ 没有配置LET智能产品")
                    return {
                        "status": "completed",
                        "reason": "no_let_smart_products_configured",
                        "processed_at": datetime.now().isoformat(),
                    }

                span.set_attribute("let_smart_products_count", len(let_smart_products))

                # 处理每个LET智能产品
                total_matches = 0
                successful_orders = 0

                for site_id, product_id, product_config, site_config in let_smart_products:
                    match_result = await self._process_product_matching(
                        post_analysis, site_id, product_id, product_config, site_config
                    )

                    if match_result["has_match"]:
                        total_matches += 1
                        # matched_products = match_result["matched_products"]
                        # 尝试自动下单
                        if product_config.auto_order_enabled:
                            order_result = await self._attempt_auto_order(
                                match_result, site_id, product_id, product_config, site_config
                            )

                            if order_result["success"]:
                                successful_orders += 1

                # 记录处理结果
                self.processed_posts.add(post_id)

                result = {
                    "status": "completed",
                    "post_title": post_analysis.post_title,
                    "provider": post_analysis.provider.name,
                    "total_products_checked": len(let_smart_products),
                    "total_matches": total_matches,
                    "successful_orders": successful_orders,
                    "processed_at": datetime.now().isoformat(),
                }

                span.set_attribute("total_matches", total_matches)
                span.set_attribute("successful_orders", successful_orders)

                if total_matches > 0:
                    self.logger.info(f"✅ 处理完成: {total_matches} 个匹配, {successful_orders} 个成功订单")

                    # 发送通知
                    await self._send_notification(result)
                else:
                    self.logger.info("ℹ️ 处理完成: 无匹配产品")

                return result

            except Exception as e:
                self.logger.error(f"处理帖子失败: {e}")
                span.record_exception(e)
                return {"status": "error", "error": str(e), "processed_at": datetime.now().isoformat()}

    async def _get_let_smart_products(self) -> list[tuple[str, str, ProductConfig, WHMCSConfig]]:
        """
        获取所有配置的LET智能产品

        Returns:
            [(site_id, product_id, product_config, site_config), ...]
        """

        let_smart_products = []

        if not self.integrated_monitor or not self.integrated_monitor.stock_monitor:
            self.logger.warning("⚠️ 没有找到WHMCS监控器实例")
            return let_smart_products

        config = self.integrated_monitor.stock_monitor.config

        for site_id, site_config in config.whmcs_sites.items():
            for product_id, product_config in site_config.products.items():
                # 只处理LET智能产品
                if (
                    product_config.product_type == "let_smart"
                    and product_config.enabled
                    and product_config.let_match_criteria
                ):
                    let_smart_products.append((site_id, product_id, product_config, site_config))

        self.logger.info(f"📋 找到 {len(let_smart_products)} 个LET智能产品配置")
        return let_smart_products

    async def _process_product_matching(
        self,
        post_analysis: LETPostAnalysis,
        site_id: str,
        product_id: str,
        product_config: ProductConfig,
        site_config: WHMCSConfig,
    ) -> dict:
        """
        处理单个产品的匹配

        Returns:
            匹配结果字典
        """

        with logfire.span("product_matching") as span:
            span.set_attribute("site_id", site_id)
            span.set_attribute("product_id", product_id)

            # 构建匹配条件
            criteria_config = product_config.let_match_criteria
            match_criteria = MatchCriteria(
                natural_language=criteria_config.natural_language,
                keywords=criteria_config.keywords,
                min_price=criteria_config.min_price,
                max_price=criteria_config.max_price,
                min_cpu_cores=criteria_config.min_cpu_cores,
                min_ram_gb=criteria_config.min_ram_gb,
                min_storage_gb=criteria_config.min_storage_gb,
                preferred_locations=criteria_config.preferred_locations,
                excluded_locations=criteria_config.excluded_locations,
            )

            # 从网站URL提取域名
            site_domain = site_config.base_url.host

            # 执行智能匹配
            match_result = await match_let_post_for_site(post_analysis, site_domain, match_criteria)

            # 更新产品统计
            await self._update_product_stats(site_id, product_id, match_result)

            span.set_attribute("has_match", match_result.has_match)
            span.set_attribute("matched_count", len(match_result.matched_products))

            self.logger.info(
                f"🎯 [{site_id}:{product_id}] 匹配结果: "
                f"{'✅' if match_result.has_match else '❌'} "
                f"{len(match_result.matched_products)} 个产品"
            )

            return {
                "has_match": match_result.has_match,
                "matched_products": match_result.matched_products,
                "reason": match_result.reason,
                "site_id": site_id,
                "product_id": product_id,
            }

    async def _attempt_auto_order(
        self, match_result: dict, site_id: str, product_id: str, product_config: ProductConfig, site_config: WHMCSConfig
    ) -> dict:
        """
        尝试自动下单

        Returns:
            下单结果字典
        """

        with logfire.span("auto_order_attempt") as span:
            span.set_attribute("site_id", site_id)
            span.set_attribute("product_id", product_id)

            if not match_result["matched_products"]:
                return {"success": False, "reason": "no_matched_products"}

            # 选择第一个匹配的产品（通常是最佳匹配）
            selected_product = match_result["matched_products"][0]

            if not selected_product.order_url:
                return {"success": False, "reason": "no_order_url"}

            try:
                # 检查失败次数限制
                if product_config.order_failure_count >= product_config.max_order_retries:
                    self.logger.warning(
                        f"⚠️ [{site_id}:{product_id}] 已达到最大重试次数 ({product_config.max_order_retries})"
                    )
                    return {"success": False, "reason": "max_retries_exceeded"}

                self.logger.info(f"🛒 [{site_id}:{product_id}] 开始自动下单: {selected_product.title}")

                # 调用WHMCS自动下单
                order_result = await auto_order(
                    base_url=str(site_config.base_url),
                    username=site_config.username,
                    password=site_config.password,
                    hostname_base=site_config.hostname_base,
                    rootpw=site_config.rootpw,
                    product_url=selected_product.order_url,
                    payment_method=site_config.payment_method,
                    promo_code=getattr(selected_product, "promo_code", None),
                )
                logfire.info(f"订单结果: {order_result}")
                if order_result.get("success"):
                    # 更新成功统计
                    await self._update_order_success_stats(site_id, product_id)

                    span.set_attribute("order_success", True)
                    span.set_attribute("order_id", order_result.get("order_id", ""))

                    self.logger.info(
                        f"✅ [{site_id}:{product_id}] 下单成功: {order_result.get('order_id', '未知订单')}"
                    )

                    return {
                        "success": True,
                        "order_id": order_result.get("order_id"),
                        "product": selected_product.title,
                    }
                else:
                    # 更新失败统计
                    await self._update_order_failure_stats(site_id, product_id)

                    span.set_attribute("order_success", False)
                    span.set_attribute("failure_reason", order_result.get("error", "未知错误"))

                    self.logger.warning(
                        f"❌ [{site_id}:{product_id}] 下单失败: {order_result.get('error', '未知错误')}"
                    )

                    return {"success": False, "reason": order_result.get("error", "unknown_error")}

            except Exception as e:
                # 更新失败统计
                await self._update_order_failure_stats(site_id, product_id)

                span.record_exception(e)
                self.logger.error(f"❌ [{site_id}:{product_id}] 下单异常: {e}")

                return {"success": False, "reason": f"exception: {e}"}

    async def _update_product_stats(self, site_id: str, product_id: str, match_result: LETMatchResult):
        """更新产品匹配统计"""

        if not self.integrated_monitor or not self.integrated_monitor.stock_monitor:
            return

        try:
            config = self.integrated_monitor.stock_monitor.config
            product = config.whmcs_sites[site_id].products[product_id]

            # 更新匹配统计
            product.last_let_match_count = len(match_result.matched_products)
            product.last_let_match_time = datetime.now()

            if match_result.has_match:
                product.total_let_matches += 1

            # 保存配置
            self.integrated_monitor.stock_monitor.save_config()

        except Exception as e:
            self.logger.error(f"更新产品统计失败: {e}")

    async def _update_order_success_stats(self, site_id: str, product_id: str):
        """更新下单成功统计"""

        if not self.integrated_monitor or not self.integrated_monitor.stock_monitor:
            return

        try:
            config = self.integrated_monitor.stock_monitor.config
            product = config.whmcs_sites[site_id].products[product_id]

            product.successful_let_orders += 1
            product.order_failure_count = 0  # 重置失败计数
            product.last_order_attempt = datetime.now()

            # 保存配置
            await self.integrated_monitor.stock_monitor.save_config()

        except Exception as e:
            self.logger.error(f"更新成功统计失败: {e}")

    async def _update_order_failure_stats(self, site_id: str, product_id: str):
        """更新下单失败统计"""

        if not self.integrated_monitor or not self.integrated_monitor.stock_monitor:
            return

        try:
            config = self.integrated_monitor.stock_monitor.config
            product = config.whmcs_sites[site_id].products[product_id]

            product.order_failure_count += 1
            product.last_order_attempt = datetime.now()

            # 保存配置
            self.integrated_monitor.stock_monitor.save_config()

        except Exception as e:
            self.logger.error(f"更新失败统计失败: {e}")

    async def _send_notification(self, result: dict):
        """发送处理结果通知"""

        if not self.integrated_monitor or not self.integrated_monitor.notification_service:
            return

        try:
            from notification_models import NotificationMessage, NotificationPriority, NotificationType

            # 构建通知消息
            title = "🎯 LET智能匹配结果"

            content = f"""
📄 帖子: {result["post_title"]}
🏢 供应商: {result["provider"]}
🔍 检查产品: {result["total_products_checked"]} 个
✅ 匹配数量: {result["total_matches"]} 个
🛒 成功订单: {result["successful_orders"]} 个
⏰ 处理时间: {result["processed_at"]}
            """.strip()

            # 根据结果确定优先级
            priority = NotificationPriority.HIGH if result["successful_orders"] > 0 else NotificationPriority.NORMAL

            message = NotificationMessage(
                title=title, content=content, notification_type=NotificationType.LET_MATCH, priority=priority
            )

            await self.integrated_monitor.notification_service.send_notification(message)

        except Exception as e:
            self.logger.error(f"发送通知失败: {e}")


# 全局集成服务实例
_integration_service: LETWHMCSIntegrationService | None = None


def get_integration_service(integrated_monitor=None) -> LETWHMCSIntegrationService:
    """获取或创建集成服务实例"""

    global _integration_service
    if _integration_service is None:
        _integration_service = LETWHMCSIntegrationService(integrated_monitor)

    return _integration_service


async def process_let_post_for_integration(post_analysis: LETPostAnalysis, integrated_monitor=None) -> dict:
    """
    处理LET帖子的集成入口函数

    Args:
        post_analysis: LET帖子分析结果
        integrated_monitor: IntegratedMonitor实例

    Returns:
        处理结果字典
    """

    service = get_integration_service(integrated_monitor)
    return await service.process_new_post(post_analysis)
