#!/usr/bin/env python3
"""
LowEndTalk 帖子分析数据模型
优化后的数据结构：一个帖子对应一个供应商，避免重复信息
"""

from pydantic import BaseModel, Field


class ServerSpecs(BaseModel):
    """服务器规格信息"""

    cpu: str | None = Field(None, description="CPU 规格，如 'Intel Xeon E5-2697v2' 或 'AMD Ryzen 9 5950X'")
    cores: int | None = Field(None, description="CPU 核心数")
    ram: str | None = Field(None, description="内存大小，如 '32GB DDR4' 或 '64GB ECC'")
    storage: str | None = Field(None, description="存储信息，如 '1TB SSD' 或 '2x1TB NVMe'")
    bandwidth: str | None = Field(None, description="带宽信息，如 '10TB @ 1Gbps' 或 'Unmetered @ 100Mbps'")
    ip_count: int | None = Field(None, description="IP 地址数量")
    virtualization: str | None = Field(None, description="虚拟化技术，如 'KVM', 'OpenVZ', 'LXC'")


class PricingInfo(BaseModel):
    """价格信息"""

    price: float | None = Field(None, description="价格数值")
    currency: str | None = Field("USD", description="货币单位")
    billing_cycle: str | None = Field(None, description="计费周期，如 'monthly', 'yearly', 'quarterly'")
    discount_info: str | None = Field(None, description="优惠信息，如 '50% off first month'")
    setup_fee: float | None = Field(None, description="安装费用")


class LocationInfo(BaseModel):
    """地理位置信息"""

    country: str | None = Field(None, description="国家")
    city: str | None = Field(None, description="城市")
    datacenter: str | None = Field(None, description="数据中心名称")
    region: str | None = Field(None, description="地区，如 'US West Coast', 'Europe'")


class ProviderInfo(BaseModel):
    """服务提供商信息"""

    name: str = Field(..., description="供应商名称")
    website: str | None = Field(None, description="官方网站")
    contact: str | None = Field(None, description="联系方式")
    reputation: str | None = Field(None, description="信誉评级或评价")


class ServerProduct(BaseModel):
    """单个服务器产品信息（不包含供应商信息，避免重复）"""

    title: str = Field(..., description="产品标题或套餐名称")
    specs: ServerSpecs
    pricing: PricingInfo
    location: LocationInfo
    availability: str | None = Field(None, description="可用性状态，如 'In Stock', 'Limited', 'Pre-order'")
    features: list[str] | None = Field(None, description="附加特性列表，如 ['DDoS Protection', 'Full Root Access']")
    support_level: str | None = Field(None, description="支持级别，如 '24/7', 'Business Hours'")
    order_url: str | None = Field(None, description="订购链接")
    promo_code: str | None = Field(None, description="优惠码")
    valid_until: str | None = Field(None, description="优惠有效期")


class LETPostAnalysis(BaseModel):
    """LowEndTalk 帖子分析结果 - 优化后的数据结构"""

    # 帖子基本信息
    post_title: str = Field(..., description="帖子标题")
    post_url: str | None = Field(None, description="帖子链接")
    post_date: str | None = Field(None, description="发帖日期")

    # 供应商信息（整个帖子只有一个供应商）
    provider: ProviderInfo = Field(..., description="帖子发布的供应商信息")

    # 产品列表（所有产品都来自同一个供应商）
    products: list[ServerProduct] = Field(default_factory=list, description="提取到的产品列表")

    # 帖子分类和内容
    is_offer: bool = Field(True, description="是否为优惠帖子")
    tags: list[str] | None = Field(None, description="帖子标签，如 ['VPS', 'Dedicated', 'Black Friday']")
    summary: str | None = Field(None, description="帖子内容摘要")

    # 全局优惠信息（适用于所有产品的通用优惠）
    global_promo_code: str | None = Field(None, description="适用于所有产品的全局优惠码")
    global_discount_info: str | None = Field(None, description="全局优惠信息")
    promotion_end_date: str | None = Field(None, description="促销结束日期")

    def format_for_telegram(self, max_length: int = 4096) -> str:
        """格式化为 Telegram 消息"""
        lines = []

        # 标题和供应商
        lines.append(f"🎯 **{self.post_title}**")
        lines.append(f"🏢 **{self.provider.name}**")
        if self.provider.website:
            lines.append(f"🌐 {self.provider.website}")
        lines.append("")

        # 摘要
        if self.summary:
            lines.append(f"📋 {self.summary}")
            lines.append("")

        # 全局优惠信息
        if self.global_promo_code or self.global_discount_info:
            lines.append("🎁 **全局优惠**")
            if self.global_promo_code:
                lines.append(f"   🎫 优惠码: `{self.global_promo_code}`")
            if self.global_discount_info:
                lines.append(f"   💰 优惠: {self.global_discount_info}")
            if self.promotion_end_date:
                lines.append(f"   ⏰ 截止: {self.promotion_end_date}")
            lines.append("")

        # 产品列表
        for i, product in enumerate(self.products[:10], 1):  # 限制10个产品
            lines.append(f"📦 **产品 {i}: {product.title}**")
            lines.append(f"   💻 {product.specs.cpu or 'N/A'} | {product.specs.ram or 'N/A'}")
            lines.append(f"   💾 {product.specs.storage or 'N/A'}")
            lines.append(f"   🌐 {product.specs.bandwidth or 'N/A'}")
            lines.append(f"   📍 {product.location.city or 'N/A'}, {product.location.country or 'N/A'}")
            lines.append(f"   💰 ${product.pricing.price or 'N/A'}/{product.pricing.billing_cycle or 'month'}")

            if product.promo_code:
                lines.append(f"   🎫 优惠码: `{product.promo_code}`")
            if product.order_url:
                lines.append(f"   🛒 [立即订购]({product.order_url})")
            if product.availability:
                lines.append(f"   📊 状态: {product.availability}")
            lines.append("")

        # 原贴链接
        if self.post_url:
            lines.append(f"🔗 [查看原帖]({self.post_url})")

        content = "\n".join(lines)

        # 如果超长则截断
        if len(content) > max_length:
            content = content[: max_length - 50] + "\n\n... (内容已截断)"

        return content

    def format_for_discord(self, max_length: int = 2000) -> str:
        """格式化为 Discord 消息"""
        lines = []

        # 使用 Discord 的 Embed 风格
        lines.append(f"## 🎯 {self.post_title}")
        lines.append(f"**供应商:** {self.provider.name}")
        if self.provider.website:
            lines.append(f"**网站:** {self.provider.website}")
        lines.append("")

        if self.summary:
            lines.append(f"**摘要:** {self.summary}")
            lines.append("")

        # 全局优惠
        if self.global_promo_code or self.global_discount_info:
            lines.append("### 🎁 全局优惠")
            if self.global_promo_code:
                lines.append(f"**优惠码:** `{self.global_promo_code}`")
            if self.global_discount_info:
                lines.append(f"**优惠详情:** {self.global_discount_info}")
            lines.append("")

        # 产品列表（简化版）
        lines.append("### 📦 产品列表")
        for i, product in enumerate(self.products[:8], 1):  # 限制8个产品
            price_info = f"${product.pricing.price or 'N/A'}/{product.pricing.billing_cycle or 'mo'}"
            location = f"{product.location.city or 'N/A'}, {product.location.country or 'N/A'}"
            lines.append(f"**{i}.** {product.title}")
            lines.append(f"   💰 {price_info} | 📍 {location}")
            if product.promo_code:
                lines.append(f"   🎫 `{product.promo_code}`")
            if product.order_url:
                lines.append(f"   🛒 [订购链接]({product.order_url})")
            lines.append("")

        if self.post_url:
            lines.append(f"[查看原帖]({self.post_url})")

        content = "\n".join(lines)

        if len(content) > max_length:
            content = content[: max_length - 50] + "\n... (内容已截断)"

        return content

    def format_for_wechat(self) -> str:
        """格式化为微信公众号文章"""
        lines = []

        lines.append(f"# {self.post_title}")
        lines.append("")
        lines.append(f"**供应商：** {self.provider.name}")
        if self.provider.website:
            lines.append(f"**官网：** {self.provider.website}")
        lines.append("")

        if self.summary:
            lines.append("## 📋 活动摘要")
            lines.append(self.summary)
            lines.append("")

        # 全局优惠
        if self.global_promo_code or self.global_discount_info:
            lines.append("## 🎁 全局优惠")
            if self.global_promo_code:
                lines.append(f"**优惠码：** `{self.global_promo_code}`")
            if self.global_discount_info:
                lines.append(f"**优惠详情：** {self.global_discount_info}")
            if self.promotion_end_date:
                lines.append(f"**截止时间：** {self.promotion_end_date}")
            lines.append("")

        # 产品详情表格
        lines.append("## 📦 产品详情")
        lines.append("")
        lines.append("| 产品 | 配置 | 价格 | 位置 | 优惠码 |")
        lines.append("|------|------|------|------|--------|")

        for product in self.products:
            config = (
                f"{product.specs.cpu or 'N/A'}<br/>{product.specs.ram or 'N/A'}<br/>{product.specs.storage or 'N/A'}"
            )
            price = f"${product.pricing.price or 'N/A'}/{product.pricing.billing_cycle or 'mo'}"
            location = f"{product.location.city or 'N/A'}, {product.location.country or 'N/A'}"
            promo = product.promo_code or "-"

            lines.append(f"| {product.title} | {config} | {price} | {location} | {promo} |")

        lines.append("")

        # 订购链接
        lines.append("## 🛒 订购链接")
        for i, product in enumerate(self.products, 1):
            if product.order_url:
                lines.append(f"{i}. [{product.title}]({product.order_url})")

        lines.append("")
        if self.post_url:
            lines.append(f"**原帖链接：** {self.post_url}")

        return "\n".join(lines)

    def format_for_email(self) -> str:
        """格式化为邮件内容"""
        lines = []

        lines.append(f"主题: {self.post_title}")
        lines.append("=" * 60)
        lines.append("")
        lines.append(f"供应商: {self.provider.name}")
        if self.provider.website:
            lines.append(f"官网: {self.provider.website}")
        lines.append("")

        if self.summary:
            lines.append("活动摘要:")
            lines.append(self.summary)
            lines.append("")

        # 全局优惠
        if self.global_promo_code or self.global_discount_info:
            lines.append("全局优惠信息:")
            if self.global_promo_code:
                lines.append(f"  优惠码: {self.global_promo_code}")
            if self.global_discount_info:
                lines.append(f"  优惠详情: {self.global_discount_info}")
            if self.promotion_end_date:
                lines.append(f"  截止时间: {self.promotion_end_date}")
            lines.append("")

        # 产品详情
        lines.append("产品详情:")
        lines.append("-" * 40)

        for i, product in enumerate(self.products, 1):
            lines.append(f"{i}. {product.title}")
            lines.append(f"   CPU: {product.specs.cpu or 'N/A'}")
            lines.append(f"   内存: {product.specs.ram or 'N/A'}")
            lines.append(f"   存储: {product.specs.storage or 'N/A'}")
            lines.append(f"   带宽: {product.specs.bandwidth or 'N/A'}")
            lines.append(f"   位置: {product.location.city or 'N/A'}, {product.location.country or 'N/A'}")
            lines.append(f"   价格: ${product.pricing.price or 'N/A'}/{product.pricing.billing_cycle or 'month'}")
            if product.promo_code:
                lines.append(f"   优惠码: {product.promo_code}")
            if product.order_url:
                lines.append(f"   订购链接: {product.order_url}")
            if product.availability:
                lines.append(f"   库存状态: {product.availability}")
            lines.append("")

        if self.post_url:
            lines.append(f"原帖链接: {self.post_url}")

        return "\n".join(lines)

    def format_compact(self) -> str:
        """紧凑格式，适合短信等字符限制严格的场景"""
        lines = []

        lines.append(f"🎯 {self.provider.name} - {len(self.products)}个产品")

        if self.global_promo_code:
            lines.append(f"🎫 全局码: {self.global_promo_code}")

        # 只显示前3个最便宜的产品
        sorted_products = sorted(
            [p for p in self.products if p.pricing.price], key=lambda x: x.pricing.price or float("inf")
        )[:3]

        for product in sorted_products:
            price = f"${product.pricing.price}/{product.pricing.billing_cycle or 'mo'}"
            location = product.location.city or "N/A"
            lines.append(f"📦 {product.title[:30]}... {price} {location}")
            if product.promo_code:
                lines.append(f"   🎫 {product.promo_code}")

        if self.post_url:
            lines.append(f"🔗 {self.post_url}")

        return "\n".join(lines)
