#!/usr/bin/env python3
"""
LowEndTalk 帖子结构化数据提取
基于 pydantic-ai 实现服务器产品信息的自动提取和结构化
"""

import os

from bs4 import BeautifulSoup
import logfire
from pydantic_ai import Agent

from .models import LETPostAnalysis

logfire.configure(send_to_logfire="if-token-present")
logfire.instrument_pydantic_ai()


# 全局变量用于缓存 Agent 实例
_let_analyzer: Agent[any, LETPostAnalysis] | None = None


def get_let_analyzer() -> Agent[any, LETPostAnalysis]:
    """获取或创建 LET 分析器实例"""
    with logfire.span("get_let_analyzer") as span:
        global _let_analyzer
        if _let_analyzer is None:
            logfire.info("🔧 创建新的LET分析器实例")

            # 设置模型
            model = os.getenv("PYDANTIC_AI_MODEL", "openai:gpt-4o")
            span.set_attribute("model", model)
            logfire.info(f"🤖 使用AI模型: {model}")

            if not model.startswith(("openai:", "anthropic:", "gemini:")):
                logfire.warn(f"⚠️ 使用非标准模型 {model}，请确保已正确配置")

            # 创建 Agent
            logfire.info("⚙️ 初始化AI Agent...")
            _let_analyzer = Agent(
                model=model,
                output_type=LETPostAnalysis,
                system_prompt="""
                你是一个专门分析 LowEndTalk (LET) 论坛服务器优惠帖子的专家。

                数据结构说明：
                - 一个帖子通常只对应一个供应商（provider），所有产品来自同一供应商
                - 供应商信息只需在帖子级别填写一次，避免重复
                - 每个产品包含独立的规格、价格、位置等信息
                - 全局优惠信息适用于所有产品的通用优惠码或促销

                你的任务是提取：
                1. 帖子基本信息（标题、发布日期、链接）
                2. 供应商信息（名称、网站、联系方式）
                3. 产品列表，每个产品包含：
                   - 产品名称和规格（CPU、内存、存储、带宽等）
                   - 价格信息（价格、计费周期、优惠信息）
                   - 地理位置（国家、城市、数据中心）
                   - 产品特性和可用性
                   - 订购链接和产品专属优惠码
                4. 全局优惠信息（适用于所有产品的通用优惠）

                注意事项：
                1. 仔细识别每个独立的产品或套餐
                2. 准确提取数值信息（价格、规格等）
                3. 识别优惠代码和有效期（区分产品专属和全局优惠）
                4. 区分不同的计费周期
                5. 提取关键特性和卖点
                6. 提取产品直达链接（特别重要）
                7. 供应商信息只需填写一次，不要在每个产品中重复
                8. 尽量使用中文描述，除非是一些地名，或者一些专有名词
                """,
            )
            logfire.info("✅ LET分析器创建完成")
            span.set_attribute("created_new_instance", True)
        else:
            logfire.info("♻️ 使用现有的LET分析器实例")
            span.set_attribute("created_new_instance", False)

        return _let_analyzer


def extract_discussion_content(html_content: str) -> str:
    """
    从 HTML 内容中提取 MessageList Discussion 部分的内容，保留结构化信息

    Args:
        html_content: 完整的 HTML 内容

    Returns:
        str: 提取的讨论内容（保留HTML结构或转换为XML格式）
    """
    with logfire.span("extract_discussion_content") as span:
        span.set_attribute("input_length", len(html_content))
        logfire.info(f"🔍 开始提取HTML内容，原始长度: {len(html_content)} 字符")

        try:
            soup = BeautifulSoup(html_content, "html.parser")
            logfire.info("✅ HTML解析成功，开始查找讨论区域")

            # 查找 class="MessageList Discussion" 的元素
            discussion_element = soup.find(class_="MessageList Discussion")

            if discussion_element:
                logfire.info("🎯 找到 'MessageList Discussion' 元素")
                # 直接保留HTML结构信息，包含所有属性和链接
                structured_content = str(discussion_element)
                span.set_attribute("extraction_method", "primary_selector_html")
                span.set_attribute("output_length", len(structured_content))
                logfire.info(f"✅ 成功提取结构化讨论内容，长度: {len(structured_content)} 字符")
                logfire.info("🏗️ 保留了完整的HTML结构信息（链接、属性、格式等）")
                return structured_content
            else:
                logfire.warn("⚠️ 未找到 'MessageList Discussion' 元素，尝试后备选择器")
                # 如果没有找到指定的class，尝试其他常见的讨论区域标识
                fallback_selectors = [
                    ".MessageList",
                    ".Discussion",
                    ".forum-content",
                    ".post-content",
                    ".message-content",
                    '[class*="message"]',
                    '[class*="discussion"]',
                ]

                for i, selector in enumerate(fallback_selectors):
                    logfire.info(f"🔍 尝试后备选择器 {i + 1}/{len(fallback_selectors)}: {selector}")
                    element = soup.select_one(selector)
                    if element:
                        # 直接保留HTML结构信息
                        structured_content = str(element)
                        span.set_attribute("extraction_method", f"fallback_{selector}_html")
                        span.set_attribute("output_length", len(structured_content))
                        logfire.info(
                            f"✅ 使用选择器 {selector} 成功提取结构化内容，长度: {len(structured_content)} 字符"
                        )
                        logfire.info("🏗️ 保留了完整的HTML结构信息")
                        return structured_content

                # 如果都没找到，返回完整的body内容
                logfire.warn("⚠️ 所有选择器都失败，尝试提取body内容")
                body = soup.find("body")
                if body:
                    structured_content = str(body)
                    span.set_attribute("extraction_method", "body_fallback_html")
                    span.set_attribute("output_length", len(structured_content))
                    logfire.info(f"✅ 使用body标签提取结构化内容，长度: {len(structured_content)} 字符")
                    logfire.info("🏗️ 保留了完整的HTML结构信息")
                    return structured_content
                else:
                    logfire.warn("⚠️ 无法找到body标签，返回原始内容")
                    span.set_attribute("extraction_method", "no_extraction")
                    span.set_attribute("output_length", len(html_content))
                    return html_content

        except Exception as e:
            logfire.error(f"❌ HTML解析出错: {e}", exc_info=True)
            span.set_attribute("error", str(e))
            span.set_attribute("extraction_method", "error_fallback")
            # 解析失败时返回原始内容
            return html_content


def analyze_let_post(post_content: str, post_url: str | None = None) -> LETPostAnalysis:
    """
    分析 LowEndTalk 帖子内容并提取结构化数据

    Args:
        post_content: 帖子内容文本或HTML内容
        post_url: 帖子链接（可选）

    Returns:
        LETPostAnalysis: 结构化的分析结果
    """
    with logfire.span("analyze_let_post") as span:
        span.set_attribute("post_url", post_url or "N/A")
        span.set_attribute("input_length", len(post_content))
        logfire.info(f"🚀 开始分析LET帖子，输入长度: {len(post_content)} 字符")

        try:
            # 如果输入看起来像HTML，先提取讨论内容
            is_html = "<" in post_content and ">" in post_content
            span.set_attribute("is_html_input", is_html)

            if is_html:
                logfire.info("📄 检测到HTML内容，开始提取讨论区域")
                extracted_content = extract_discussion_content(post_content)
                span.set_attribute("extracted_length", len(extracted_content))
                reduction_ratio = (1 - len(extracted_content) / len(post_content)) * 100
                logfire.info(
                    f"✅ 内容提取完成，压缩率: {reduction_ratio:.1f}% (从 {len(post_content)} 到 {len(extracted_content)} 字符)"
                )
            else:
                logfire.info("📝 检测到纯文本内容，直接使用")
                extracted_content = post_content
                span.set_attribute("extracted_length", len(extracted_content))

            # 构建完整的提示
            logfire.info("🤖 构建AI分析提示")
            if is_html:
                prompt = f"""
                请分析以下 LowEndTalk 帖子内容，使用优化后的数据结构提取信息。

                帖子链接：{post_url or "N/A"}

                重要说明：
                1. 一个帖子通常只有一个供应商，请在帖子级别提取供应商信息
                2. 所有产品都来自同一供应商，产品中不需要重复供应商信息
                3. 区分产品专属优惠码和全局优惠码

                请从HTML内容中提取：

                1. 帖子基本信息：
                   - 帖子标题、发布日期

                2. 供应商信息（整个帖子级别，只需填写一次）：
                   - 供应商名称、官网、联系方式

                3. 产品列表（每个产品包含）：
                   - 产品名称和规格（CPU、内存、存储、带宽等）
                   - 价格信息（价格、计费周期、产品特定优惠）
                   - 地理位置（国家、城市、数据中心）
                   - 产品特性、可用性状态
                   - 订购链接（**极其重要！**从<a>标签的href属性提取完整URL）
                   - 产品专属优惠码

                4. 全局优惠信息：
                   - 适用于所有产品的通用优惠码
                   - 全局促销信息和结束日期

                HTML帖子内容：
                {extracted_content}
                """
            else:
                prompt = f"""
                请分析以下 LowEndTalk 帖子内容，提取所有服务器产品的详细信息：

                帖子链接：{post_url or "N/A"}

                帖子内容：
                {extracted_content}

                请仔细提取每个产品的完整信息，包括规格、价格、位置等所有可用细节，不要遗漏任何信息，尤其是每个产品的直达链接。
                """

            span.set_attribute("prompt_length", len(prompt))
            logfire.info(f"📝 提示构建完成，总长度: {len(prompt)} 字符")

            logfire.info("🔄 调用AI分析器...")
            analyzer = get_let_analyzer()

            with logfire.span("ai_analysis") as ai_span:
                result = analyzer.run_sync(prompt)
                ai_span.set_attribute("products_found", len(result.output.products))
                ai_span.set_attribute("is_offer", result.output.is_offer)
                ai_span.set_attribute("post_title", result.output.post_title)

            logfire.info("✅ AI分析完成！")
            logfire.info(
                f"📊 分析结果: 标题='{result.output.post_title}', 产品数量={len(result.output.products)}, 是否优惠={result.output.is_offer}"
            )

            # 详细记录每个产品
            for i, product in enumerate(result.output.products):
                with logfire.span(f"product_{i + 1}") as product_span:
                    product_span.set_attribute("title", product.title)
                    product_span.set_attribute("provider", result.output.provider.name)
                    product_span.set_attribute("price", product.pricing.price)
                    product_span.set_attribute("currency", product.pricing.currency)
                    product_span.set_attribute("location", f"{product.location.city}, {product.location.country}")
                    logfire.info(
                        f"📦 产品 {i + 1}: {product.title} - ${product.pricing.price} - {result.output.provider.name}"
                    )

            span.set_attribute("success", True)
            span.set_attribute("products_count", len(result.output.products))
            return result.output

        except Exception as e:
            logfire.error(f"❌ 分析帖子时出错: {e}", exc_info=True)
            span.set_attribute("success", False)
            span.set_attribute("error", str(e))

            # 返回默认结构
            from .models import ProviderInfo

            fallback_result = LETPostAnalysis(
                post_title="解析失败",
                post_url=post_url,
                post_date=None,
                provider=ProviderInfo(name="未知供应商"),
                products=[],
                is_offer=False,
                tags=None,
                summary=f"解析失败: {e!s}",
            )
            logfire.info("🔄 返回默认错误结构")
            return fallback_result
