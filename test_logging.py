#!/usr/bin/env python3
"""
统一日志系统测试脚本
"""

import asyncio
import os
from pathlib import Path

# 测试统一日志系统
from utils.logging_config import get_logger, setup_logging


def test_basic_logging():
    """测试基本日志功能"""
    print("🧪 测试基本日志功能")
    
    # 获取日志器
    logger = get_logger("test_basic")
    
    # 测试各种日志级别
    logger.debug("这是一条调试信息")
    logger.info("这是一条信息")
    logger.warning("这是一条警告")
    logger.error("这是一条错误")
    logger.critical("这是一条严重错误")
    
    print("✅ 基本日志功能测试完成")


def test_module_loggers():
    """测试不同模块的日志器"""
    print("\n🧪 测试不同模块的日志器")
    
    # 模拟不同模块的日志器
    whmcs_logger = get_logger("whmcs.core.WHMCSAuto")
    stock_logger = get_logger("whmcs.stock_monitor.AsyncStockMonitor")
    let_logger = get_logger("let.scheduler.LETScheduler")
    api_logger = get_logger("api_server")
    
    # 测试日志输出
    whmcs_logger.info("WHMCS 核心模块日志测试")
    stock_logger.info("库存监控模块日志测试")
    let_logger.info("LET 调度器模块日志测试")
    api_logger.info("API 服务器模块日志测试")
    
    print("✅ 不同模块日志器测试完成")


def test_log_levels():
    """测试不同日志级别"""
    print("\n🧪 测试不同日志级别")
    
    # 测试默认级别
    logger = get_logger("test_levels")
    logger.info("当前日志级别测试")
    
    # 测试设置不同级别
    print("设置日志级别为 DEBUG")
    setup_logging("DEBUG")
    debug_logger = get_logger("test_debug")
    debug_logger.debug("这条调试信息现在应该可见")
    debug_logger.info("这是信息级别")
    
    print("设置日志级别为 WARNING")
    setup_logging("WARNING")
    warning_logger = get_logger("test_warning")
    warning_logger.info("这条信息应该不可见")
    warning_logger.warning("这条警告应该可见")
    
    # 恢复默认级别
    setup_logging("INFO")
    print("✅ 日志级别测试完成")


def test_log_file():
    """测试日志文件写入"""
    print("\n🧪 测试日志文件写入")
    
    log_file = Path("data/logs/whmcs_auto.log")
    
    # 记录一些测试日志
    logger = get_logger("test_file")
    logger.info("测试日志文件写入 - 这条消息应该写入文件")
    logger.error("测试错误日志写入")
    
    # 检查文件是否存在
    if log_file.exists():
        print(f"✅ 日志文件已创建: {log_file}")
        
        # 读取最后几行
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            if lines:
                print("📄 日志文件最后几行:")
                for line in lines[-3:]:
                    print(f"   {line.strip()}")
            else:
                print("⚠️ 日志文件为空")
    else:
        print("❌ 日志文件未创建")
    
    print("✅ 日志文件测试完成")


async def test_async_logging():
    """测试异步环境下的日志"""
    print("\n🧪 测试异步环境下的日志")
    
    logger = get_logger("test_async")
    
    async def async_task(task_id: int):
        logger.info(f"异步任务 {task_id} 开始")
        await asyncio.sleep(0.1)
        logger.info(f"异步任务 {task_id} 完成")
    
    # 并发执行多个异步任务
    tasks = [async_task(i) for i in range(3)]
    await asyncio.gather(*tasks)
    
    print("✅ 异步日志测试完成")


def test_import_modules():
    """测试导入使用日志的模块"""
    print("\n🧪 测试导入使用日志的模块")
    
    try:
        # 测试导入 WHMCS 核心模块
        from whmcs.core import WHMCSAuto
        print("✅ WHMCS 核心模块导入成功")
        
        # 测试导入库存监控模块
        from whmcs.stock_monitor import AsyncStockMonitor
        print("✅ 库存监控模块导入成功")
        
        # 测试导入 CFBypass 客户端
        from whmcs.cfbypass_client import CFBypassClient
        print("✅ CFBypass 客户端模块导入成功")
        
        # 测试导入 LET 调度器
        from let.scheduler import LETScheduler
        print("✅ LET 调度器模块导入成功")
        
        # 测试导入通知服务
        from advanced_notification_service import AdvancedNotificationService
        print("✅ 通知服务模块导入成功")
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    
    print("✅ 所有模块导入测试完成")
    return True


async def main():
    """主测试函数"""
    print("🚀 开始统一日志系统测试")
    print("=" * 50)
    
    # 设置测试环境
    os.environ.setdefault("LOG_LEVEL", "INFO")
    
    # 初始化日志系统
    setup_logging()
    
    # 运行测试
    test_basic_logging()
    test_module_loggers()
    test_log_levels()
    test_log_file()
    await test_async_logging()
    
    # 测试模块导入
    import_success = test_import_modules()
    
    print("\n" + "=" * 50)
    if import_success:
        print("🎉 所有测试通过！统一日志系统工作正常")
        print("\n📋 统一日志系统特性:")
        print("   ✅ 单例模式，避免重复配置")
        print("   ✅ 自动创建日志目录")
        print("   ✅ 文件和控制台双重输出")
        print("   ✅ 带颜色的控制台输出")
        print("   ✅ 轮转日志文件管理")
        print("   ✅ 第三方库日志级别控制")
        print("   ✅ 支持环境变量配置")
        print("   ✅ 异步环境兼容")
        
        print("\n🔧 使用方法:")
        print("   from utils.logging_config import get_logger")
        print("   logger = get_logger(__name__)")
        print("   logger.info('你的日志消息')")
        
        return True
    else:
        print("💥 部分测试失败，请检查模块导入问题")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
